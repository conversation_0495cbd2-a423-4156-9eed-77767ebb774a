import request from '@/utils/request'

/**
 * 产品管理API
 */

// 获取产品列表
export function getProductListApi(params) {
  return request({
    url: '/management/products',
    method: 'get',
    params
  })
}

// 获取产品详情
export function getProductDetailApi(id) {
  return request({
    url: `/management/products/${id}`,
    method: 'get'
  })
}

// 创建产品
export function createProductApi(data) {
  return request({
    url: '/management/products',
    method: 'post',
    data
  })
}

// 更新产品
export function updateProductApi(data) {
  return request({
    url: '/management/products',
    method: 'put',
    data
  })
}

// 删除产品
export function deleteProductApi(ids) {
  return request({
    url: `/management/products/${ids}`,
    method: 'delete'
  })
}

// 上架产品
export function enableProductApi(id) {
  return request({
    url: `/management/products/${id}/enable`,
    method: 'put'
  })
}

// 下架产品
export function disableProductApi(id) {
  return request({
    url: `/management/products/${id}/disable`,
    method: 'put'
  })
}

// 获取所有上架的产品列表（用于下单选择）
export function getAvailableProductsApi(data) {
  return request({
    url: '/management/products/available/list',
    method: 'post',
    data
  })
}

// 根据类型查询产品列表
export function getProductsByTypeApi(type) {
  return request({
    url: `/management/products/type/${type}`,
    method: 'get'
  })
}

// 根据学科查询产品列表
export function getProductsBySubjectApi(subject) {
  return request({
    url: `/management/products/subject/${subject}`,
    method: 'get'
  })
}

// 根据标签查询产品列表
export function getProductsByTagApi(tag) {
  return request({
    url: `/management/products/tag/${tag}`,
    method: 'get'
  })
}

// 查询热门产品列表
export function getHotProductsApi(limit = 10) {
  return request({
    url: '/management/products/hot',
    method: 'get',
    params: { limit }
  })
}

// 查询产品统计信息
export function getProductStatisticsApi() {
  return request({
    url: '/management/products/statistics',
    method: 'get'
  })
}

// 更新产品库存
export function updateProductStockApi(id, quantity) {
  return request({
    url: `/management/products/${id}/stock`,
    method: 'put',
    params: { quantity }
  })
}

// 检查产品库存
export function checkProductStockApi(id, quantity) {
  return request({
    url: `/management/products/${id}/stock/check`,
    method: 'get',
    params: { quantity }
  })
}

// Mock数据（备用，已注释）
/*
const mockProducts = [
  {
    id: '1',
    name: '小学英语单词课程包',
    description: '适合小学生的英语单词学习课程，包含基础词汇和常用句型',
    type: '课程包',
    subject: '英语',
    specification: '单词课',
    grade: '小学1-6年级',
    price: 299900,
    hours: 20.0,
    status: '上架',
    coverImage: 'https://via.placeholder.com/300x200?text=英语课程',
    sortOrder: 1,
    createTime: '2025-08-05 10:00:00',
    updateTime: '2025-08-05 10:00:00'
  },
  {
    id: '2',
    name: '初中数学基础课程',
    description: '初中数学基础知识点讲解，适合初一初二学生',
    type: '课程包',
    subject: '数学',
    specification: '基础课',
    grade: '初中1-2年级',
    price: 399900,
    hours: 30.0,
    status: '上架',
    coverImage: 'https://via.placeholder.com/300x200?text=数学课程',
    sortOrder: 2,
    createTime: '2025-08-05 10:30:00',
    updateTime: '2025-08-05 10:30:00'
  },
  {
    id: '3',
    name: '高中语文阅读理解专项',
    description: '高中语文阅读理解技巧训练，提升阅读能力',
    type: '课程包',
    subject: '语文',
    specification: '阅读课',
    grade: '高中1-3年级',
    price: 499900,
    hours: 25.0,
    status: '上架',
    coverImage: 'https://via.placeholder.com/300x200?text=语文课程',
    sortOrder: 3,
    createTime: '2025-08-05 11:00:00',
    updateTime: '2025-08-05 11:00:00'
  },
  {
    id: '4',
    name: '英语语法单次课程',
    description: '英语语法专项讲解，单次课程',
    type: '单次课程',
    subject: '英语',
    specification: '语法课',
    grade: '初中1-3年级',
    price: 9900,
    hours: 2.0,
    status: '下架',
    coverImage: 'https://via.placeholder.com/300x200?text=语法课程',
    sortOrder: 4,
    createTime: '2025-08-05 11:30:00',
    updateTime: '2025-08-05 11:30:00'
  },
  {
    id: '5',
    name: '数学教材配套练习册',
    description: '数学教材配套的练习册，包含详细解答',
    type: '教材',
    subject: '数学',
    specification: '练习册',
    grade: '小学3-6年级',
    price: 5900,
    hours: null,
    status: '上架',
    coverImage: 'https://via.placeholder.com/300x200?text=练习册',
    sortOrder: 5,
    createTime: '2025-08-05 12:00:00',
    updateTime: '2025-08-05 12:00:00'
  }
]
*/
