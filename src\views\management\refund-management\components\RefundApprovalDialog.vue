<template>
  <el-dialog v-model="visible" :title="title" width="900px" :close-on-click-modal="false">
    <el-alert
      v-if="result === 'approved'"
      title="审批通过后将自动提交退款到支付平台，请确认."
      type="warning"
      :closable="false"
      show-icon
      class="mb-2"
    />
    
    <!-- 订单课时信息 -->
    <div v-if="refundDetail && result === 'approved'" class="order-info-section">
      <h4 class="section-title">订单课时信息</h4>
      <el-descriptions :column="3" border class="mb-3">
        <el-descriptions-item label="产品名称">
          {{ orderInfo.productName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="学科">
          {{ orderInfo.subject || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="课型">
          {{ orderInfo.courseType || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="课时">
          <span class="highlight-text">{{ orderInfo.quantity || 0 }}课时</span>
        </el-descriptions-item>
        <el-descriptions-item label="赠送课时">
          <span class="highlight-text">{{ orderInfo.bonusHoursQuantity || 0 }}课时</span>
        </el-descriptions-item>
        <el-descriptions-item label="总课时">
          <span class="highlight-text total-hours">{{ (orderInfo.quantity || 0) + (orderInfo.bonusHoursQuantity || 0) }}课时</span>
        </el-descriptions-item>
        <el-descriptions-item label="单价">
          ¥{{ ((orderInfo.unitPrice || 0) / 100).toFixed(2) }}/课时
        </el-descriptions-item>
        <el-descriptions-item label="教材费用">
          <span v-if="orderInfo.hasMaterialFee" class="material-fee">
            ¥{{ ((orderInfo.materialFee || 0) / 100).toFixed(2) }}
          </span>
          <span v-else class="no-material-fee">无教材费</span>
        </el-descriptions-item>
        <el-descriptions-item label="订单总额">
          <span class="total-amount">¥{{ ((orderInfo.totalAmount || 0) / 100).toFixed(2) }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-if="refundDetail && result === 'approved'" class="refund-details-section">
      <h4 class="section-title">申请退款原因</h4>
      {{ refundDetail.refundReasonType }}<span v-if="refundDetail.refundReason">（{{ refundDetail.refundReason }}）</span>
    </div>
    
    <!-- 退款流水详情 -->
    <div v-if="refundDetail && result === 'approved'" class="refund-details-section">
      <h4 class="section-title">退款流水详情</h4>
      <el-table :data="refundTrxs" border style="width: 100%" class="mb-3">
        <el-table-column prop="originalTrxId" label="原交易流水ID" min-width="150" />
        <el-table-column prop="payType" label="支付方式" width="100">
          <template #default="{ row }">
            <el-tag :type="getPayTypeTag(row.payType)">{{ getPayTypeName(row.payType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="originalAmount" label="原支付金额(元)" width="120">
          <template #default="{ row }">
            ¥{{ (row.originalAmount / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="refundAmount" label="退款金额(元)" width="150">
          <template #default="{ row, $index }">
            <el-input-number
              v-model="row.refundAmountYuan"
              :min="0"
              :max="row.originalAmountYuan"
              :precision="2"
              :step="1"
              size="small"
              style="width: 120px"
              @change="updateTotalRefund"
            />
          </template>
        </el-table-column>
          <el-table-column label="消耗课时" prop="trxAmt" width="120" align="center">
            <template #default="{ row }">
              {{ row.calcCourseHours?.consumedFromThisPayment }}
            </template>
          </el-table-column>
          <el-table-column label="退款课时" prop="trxAmt" width="120" align="center">
            <template #default="{ row }">
              {{ row.calcCourseHours?.refundableHours }}
            </template>
          </el-table-column>
        <el-table-column prop="refundStatus" label="退款状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getRefundStatusTag(row.refundStatus)">{{ getRefundStatusName(row.refundStatus) }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 总退款金额 -->
      <div class="total-refund">
        <el-form-item label="总退款金额">
          <el-input
            :value="`¥${(totalRefundAmount / 100).toFixed(2)}`"
            disabled
            style="width: 200px"
          />
        </el-form-item>
      </div>
    </div>

    <el-form ref="formRef" :model="form" :rules="rules" label-width="90px">
      <el-form-item label="审批结果">
        <el-tag :type="result === 'approved' ? 'success' : 'danger'">
          {{ result === 'approved' ? '通过' : '拒绝' }}
        </el-tag>
      </el-form-item>
      <el-form-item label="审批意见" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="4"
          maxlength="200"
          show-word-limit
          placeholder="请输入审批意见"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" :loading="submitting" @click="onSubmit" v-hasPermi="['refund:records:approve']">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox, FormInstance, FormRules } from 'element-plus'
import { approveRefundRecord, getRefundRecordDetail } from '@/api/management/refund-records'
import { getOrderDetailApi } from '@/api/management/order'

interface RefundTrx {
  originalTrxId: string
  refundTrxId: string
  refundType: string
  refundAmount: number
  originalAmount: number
  refundStatus: string
  payType: string
}

interface RefundDetail {
  id: string
  refundNo: string
  refundAmount: number
  refundReasonType: string
  refundReason: string
  refundStatus: string
  orderId: string
  refundTrxs: RefundTrx[]
}

interface OrderInfo {
  productName: string
  subject: string
  courseType: string
  quantity: number
  bonusHoursQuantity: number
  unitPrice: number
  hasMaterialFee: boolean
  materialFee: number
  totalAmount: number
}

const props = defineProps<{ 
  modelValue: boolean
  refundId?: string
  result: 'approved' | 'rejected' | ''
}>()
const emit = defineEmits<{ 
  (e: 'update:modelValue', v: boolean): void
  (e: 'success'): void 
}>()

const visible = ref<boolean>(props.modelValue)
const refundDetail = ref<RefundDetail | null>(null)
const orderInfo = ref<OrderInfo>({
  productName: '',
  subject: '',
  courseType: '',
  quantity: 0,
  bonusHoursQuantity: 0,
  unitPrice: 0,
  hasMaterialFee: false,
  materialFee: 0,
  totalAmount: 0
})
const refundTrxs = ref<RefundTrx[]>([])
const loading = ref(false)

// 添加表单对象定义
const form = ref({
  remark: ''
})

// 添加表单引用
const formRef = ref<FormInstance>()

// 添加提交状态
const submitting = ref(false)

// 添加表单验证规则
const rules: FormRules = {
  remark: [
    { required: true, message: '请输入审批意见', trigger: 'blur' },
    { min: 1, max: 200, message: '审批意见长度在 1 到 200 个字符', trigger: 'blur' }
  ]
}

watch(
  () => props.modelValue,
  (v) => {
    visible.value = v
    if (v && props.refundId && props.result === 'approved') {
      fetchRefundDetail()
    }
    // 重置表单
    if (v) {
      form.value.remark = ''
    }
  }
)
watch(visible, (v) => emit('update:modelValue', v))

const title = computed(() => (props.result === 'approved' ? '退款审批 - 通过' : '退款审批 - 拒绝'))

// 获取退款记录详情
const fetchRefundDetail = async () => {
  if (!props.refundId) return
  
  try {
    loading.value = true
    const res: any = await getRefundRecordDetail(props.refundId)
    if (res.code === 200) {
      refundDetail.value = res.data
      // 初始化退款流水数据，转换单位为元显示
      refundTrxs.value = (res.data.refundTrxs || []).map((trx: any) => ({
        ...trx,
        originalAmount: trx.refundAmount || 0, // 原始金额（分）
        originalAmountYuan: ((trx.refundAmount || 0) / 100), // 原始金额（元）
        refundAmount: trx.refundAmount || 0, // 退款金额（分）
        refundAmountYuan: ((trx.refundAmount || 0) / 100) // 退款金额（元）
      }))
      
      // 获取订单详情信息
      if (res.data.orderId) {
        await fetchOrderDetail(res.data.orderId)
      }
    } else {
      ElMessage.error(res.msg || '获取退款详情失败')
    }
  } catch (e) {
    console.error(e)
    ElMessage.error('获取退款详情失败')
  } finally {
    loading.value = false
  }
}

// 计算总退款金额（分）
const totalRefundAmount = computed(() => {
  return refundTrxs.value.reduce((sum, trx) => {
    // 将元转换为分进行计算
    const amountInCents = Math.round((trx.refundAmountYuan || 0) * 100)
    return sum + amountInCents
  }, 0)
})

// 更新总退款金额
const updateTotalRefund = () => {
  // 同步更新分单位的退款金额
  refundTrxs.value.forEach(trx => {
    trx.refundAmount = Math.round((trx.refundAmountYuan || 0) * 100)
  })
}

const onSubmit = async () => {
  if (!props.refundId || !props.result) return
  await formRef.value?.validate(async (valid) => {
    if (!valid) return
    try {
      await ElMessageBox.confirm('确认提交该审批操作？', '提示', { type: 'warning' })
      submitting.value = true
      
      const payload: any = {
        refundIds: [props.refundId],
        approveResult: props.result === 'approved' ? '审批通过' : '审批拒绝',
        approveDesc: form.value.remark
      }
      
      // 如果是审批通过且有退款流水调整，包含调整后的数据（转换为分）
      if (props.result === 'approved' && refundTrxs.value.length > 0) {
        payload.refundTrxs = refundTrxs.value.map(trx => ({
          originalTrxId: trx.originalTrxId,
          refundTrxId: trx.refundTrxId,
          refundAmount: Math.round((trx.refundAmountYuan || 0) * 100) // 转换为分
        }))
        payload.totalRefundAmount = totalRefundAmount.value // 已经是分单位
      }
      
      const res: any = await approveRefundRecord(payload)
      if (res.code === 200) {
        ElMessage.success('审批成功')
        emit('success')
        visible.value = false
      } else {
        ElMessage.error(res.msg || '审批失败')
      }
    } catch (e: any) {
      if (e !== 'cancel') {
        console.error(e)
        ElMessage.error('审批失败')
      }
    } finally {
      submitting.value = false
    }
  })
}

// 获取订单详情
const fetchOrderDetail = async (orderId: string) => {
  try {
    const res: any = await getOrderDetailApi(orderId)
    if (res.code === 200 && res.data) {
      const orderData = res.data
      // 从订单的originOrder中获取产品信息
      debugger
      const product = orderData.products || {}
      
      orderInfo.value = {
        productName: product.name || orderData.body || '',
        subject: product.subject || '',
        courseType: product.courseType || '',
        quantity: product.quantity || 0,
        bonusHoursQuantity: product.bonusHoursQuantity || 0,
        unitPrice: product.unitPrice || 0,
        hasMaterialFee: product.hasMaterialFee || false,
        materialFee: product.materialFee || 0,
        totalAmount: orderData.totalAmt || 0
      }
    }
  } catch (e) {
    console.error('获取订单详情失败:', e)
  }
}

// 获取支付方式标签类型
const getPayTypeTag = (payType: string) => {
  const tagMap: Record<string, string> = {
    'ALIPAY': 'primary',
    'WECHAT': 'success',
    'BANK': 'warning',
    'CASH': 'info'
  }
  return tagMap[payType] || 'info'
}

// 获取支付方式名称
const getPayTypeName = (payType: string) => {
  const nameMap: Record<string, string> = {
    'ALIPAY': '支付宝',
    'WECHAT': '微信',
    'BANK': '银行卡',
    'CASH': '现金'
  }
  return nameMap[payType] || payType
}

// 获取退款状态标签类型
const getRefundStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    'WAIT_APPROVE': 'warning',
    'PROCESSING': 'primary',
    'SUCCESS': 'success',
    'FAILED': 'danger'
  }
  return tagMap[status] || 'info'
}

// 获取退款状态名称
const getRefundStatusName = (status: string) => {
  const nameMap: Record<string, string> = {
    'WAIT_APPROVE': '待审批',
    'PROCESSING': '处理中',
    'SUCCESS': '成功',
    'FAILED': '失败'
  }
  return nameMap[status] || status
}

// const onSubmit = async () => {
//   if (!props.refundId || !props.result) return
//   await formRef.value?.validate(async (valid) => {
//     if (!valid) return
//     try {
//       await ElMessageBox.confirm('确认提交该审批操作？', '提示', { type: 'warning' })
//       submitting.value = true
//
//       const payload: any = {
//         refundIds: [props.refundId],
//         approveResult: props.result === 'approved' ? '审批通过' : '审批拒绝',
//         approveDesc: form.value.remark
//       }
//
//       // 如果是审批通过且有退款流水调整，包含调整后的数据
//       if (props.result === 'approved' && refundTrxs.value.length > 0) {
//         payload.refundTrxs = refundTrxs.value.map(trx => ({
//           originalTrxId: trx.originalTrxId,
//           refundTrxId: trx.refundTrxId,
//           refundAmount: trx.refundAmount
//         }))
//         payload.totalRefundAmount = totalRefundAmount.value
//       }
//
//       const res: any = await approveRefundRecord(payload)
//       if (res.code === 200) {
//         ElMessage.success('审批成功')
//         emit('success')
//         visible.value = false
//       } else {
//         ElMessage.error(res.msg || '审批失败')
//       }
//     } catch (e: any) {
//       if (e !== 'cancel') {
//         console.error(e)
//         ElMessage.error('审批失败')
//       }
//     } finally {
//       submitting.value = false
//     }
//   })
// }
</script>

<style scoped>
.mb-2 { margin-bottom: 12px; }
.mb-3 { margin-bottom: 16px; }

.order-info-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border-left: 4px solid #3b82f6;
}

.refund-details-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.highlight-text {
  font-weight: 600;
  color: #409eff;
}

.total-hours {
  color: #67c23a;
  font-size: 16px;
}

.material-fee {
  color: #e6a23c;
  font-weight: 600;
}

.no-material-fee {
  color: #909399;
}

.total-amount {
  color: #f56c6c;
  font-weight: 600;
  font-size: 16px;
}

.total-refund {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #ebeef5;
}

.total-refund .el-form-item {
  margin-bottom: 0;
}
</style>

