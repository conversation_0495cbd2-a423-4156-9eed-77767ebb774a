<template>
  <el-dialog
    :model-value="modelValue"
    title="销售人员详情"
    width="800px"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <div v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="姓名">
          {{ staffDetail.nickName }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号">
          {{ staffDetail.phonenumber }}
        </el-descriptions-item>
        <el-descriptions-item label="邮箱">
          {{ staffDetail.email }}
        </el-descriptions-item>
        <el-descriptions-item label="性别">
          {{ getSexLabel(staffDetail.sex) }}
        </el-descriptions-item>
        <el-descriptions-item label="角色">
          <el-tag :type="getRoleTagType(staffDetail.roleType)" size="small">
            {{ getRoleLabel(staffDetail.roleType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag
            :type="staffDetail.status === 'active' ? 'success' : 'danger'"
            size="small"
          >
            {{ staffDetail.status === "active" ? "正常" : "停用" }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="所属销售组">
          {{ staffDetail.groupName || "未分配" }}
        </el-descriptions-item>
        <el-descriptions-item label="负责学生数">
          {{ staffDetail.studentCount || 0 }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">
          {{ staffDetail.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          {{ staffDetail.remark || "无" }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 统计信息 -->
      <div class="stats-section" v-if="statsData">
        <h3>统计信息</h3>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-item">
                <div class="stat-value">{{ statsData.totalStudents || 0 }}</div>
                <div class="stat-label">负责学生总数</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-item">
                <div class="stat-value">{{ statsData.activeStudents || 0 }}</div>
                <div class="stat-label">活跃学生数</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-item">
                <div class="stat-value">{{ statsData.monthlyApplications || 0 }}</div>
                <div class="stat-label">本月申请数</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-item">
                <div class="stat-value">{{ statsData.approvalRate || 0 }}%</div>
                <div class="stat-label">申请通过率</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 最近活动 -->
      <div class="activity-section" v-if="recentActivities.length > 0">
        <h3>最近活动</h3>
        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.createTime"
            placement="top"
          >
            <el-card>
              <h4>{{ activity.title }}</h4>
              <p>{{ activity.description }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from "vue";
import { getSalesStaffDetailApi } from "@/api/management/salesStaff";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  staffId: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:modelValue"]);

// 响应式数据
const loading = ref(false);
const staffDetail = ref({});
const statsData = ref(null);
const recentActivities = ref([]);

// 监听对话框打开
watch(
  () => props.modelValue,
  (val) => {
    if (val && props.staffId) {
      loadStaffDetail();
    }
  }
);

// 加载销售人员详情
const loadStaffDetail = async () => {
  loading.value = true;
  try {
    const response = await getSalesStaffDetailApi(props.staffId);
    staffDetail.value = response.data || {};
    statsData.value = response.stats || null;
    recentActivities.value = response.activities || [];
  } catch (error) {
    console.error("获取销售人员详情失败:", error);
  } finally {
    loading.value = false;
  }
};

// 获取性别标签
const getSexLabel = (sex) => {
  const labelMap = {
    0: "男",
    1: "女",
    2: "未知",
  };
  return labelMap[sex] || "未知";
};

// 获取角色标签类型
const getRoleTagType = (roleKey) => {
  const typeMap = {
    leader: "danger",
    member: "info"
  };
  return typeMap[roleKey] || "info";
};

// 获取角色标签
const getRoleLabel = (roleKey) => {
  const labelMap = {
    leader: "组长",
    member: "成员"
  };
  return labelMap[roleKey] || "未知";
};
</script>

<style scoped>
.stats-section {
  margin-top: 30px;
}

.stats-section h3 {
  margin-bottom: 20px;
  color: #303133;
}

.stat-card {
  text-align: center;
}

.stat-item {
  padding: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.activity-section {
  margin-top: 30px;
}

.activity-section h3 {
  margin-bottom: 20px;
  color: #303133;
}

.dialog-footer {
  text-align: right;
}
</style>
