<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑销售组' : '新建销售组'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="组名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入销售组名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="组长" prop="leaderId">
        <el-select
          v-model="formData.leaderId"
          placeholder="请选择组长（可选）"
          style="width: 100%"
          filterable
          clearable
        >
          <el-option
            v-for="leader in leaderOptions"
            :key="leader.id"
            :label="leader.name"
            :value="leader.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="4"
          placeholder="请输入销售组描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="active">正常</el-radio>
          <el-radio label="inactive">停用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  createSalesGroupApi, 
  updateSalesGroupApi,
  getLeaderOptionsApi
} from '@/api/management/salesGroup'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  group: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const leaderOptions = ref([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.group?.id)

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  leaderId: '',
  description: '',
  status: 'active',
  deptId: null
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入销售组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  // leaderId 改为可选，不需要验证规则
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: '',
    name: '',
    leaderId: '',
    description: '',
    status: 'active',
    deptId: null
  })
  formRef.value?.clearValidate()
}

// 监听props变化
watch(() => props.group, (newGroup) => {
  if (newGroup) {
    Object.assign(formData, {
      id: newGroup.id || '',
      name: newGroup.name || '',
      leaderId: newGroup.leaderId || '',
      description: newGroup.description || '',
      status: newGroup.status || 'active',
      deptId: newGroup.deptId || null
    })
  } else {
    resetForm()
  }
}, { immediate: true })

// 页面初始化
onMounted(() => {
  loadLeaderOptions()
})

// 加载组长选项
const loadLeaderOptions = async () => {
  try {
    console.log('开始加载组长选项...')
    const response = await getLeaderOptionsApi()
    console.log('组长选项API响应:', response)

    // 处理响应数据
    const data = response.data || response || []
    console.log('处理后的组长选项数据:', data)

    // 确保数据格式正确 - 后端返回的字段是 name (实际是 salesName)
    leaderOptions.value = Array.isArray(data) ? data.map(item => ({
      id: item.id,
      name: item.name || '未知',  // 后端已经设置为 salesName
      phone: item.phone || ''
    })) : []

    console.log('最终的组长选项:', leaderOptions.value)
  } catch (error) {
    console.error('获取组长选项失败:', error)
    leaderOptions.value = []
  }
}

// 重置表单函数已在上面定义

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    if (isEdit.value) {
      await updateSalesGroupApi(formData)
      ElMessage.success('更新成功')
    } else {
      await createSalesGroupApi(formData)
      ElMessage.success('创建成功')
    }
    
    emit('success')
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
