export interface OrderInfo {
    id: string
    no: string
    orderStatus: string
    totalAmt: number
    amtPaid: number
    amtUnpaid: number
    studentName?: string
    salerName?: string
    signStatus?: string
}

export interface OrderDetailExt extends OrderInfo {
    evaluateRefundAmt: number
}

export interface ProductDetailExt {
    productNo: string
    name: string
    description?: string
    subject: string
    courseType?: string
    applicableGrades: string[]
    unitPrice: number
    quantity: number
    hasBonusHours: boolean
    bonusHoursQuantity: number
    hasMaterialFee: boolean
    materialFee: number
    originalPrice: number
    sellingPrice: number
    status: string
    sortOrder: number
    salesCount: number
    createTime: string
    updateTime: string
}

export interface RefundCalculation {
    totalCourseHours: number
    totalReleasedHours: number
    remainingHours: number
    consumedHours: number
    refundableHours: number
    suggestedRefundAmount: number
    paymentReleaseDetails: any[]
}

export interface RefundDetail {
    orderTrxId: string
    paymentAmount: number
    paymentRatio: number
    releasedPurchasedHours: number
    releasedGiftHours: number
    totalReleasedHours: number
    releaseType: string
    releaseTime: string
    refundable: boolean
    refundableHours: number
    consumedFromThisPayment: number
    remark: string
    actualRefundAmount: number
}