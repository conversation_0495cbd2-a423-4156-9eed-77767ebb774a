<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑学生' : '新增学生'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      @submit.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学生姓名" prop="name">
            <el-input v-model="formData.name" placeholder="请输入学生姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号码" prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入手机号码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-select v-model="formData.gender" placeholder="请选择性别">
              <el-option label="男" value="0" />
              <el-option label="女" value="1" />
              <el-option label="未知" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="年级" prop="grade">
            <el-select v-model="formData.grade" placeholder="请选择年级">
              <el-option label="一年级" value="1" />
              <el-option label="二年级" value="2" />
              <el-option label="三年级" value="3" />
              <el-option label="四年级" value="4" />
              <el-option label="五年级" value="5" />
              <el-option label="六年级" value="6" />
              <el-option label="初一" value="7" />
              <el-option label="初二" value="8" />
              <el-option label="初三" value="9" />
              <el-option label="高一" value="10" />
              <el-option label="高二" value="11" />
              <el-option label="高三" value="12" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学校" prop="school">
            <el-input v-model="formData.school" placeholder="请输入学校名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="班级" prop="className">
            <el-input v-model="formData.className" placeholder="请输入班级" />
          </el-form-item>
        </el-col>
      </el-row>



      <el-row :gutter="24" style="margin-bottom: 5px;">
        <el-col :span="24">
            <el-alert title="注意：家长姓名和手机号需要支付宝实名的姓名及对应的手机号信息。" type="error" :closable="false"/>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="家长姓名" prop="parentName">
            <el-input v-model="formData.parentName" placeholder="请输入家长姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="家长电话" prop="parentPhone">
            <el-input v-model="formData.parentPhone" placeholder="请输入家长电话" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="学习目标" prop="learningGoals">
            <el-input
              v-model="formData.learningGoals"
              type="textarea"
              :rows="3"
              placeholder="请输入学习目标"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { createSalesStudentApi, updateSalesStudentApi } from '@/api/management/salesStudent'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  student: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => {
  return props.student && props.student.id
})

const formData = ref({
  name: '',
  phone: '',
  gender: '2', // 默认为未知
  grade: '',
  school: '',
  className: '',
  parentName: '',
  parentPhone: '',
  learningGoals: '',
  remarks: '',
  status: 'active'
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入学生姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  grade: [{ required: true, message: "请选择年级", trigger: "change" }],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  parentName: [
    { required: true, message: '请输入家长姓名', trigger: 'blur' }
  ],
  parentPhone: [
    { required: true, message: '请输入家长电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的家长电话', trigger: 'blur' }
  ]
}

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  formData.value = {
    name: '',
    phone: '',
    gender: '2', // 默认为未知
    grade: '',
    school: '',
    className: '',
    parentName: '',
    parentPhone: '',
    learningGoals: '',
    remarks: '',
    status: 'active'
  }
}

const loadFormData = () => {
  if (props.student && props.student.id) {
    // 确保性别值正确映射
    let genderValue = String(props.student.gender || '2') // 默认为未知
    // 处理可能的数据类型不匹配
    if (genderValue === 'null' || genderValue === 'undefined' || genderValue === '') {
      genderValue = '2' // 未知
    }

    Object.assign(formData.value, {
      ...props.student,
      // 确保字段为字符串类型，并且值正确
      gender: genderValue,
      grade: String(props.student.grade || '')
    })
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    let success = false
    if (isEdit.value) {
      // 更新时需要包含学生ID
      const updateData = {
        ...formData.value,
        id: props.student.id
      }
      const response = await updateSalesStudentApi(updateData)
      success = response.code === 200
    } else {
      const response = await createSalesStudentApi(formData.value)
      success = response.code === 200
    }

    if (success) {
      ElMessage.success(isEdit.value ? '更新学生信息成功' : '创建学生成功')
      emit('success')
      handleClose()
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(isEdit.value ? '更新学生信息失败' : '创建学生失败')
  } finally {
    submitting.value = false
  }
}

// 监听器
watch(
  () => props.student,
  (newStudent) => {
    if (newStudent && props.modelValue) {
      loadFormData()
    }
  },
  { immediate: true }
)

watch(
  () => props.modelValue,
  (visible) => {
    if (visible) {
      if (props.student) {
        loadFormData()
      }
    } else {
      resetForm()
    }
  }
)
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 600;
}

:deep(.el-select) {
  width: 100%;
}
</style>
