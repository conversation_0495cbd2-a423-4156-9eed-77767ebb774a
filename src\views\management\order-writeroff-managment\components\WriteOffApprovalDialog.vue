<template>
  <el-dialog
    title="核销审核"
    v-model="visible"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="props.writeoffRecord" class="approval-content">
      <!-- 核销信息概要 -->
      <el-card class="info-card" style="margin-bottom: 20px;">
        <template #header>
          <span>核销信息</span>
        </template>
        <el-descriptions :column="1" size="small">
          <el-descriptions-item label="核销订单号">{{ props.writeoffRecord?.woffOrderNo || '-' }}</el-descriptions-item>
          <el-descriptions-item label="核销来源">{{ props.writeoffRecord?.woffOrderSource || '-' }}</el-descriptions-item>
          <el-descriptions-item label="学生姓名">{{ props.writeoffRecord?.studentName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="产品名称">{{ props.writeoffRecord?.productName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="核销状态">{{ props.writeoffRecord?.status || '-' }}</el-descriptions-item>
          <el-descriptions-item label="核销备注">{{ props.writeoffRecord?.remark || '-' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 审核表单 -->
      <el-form
        ref="approvalForm"
        :model="approvalFormData"
        :rules="approvalRules"
        label-width="100px"
      >
        <el-form-item label="审核结果">
          <el-tag :type="props.approvalResult === '审核通过' ? 'success' : 'danger'">
            {{ props.approvalResult }}
          </el-tag>
        </el-form-item>

        <el-form-item label="审核备注" prop="approveRemark">
          <el-input
            v-model="approvalFormData.approveRemark"
            type="textarea"
            :rows="4"
            placeholder="请输入审核备注"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="handleClose">取 消</el-button>
      <el-button 
        type="primary" 
        :loading="submitting" 
        @click="handleSubmit"
      >
        {{ submitting ? '提交中...' : '确认审核' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup name="WriteOffApprovalDialog">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { approveWriteOff } from '@/api/management/order-writeroff'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  writeoffRecord: {
    type: Object,
    default: null
  },
  approvalResult: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const visible = ref(props.modelValue)
const submitting = ref(false)
const approvalForm = ref()

const approvalFormData = reactive({
  approveRemark: ''
})

const approvalRules = {
  approveRemark: [
    { required: true, message: '请输入审核备注', trigger: 'blur' },
    { min: 5, max: 200, message: '审核备注长度在5到200个字符', trigger: 'blur' }
  ]
}

// 监听器
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    resetForm()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 方法
const resetForm = () => {
  approvalFormData.approveRemark = ''
  nextTick(() => {
    if (approvalForm.value) {
      approvalForm.value.resetFields()
    }
  })
}

const handleSubmit = () => {
  approvalForm.value.validate(async (valid) => {
    if (valid) {
      await doApproval()
    }
  })
}

const doApproval = async () => {
  try {
    submitting.value = true

    const approvalData = {
      approveResult: props.approvalResult,
      approveReason: approvalFormData.approveRemark
    }

    const response = await approveWriteOff(props.writeoffRecord.id, approvalData)

    if (response.code === 200) {
      ElMessage.success('审核成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error('审核失败: ' + (response.msg || '未知错误'))
    }
  } catch (error) {
    console.error('审核失败:', error)
    ElMessage.error('审核失败: ' + (error.message || '未知错误'))
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}
</script>

<style scoped>
.approval-content {
  padding: 10px 0;
}

.info-card {
  border-radius: 8px;
}
</style>
