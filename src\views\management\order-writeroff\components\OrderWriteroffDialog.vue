<template>
  <!-- 订单核销对话框 -->
  <el-dialog
      v-model="visible"
      title="订单核销"
      width="1000px"
      :close-on-click-modal="false"
      @close="handleCancel"
  >
    <el-form 
      ref="formRef" 
      :model="formData" 
      :rules="rules" 
      label-width="150px"
    >
      <el-form-item label="学生信息：">
        <div v-if="localStudentInfo && localStudentInfo.name" class="student-info">
          <span class="student-name">{{ localStudentInfo.name }}</span>
          <span class="student-phone">{{ localStudentInfo.phone || '' }}</span>
          <el-tag size="small" type="success" v-if="localStudentInfo.grade">{{ getGradeText(localStudentInfo.grade) }}</el-tag>
        </div>
        <div v-else class="student-info">
          <span class="student-name text-warning">未选择学生</span>
        </div>
      </el-form-item>

      <el-form-item label="家长信息：">
        <div v-if="localStudentInfo && (localStudentInfo.parentName || localStudentInfo.parentPhone)" class="parent-info">
          <span class="parent-name" v-if="localStudentInfo.parentName">{{ localStudentInfo.parentName }}</span>
          <span class="parent-phone" v-if="localStudentInfo.parentPhone">{{ localStudentInfo.parentPhone }}</span>
        </div>
        <div v-else class="student-info">
          <span class="student-name text-warning">未设置家长信息</span>
        </div>
      </el-form-item>
      
      <el-form-item label="核销平台：" prop="orderSource">
        <el-select
            :disabled="true"
            v-model="formData.orderSource"
            placeholder="请选择核销平台"
            @change="handleSourceChange"
        >
          <el-option
              v-for="item in OrderWriteroffSource"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item :label="`${formData.orderSource || '平台'}订单号：`" prop="orderNo">
        <el-input
            :disabled="selectOrderInfo"
            v-model="formData.orderNo"
            :placeholder="`请输入${formData.orderSource || '平台'}订单号`"
            clearable
            @blur="handleOrderNoBlur"
        />
      </el-form-item>

      <!-- 产品配置选择 -->
      <el-form-item label="产品配置：" v-if="showProductConfig">
        <!-- 调试信息 -->
        <!-- <div style="color: red; font-size: 12px;">
          Debug: showProductConfig={{ showProductConfig }},
          hasProductInfo={{ !!props.productInfo }},
          productName={{ props.productInfo?.name }}
        </div> -->

        <!-- 当有预设产品时，直接显示选中的产品 -->
        <div v-if="props.productInfo" class="selected-product-info">
          <el-card shadow="never" style="border: 1px solid #67c23a;">
            <div class="product-info-display">
              <div class="product-header">
                <span class="product-name">{{ props.productInfo.name }}</span>
                <el-tag type="success" size="small">已选中</el-tag>
              </div>
              <div class="product-details">
                <span class="product-subject">{{ props.productInfo.subject }} / {{ props.productInfo.courseType }}</span>
                <span class="product-price">¥{{ (props.productInfo.sellingPrice / 100).toFixed(2) }}</span>
              </div>
              <div class="product-grades">
                <span>适用年级：</span>
                <el-tag
                  v-for="grade in props.productInfo.applicableGrades"
                  :key="grade"
                  size="small"
                  style="margin-right: 5px"
                >
                  {{ grade }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 当没有预设产品时，显示产品选择界面 -->
        <div v-else class="product-config-section">
          <div class="config-header">
            <span>请选择要核销的产品配置：</span>
            <el-button type="text" @click="refreshProductList">
              <el-icon><Refresh /></el-icon>
              刷新产品列表
            </el-button>
          </div>

          <!-- 产品搜索表单 -->
          <div class="product-search-form">
            <el-form :inline="true" class="search-form">
              <el-form-item label="产品名称">
                <el-input
                  v-model="productSearchForm.name"
                  placeholder="请输入产品名称"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item label="学科">
                <el-select
                  v-model="productSearchForm.subject"
                  placeholder="请选择学科"
                  clearable
                  @change="handleSubjectChange"
                  style="width: 150px"
                >
                  <el-option label="英语" value="英语" />
                  <el-option label="语文" value="语文" />
                  <el-option label="数学" value="数学" />
                  <el-option label="物理" value="物理" />
                  <el-option label="化学" value="化学" />
                </el-select>
              </el-form-item>
              <el-form-item label="课型">
                <el-select
                  v-model="productSearchForm.courseType"
                  placeholder="请选择课型"
                  clearable
                  style="width: 150px"
                >
                  <el-option
                      v-for="option in availableSpecifications"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleProductSearch">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="resetProductSearch">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 产品显示选项 -->
          <div class="product-options" v-if="localStudentInfo">
            <el-form :inline="true" class="options-form">
              <el-form-item label="显示不可用产品">
                <el-switch
                  v-model="showInvalidProducts"
                  @change="handleShowInvalidChange"
                  active-text="显示"
                  inactive-text="隐藏"
                />
              </el-form-item>
            </el-form>
          </div>

          <div class="product-list" v-loading="productLoading">
            <el-row :gutter="16" class="product-grid">
              <el-col :xs="24" :sm="12" :md="8" :lg="8" v-for="product in filteredProducts" :key="product.id" style="margin-bottom:10px">
                <el-card
                  :class="[
                    'product-card',
                    { 'selected': selectedProduct && selectedProduct.id === product.id },
                    { 'disabled': isProductDisabled(product) },
                  ]"
                  @click="!isProductDisabled(product) ? selectProduct(product) : null"
                  shadow="hover"
                >
                  <!-- 客户类型标识 - 右上角标签 -->
                  <div class="customer-type-corner">
                    <el-tag
                      :type="product.customerType !== '通用' ? (product.customerType === '仅老客户可用' ? 'success' : 'warning') : 'primary'"
                      size="small"
                    >
                      {{ product.customerType }}
                    </el-tag>
                  </div>

                  <div class="product-info">
                    <h4 class="product-name">{{ product.name }}</h4>
                    <div class="product-meta">
                      <el-tag size="small" type="success">{{ product.subject }}</el-tag>
                      <el-tag size="small" type="info" style="margin-left: 8px">{{ product.courseType }}</el-tag>
                    </div>
                    <div class="product-meta" v-if="product.applicableGrades" style=" line-height: 25px;">
                      <el-tag
                          size="small"
                          :type="localStudentInfo && !isGradeApplicable(grade, localStudentInfo.grade) ? 'danger' : 'info'"
                          v-for="grade in product.applicableGrades"
                          :key="grade"
                          style="margin-right: 10px;"
                      >
                        {{ getGradeText(grade) }}
                      </el-tag>
                    </div>
                    <div class="product-details">
                      <span>正课：{{ product.quantity }}课时</span>
                      <span v-if="product.hasBonusHours" style="margin-left: 10px">
                        赠送：{{ product.bonusHoursQuantity }}课时
                      </span>
                    </div>
                    <div class="product-price">
                      ¥{{ (product.sellingPrice / 100).toFixed(2) }}
                    </div>

                    <!-- 综合不适用提示 - 只在显示所有产品时显示 -->
                    <div v-if="showInvalidProducts && localStudentInfo && getProductIncompatibleReasons(product, localStudentInfo).length > 0" class="incompatible-notice">
                      <el-icon><WarningFilled /></el-icon>
                      <span>{{ getProductIncompatibleReasons(product, localStudentInfo)?.join('，') }}</span>
                    </div>

                    <!-- 仅无效产品提示（当没有选择学生时） -->
                    <div v-else-if="!localStudentInfo && product.validStatus === '无效'" class="incompatible-notice">
                      <el-icon><WarningFilled /></el-icon>
                      <span>产品已无效</span>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>

            <div v-if="!filteredProducts.length && !productLoading" class="no-products">
              <el-empty
                description="暂无可用产品"
                :image-size="100"
              >
                <template #description>
                  <span v-if="!showInvalidProducts">
                    暂无可用产品，您可以尝试
                    <el-button type="text" @click="showInvalidProducts = true">显示不可用产品</el-button>
                  </span>
                  <span v-else>暂无产品数据</span>
                </template>
              </el-empty>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="订单截图：" prop="orderImageFile">
        <el-upload
            ref="upload"
          action="#"
          list-type="picture-card"
          :auto-upload="false"
          :before-upload="beforeOrderImageUpload"
          :file-list="fileList"
          :limit="1" :multiple="false"
          :on-exceed="handleExceed"
          accept="image/*"
          @remove="handleRemove"
          :on-change="handleChange"
        >
          <el-icon><Plus /></el-icon>
          
          <template #file="{ file }">
            <div>
              <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
              <span class="el-upload-list__item-actions">
                <span
                  class="el-upload-list__item-preview"
                  @click="handlePictureCardPreview(file)"
                >
                  <el-icon><ZoomIn /></el-icon>
                </span>
                <span
                  class="el-upload-list__item-delete"
                  @click="handleRemove(file)"
                >
                  <el-icon><Delete /></el-icon>
                </span>
              </span>
            </div>
          </template>
          
          <template #tip>
            <div class="el-upload__tip">
              支持JPG、PNG格式，大小不超过2MB。
            </div>
          </template>
        </el-upload>
        
        <el-dialog v-model="dialogVisible" title="预览图片">
          <img w-full :src="dialogImageUrl" alt="Preview Image" style="max-width: 100%;" />
        </el-dialog>
      </el-form-item>
      
      <el-form-item label="备注：">
        <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="OrderWriteroffDialog">
import {reactive, ref, watch, computed} from 'vue'
import type {UploadInstance, UploadProps, UploadRawFile} from 'element-plus'
import {ElMessage, genFileId} from 'element-plus'
import {writeOffOrderApi} from '@/api/management/order-writeoff'
import {OrderWriteroffSource} from '@/api/management/common/OrderConstants'
import {Delete, Plus, Refresh, Search, WarningFilled, ZoomIn} from '@element-plus/icons-vue'
import {getAvailableProductsApi} from '@/api/management/product'
import {getAlreadyPaidOrdersApi} from '@/api/management/order'
import request from '@/utils/request'
import {getGradeText, GRADE_OPTIONS} from '@/utils/gradeUtils'

// Props
interface Props {
  modelValue: boolean
  studentInfo?: any
  orderInfo?: any
  productInfo?: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
}>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const confirmDialogVisible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()
const fileList = ref([])
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const upload = ref<UploadInstance>()
const localStudentInfo = ref(null)
const selectOrderInfo = ref(null)

// 产品配置相关
const showProductConfig = ref(false)
const productLoading = ref(false)
const availableProducts = ref([])
const selectedProduct = ref(null)
const showInvalidProducts = ref(false)
const subjectCourseTypes = ref([])

// 产品搜索表单
const productSearchForm = reactive({
  name: '',
  subject: '',
  courseType: ''
})

// 表单数据
const formData = reactive({
  orderSource: '抖店',
  orderNo: '',
  orderImage: '',
  orderImageFile: null,
  remark: '',
})

const allSpecifications = [
      { label: "单词课", value: "单词课" },
      { label: "音标拼读课", value: "音标拼读课" },
      { label: "语法课", value: "语法课" },
      { label: "题型课", value: "题型课" },
      { label: "听说课", value: "听说课" },
      { label: "通用课（非英语）", value: "通用课（非英语）" }
    ]
const englishSpecifications = [
      { label: "单词课", value: "单词课" },
      { label: "音标拼读课", value: "音标拼读课" },
      { label: "语法课", value: "语法课" },
      { label: "题型课", value: "题型课" },
      { label: "听说课", value: "听说课" }
    ]
const otherSubjectSpecifications = [
      { label: "通用课（非英语）", value: "通用课（非英语）" }
    ]
const availableSpecifications = ref(allSpecifications)

// 表单验证规则
const rules = {
  orderSource: [
    { required: true, message: '请选择核销平台', trigger: 'change' }
  ],
  orderNo: [
    { required: true, message: '请输入订单号', trigger: 'blur' },
    { min: 3, max: 50, message: '订单号长度在 3 到 50 个字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9_-]+$/,
      message: '订单号只能包含字母、数字、下划线和横线',
      trigger: 'blur'
    }
  ],
  orderImageFile: [
    {
      required: true,
      message: '请上传订单截图',
      trigger: 'change'
    }
  ]
}

// 计算属性：过滤后的产品列表
const filteredProducts = computed(() => {
  if (!availableProducts.value || availableProducts.value.length === 0) {
    return []
  }

  // 如果显示不可用产品，返回所有产品
  if (showInvalidProducts.value) {
    return availableProducts.value
  }

  // 否则过滤掉不可用的产品
  return availableProducts.value.filter(product => {
    // 检查产品有效性
    if (product.validStatus === '无效') {
      return false
    }

    // 如果有学生信息，检查产品是否适用于该学生
    if (localStudentInfo.value) {
      const reasons = getProductIncompatibleReasons(product, localStudentInfo.value)
      return reasons.length === 0
    }

    // 没有学生信息时，只要产品有效就显示
    return true
  })
})


// 检查产品是否不适用（用于disabled状态判断）
const isProductDisabled = (product) => {
  // 在显示所有产品模式下，检查产品是否不适用
  if (showInvalidProducts.value) {
    // 检查产品有效性
    if (product.validStatus === '无效') {
      return true
    }

    // 如果有学生信息，检查产品是否适用于该学生
    if (localStudentInfo.value) {
      const reasons = getProductIncompatibleReasons(product, localStudentInfo.value)
      return reasons.length > 0
    }
  }

  // 隐藏模式下，不适用的产品已经被过滤掉了，所以不需要禁用
  return false
}

// 监听弹窗显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    resetForm()
    if(props.orderInfo) {
      formData.orderNo = props.orderInfo.originOrderId
      selectOrderInfo.value = {...props.studentInfo}
    }
    // 同步学生信息到本地变量
    if (props.studentInfo) {
      localStudentInfo.value = {...props.studentInfo}
      // 加载学生已购买的课型
      if (props.studentInfo.id) {
        loadStudentPaidCourseTypes(props.studentInfo.id)
      }
    }

    // 如果有产品信息，自动选择该产品
    if (props.productInfo) {
      selectedProduct.value = {...props.productInfo}
      showProductConfig.value = true
      // 当有预设产品时，不需要加载产品列表，直接显示选中的产品
    } else {
      // 没有预设产品时，需要等待订单号输入后才显示产品配置
      showProductConfig.value = false
    }
  }
})

// 监听props.studentInfo的变化
watch(() => props.studentInfo, async (newVal) => {
  if (newVal) {
    localStudentInfo.value = {...newVal}
    // 加载学生已购买的课型
    if (newVal.id) {
      await loadStudentPaidCourseTypes(newVal.id)
    }
  } else {
    subjectCourseTypes.value = []
  }
}, { immediate: true })

// 监听props.productInfo的变化
watch(() => props.productInfo, (newVal) => {
  if (newVal) {
    selectedProduct.value = {...newVal}
    showProductConfig.value = true
    // 当有预设产品时，不需要加载产品列表，直接显示选中的产品
  }
}, { immediate: true })

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 图片上传前的验证
const beforeOrderImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('上传文件只能是图片格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 自定义上传图片
const uploadOrderImage = async (file) => {
  try {
    // 创建FormData对象
    const formDataUpload = new FormData()

    formDataUpload.append('file', new File([file.raw], file.name, {
      type: file.raw.type,
    }))
    formDataUpload.append('dir', 'order-writeroff-images')

    // 调用后端上传接口
    const result = await request({
      url: '/word/file/upload',
      method: 'post',
      data: formDataUpload,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    if (result.success) {
      // 上传成功，保存图片URL
      formData.orderImage = result.data.fileUrl

      // 添加到文件列表中以便显示
      fileList.value = [{
        name: file.name,
        url: result.data.fileUrl
      }]

      // 手动触发表单验证，清除orderImage字段的错误状态
      if (formRef.value) {
        formRef.value.validateField('orderImage')
      }

      ElMessage.success('图片上传成功')
    } else {
      ElMessage.error(result.message || '图片上传失败')
    }
  } catch (error) {
    console.error('图片上传失败:', error)
    ElMessage.error('图片上传失败，请重试')
  }
}

// 处理图片预览
const handlePictureCardPreview = (file) => {
  dialogImageUrl.value = file.url
  dialogVisible.value = true
}

// 处理图片删除
const handleRemove = (file) => {
  fileList.value = []
  formData.orderImage = ''

  // 手动触发表单验证，恢复orderImage字段的错误状态
  if (formRef.value) {
    formRef.value.validateField('orderImage')
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    orderSource: '抖店',
    orderNo: '',
    orderImage: '',
    orderImageFile: null,
    remark: ''
  })
  fileList.value = []
  dialogImageUrl.value = ''
  dialogVisible.value = false
  confirmDialogVisible.value = false

  // 重置产品相关状态（如果没有预设产品）
  if (!props.productInfo) {
    showProductConfig.value = false
    selectedProduct.value = null
    availableProducts.value = []

    // 重置产品搜索表单
    productSearchForm.name = ''
    productSearchForm.subject = ''
    productSearchForm.courseType = ''
  }
}

// 处理平台变化
const handleSourceChange = () => {
  formData.orderNo = ''
  // 如果没有预设产品，才清除产品选择
  if (!props.productInfo) {
    showProductConfig.value = false
    selectedProduct.value = null
  }
}

// 处理订单号失焦
const handleOrderNoBlur = () => {
  if (formData.orderNo && formData.orderSource) {
    showProductConfig.value = true
    // 如果没有预设产品，才加载产品列表
    if (!props.productInfo) {
      loadAvailableProducts()
    }
  } else {
    // 如果有预设产品，始终显示产品配置
    if (props.productInfo) {
      showProductConfig.value = true
    } else {
      showProductConfig.value = false
    }
  }
}

// 加载可用产品列表
const loadAvailableProducts = async () => {
  try {
    productLoading.value = true
    const params = {
      showInvalid: true,
      studentId: localStudentInfo.value?.id,
      ...productSearchForm
    }

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (!params[key]) {
        delete params[key]
      }
    })

    const response = await getAvailableProductsApi(params)
    availableProducts.value = response.data || []
  } catch (error) {
    ElMessage.error('加载产品列表失败')
    availableProducts.value = []
  } finally {
    productLoading.value = false
  }
}

// 刷新产品列表
const refreshProductList = () => {
  loadAvailableProducts()
}

const handleSubjectChange = (subject) => {
  productSearchForm.courseType = ''
  if (!subject) {
    // 没有选择学科，显示所有课型
    availableSpecifications.value = allSpecifications
  } else if (subject === '英语') {
    // 英语学科，显示英语相关课型
    availableSpecifications.value = englishSpecifications
  } else {
    // 其他学科，只显示通用课
    availableSpecifications.value = otherSubjectSpecifications
  }
}

// 选择产品
const selectProduct = (product) => {
  // 检查产品有效性
  if (product.validStatus === '无效') {
    ElMessage.warning('该产品已无效，无法选择')
    return
  }

  // 如果选择了学生，检查所有不适用原因
  if (localStudentInfo.value) {
    const reasons = getProductIncompatibleReasons(product, localStudentInfo.value)
    if (reasons.length > 0) {
      ElMessage.warning(`无法选择该产品：${reasons.join('，')}`)
      return
    }
  }

  selectedProduct.value = product
}

// 处理产品搜索
const handleProductSearch = () => {
  loadAvailableProducts()
}

// 重置产品搜索
const resetProductSearch = () => {
  productSearchForm.name = ''
  productSearchForm.subject = ''
  productSearchForm.courseType = ''
  loadAvailableProducts()
}

// 获取学生已购买的课型
const loadStudentPaidCourseTypes = async (studentId) => {
  try {
    const response = await getAlreadyPaidOrdersApi(studentId)
    subjectCourseTypes.value = response.data || []
    console.log('学生已购买课型:', subjectCourseTypes.value)
  } catch (error) {
    console.error('获取学生已购买课型失败:', error)
    subjectCourseTypes.value = []
  }
}

// 检查产品是否适用于学生
const isProductApplicableForStudent = (product, student) => {
  if (!product || !student || !product.applicableGrades || !student.grade) {
    return false
  }

  return product.applicableGrades.includes(getGradeText(student.grade))
}

// 检查家长信息是否完整
const isParentInfoComplete = (student) => {
  if (!student) {
    return false
  }
  // 检查家长姓名和家长手机号是否都不为空
  return !!(student.parentName && student.parentName.trim() && student.parentPhone && student.parentPhone.trim())
}

// 检查产品客户类型是否适用于学生
const isCustomerTypeApplicable = (product, student) => {
  if (!product || !product.customerType) {
    return true // 如果没有客户类型限制，默认适用
  }

  // 通用产品，所有人都可以购买
  if (product.customerType === '通用') {
    return true
  }

  if (!student || !subjectCourseTypes.value) {
    return false
  }

  const currentCourseType = `${product.subject}_${product.courseType}`
  const hasPurchased = subjectCourseTypes.value.includes(currentCourseType)

  // 仅新客可用：学生没有购买过该学科和课型
  if (product.customerType === '仅新客可用') {
    return !hasPurchased
  }

  // 仅老客可用：学生之前购买过该学科和课型
  if (product.customerType === '仅老客可用') {
    return hasPurchased
  }

  return true
}

// 获取客户类型不适用的原因
const getCustomerTypeIncompatibleReason = (product) => {
  if (!product || !product.customerType || !localStudentInfo.value) {
    return ''
  }

  const currentCourseType = `${product.subject}_${product.courseType}`
  const hasPurchased = subjectCourseTypes.value.includes(currentCourseType)

  if (product.customerType === '仅新客可用' && hasPurchased) {
    return '仅新客可用，您已购买过该课型'
  }

  if (product.customerType === '仅老客可用' && !hasPurchased) {
    return '仅老客可用，您未购买过该课型'
  }

  return ''
}

// 获取产品的所有不适用原因
const getProductIncompatibleReasons = (product, student) => {
  if (!product || !student) {
    return []
  }

  const reasons = []

  // 检查年级适用性
  if (!isProductApplicableForStudent(product, student)) {
    reasons.push(`不适用于${getGradeText(student.grade)}`)
  }

  // 检查家长信息完整性
  if (!isParentInfoComplete(student)) {
    reasons.push('家长信息不完整')
  }

  // 检查客户类型适用性
  if (!isCustomerTypeApplicable(product, student)) {
    const customerTypeReason = getCustomerTypeIncompatibleReason(product)
    if (customerTypeReason) {
      reasons.push(customerTypeReason)
    }
  }

  return reasons
}

// 处理显示无效产品开关变化
const handleShowInvalidChange = () => {
  // 提供反馈信息
  if (showInvalidProducts.value) {
    ElMessage.info('已显示所有产品（包括不可用产品），不可用产品将显示为置灰状态')
  } else {
    ElMessage.info('已隐藏不可用产品，只显示可用产品')
  }
}

// 处理取消
const handleCancel = () => {
  visible.value = false
  resetForm()
}

// 处理确认 - 显示确认弹窗
const handleConfirm = async () => {
  if (!formRef.value) return
  await formRef.value.validate()
  if (!localStudentInfo.value?.id) {
    ElMessage.error('学生信息不完整，无法进行核销')
    return
  }
  if (!formData.orderImageFile) {
    ElMessage.error('请上传订单截图')
    return
  }

  if (!formData.orderImage && formData.orderImageFile) {
    const reader = new FileReader()
    reader.readAsDataURL(formData.orderImageFile.raw)
    reader.onload = async () => {
      formData.orderImage = reader.result
    }
    await handleFinalConfirm();
    return
  }
}


// 处理最终确认核销
const handleFinalConfirm = async () => {
  try {
    confirmLoading.value = true

    // 先上传图片到后端
    let uploadedImageUrl = ''
    if (formData.orderImageFile) {
      try {
        const formDataUpload = new FormData()
        formDataUpload.append('file', new File([formData.orderImageFile.raw], formData.orderImageFile.name, {
          type: formData.orderImageFile.raw.type,
        }))
        formDataUpload.append('dir', 'order-writeroff-images')

        const uploadResult = await request({
          url: '/word/file/upload',
          method: 'post',
          data: formDataUpload
        })

        if (uploadResult.success) {
          uploadedImageUrl = uploadResult.data.fileUrl
        } else {
          ElMessage.error('图片上传失败：' + (uploadResult.message || '未知错误'))
          return
        }
      } catch (uploadError) {
        console.error('图片上传失败:', uploadError)
        ElMessage.error('图片上传失败，请重试')
        return
      }
    }

    const params = {
      studentId: localStudentInfo.value.id,
      orderSource: formData.orderSource,
      orderNo: formData.orderNo,
      orderImg: uploadedImageUrl,
      remark: formData.remark || '',
      productId: selectedProduct.value?.id || null
    }
    
    await writeOffOrderApi(params)
    
    ElMessage.success('订单核销提交成功，等待审核')
    
    // 关闭所有弹窗
    confirmDialogVisible.value = false
    visible.value = false
    
    emit('success')
    resetForm()
    
  } finally {
    confirmLoading.value = false
  }
}

const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}

const handleChange: UploadProps['onChange'] = (uploadFile, uploadFiles) => {
  formData.orderImageFile = uploadFile

  if (uploadFiles.length !== 0) {
    formRef.value.validateField('orderImageFile')
  }
}

// 获取年级文本
const getGradeText = (grade) => {
  const option = GRADE_OPTIONS.find(item => item.value === parseInt(grade))
  return option ? option.label : grade
}

/** 检查年级是否适用（用于产品年级标签显示） */
const isGradeApplicable = (gradeToCheck:any, studentGrade:any) => {
  if (!gradeToCheck || !studentGrade) return false
  return String(gradeToCheck) === String(studentGrade)
}
</script>

<style scoped>
.student-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.student-name {
  font-weight: 500;
  color: #303133;
}

.student-phone {
  color: #909399;
  font-size: 14px;
}

.parent-info {
  display: flex;
  align-items: center;
  gap: 16px;
  padding-left: 4px;
}

.parent-label {
  color: #606266;
  font-size: 13px;
}

.parent-name {
  font-weight: 500;
  color: #303133;
}

.parent-phone {
  color: #909399;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

.order-image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 178px;
  height: 178px;
}

/* 产品配置相关样式 */
.product-config-section {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  background: #fafafa;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 500;
  color: #303133;
}

.product-search-form {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.product-search-form .search-form {
  margin: 0;
}

.product-search-form .el-form-item {
  margin-bottom: 0;
}

.product-list {
  max-height: 400px;
  overflow-y: auto;
}

/* 产品显示选项样式 */
.product-options {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.options-form {
  margin: 0;
}

/* 产品网格布局 */
.product-grid {
  min-height: 200px;
}

.product-card {
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  height: 100%;
  border: 1px solid #e1dfdf;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.product-card.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.product-card.invalid {
  border-color: #f56c6c;
  background: #fef0f0;
}

/* 产品卡片禁用状态 */
.product-card.disabled {
  cursor: not-allowed;
  background-color: #f5f5f5;
  pointer-events: none;
}

.product-card.disabled:hover {
  box-shadow: none;
  transform: none;
}

.product-card.disabled .product-info {
  color: #999;
}

/* 为disabled产品添加遮罩效果 */
.product-card.disabled::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 5;
  border-radius: 4px;
}

.product-info {
  padding: 12px;
}

.product-name {
  border-left: 3px solid #409EFF;
  padding-left: 10px;
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.product-meta {
  margin: 8px 0;
}

.product-details {
  margin: 8px 0;
  color: #67c23a;
  font-weight: 500;
}

.product-price {
  margin: 8px 0;
  font-size: 18px;
  font-weight: bold;
  color: #e6a23c;
}

.invalid-notice {
  color: #f56c6c;
  font-size: 12px;
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.invalid-notice .el-icon {
  margin-right: 4px;
}

.no-products {
  text-align: center;
  padding: 40px 0;
}

.order-image-uploader:hover {
  border-color: #409EFF;
}

.order-image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.order-image {
  width: 178px;
  height: 178px;
  display: block;
  object-fit: cover;
}

.el-upload__tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

/* 客户类型标识样式 - 右上角标签 */
.customer-type-corner {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 15;
}

.customer-type-corner .el-tag {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 0 4px 0 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 不适用提示样式 */
.incompatible-notice {
  display: flex;
  align-items: center;
  margin-top: 8px;
  padding: 4px 8px;
  background-color: #fdf6ec;
  border: 1px solid #f5dab1;
  border-radius: 4px;
  color: #e6a23c;
  font-size: 12px;
}

.incompatible-notice .el-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* 空状态样式 */
.no-products {
  padding: 40px 20px;
  text-align: center;
}

/* 选中产品信息显示样式 */
.selected-product-info .product-info-display .product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.selected-product-info .product-info-display .product-header .product-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.selected-product-info .product-info-display .product-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.selected-product-info .product-info-display .product-details .product-subject {
  color: #606266;
  font-size: 14px;
}

.selected-product-info .product-info-display .product-details .product-price {
  font-size: 18px;
  font-weight: 600;
  color: #67c23a;
}

.selected-product-info .product-info-display .product-grades {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 5px;
}

.selected-product-info .product-info-display .product-grades span:first-child {
  color: #606266;
  font-size: 14px;
  margin-right: 5px;
}
</style>