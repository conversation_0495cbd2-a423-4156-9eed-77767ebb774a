import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { teachingGroupApi } from '@/api/management/teachingGroup.ts'

export const useTeachingGroupStore = defineStore('teachingGroup', () => {
  // 状态
  const teachingGroups = ref([])
  const currentGroup = ref(null)
  const groupTeachers = ref([])
  const availableTeachers = ref([])
  const allTeachers = ref([])
  const teachingGroupStats = ref({
    totalGroups: 0,
    activeGroups: 0,
    totalTeachers: 0,
    unassignedTeachers: 0
  })
  const loading = ref(false)
  const teachersLoading = ref(false)

  // 计算属性
  const activeGroups = computed(() => 
    teachingGroups.value.filter(group => group.status === 'active')
  )

  const inactiveGroups = computed(() => 
    teachingGroups.value.filter(group => group.status === 'inactive')
  )

  // 获取教学组列表
  const fetchTeachingGroups = async (params = {}) => {
    try {
      loading.value = true
      const response = await teachingGroupApi.getTeachingGroups({
        pageNum: 1,
        pageSize: 100,
        ...params
      })
      
      if (response.code === 200) {
        teachingGroups.value = response.data.rows ?? response.data.records
        return response.data
      } else {
        ElMessage.error(response.message || '获取教学组列表失败')
        return null
      }
    } catch (error) {
      console.error('获取教学组列表失败:', error)
      ElMessage.error('获取教学组列表失败')
      return null
    } finally {
      loading.value = false
    }
  }

  // 获取教学组详情
  const fetchTeachingGroup = async (id) => {
    try {
      const response = await teachingGroupApi.getTeachingGroup(id)
      
      if (response.code === 200) {
        currentGroup.value = response.data
        return response.data
      } else {
        ElMessage.error(response.message || '获取教学组详情失败')
        return null
      }
    } catch (error) {
      console.error('获取教学组详情失败:', error)
      ElMessage.error('获取教学组详情失败')
      return null
    }
  }

  // 创建教学组
  const createTeachingGroup = async (data) => {
    try {
      const response = await teachingGroupApi.createTeachingGroup(data)
      
      if (response.code === 200) {
        ElMessage.success('创建教学组成功')
        await fetchTeachingGroups()
        await fetchTeachingGroupStats()
        return true
      } else {
        ElMessage.error(response.message || '创建教学组失败')
        return false
      }
    } catch (error) {
      console.error('创建教学组失败:', error)
      ElMessage.error('创建教学组失败')
      return false
    }
  }

  // 更新教学组
  const updateTeachingGroup = async (data) => {
    try {
      const response = await teachingGroupApi.updateTeachingGroup(data)
      
      if (response.code === 200) {
        ElMessage.success('更新教学组成功')
        await fetchTeachingGroups()
        if (currentGroup.value && currentGroup.value.id === data.id) {
          await fetchTeachingGroup(data.id)
        }
        return true
      } else {
        ElMessage.error(response.message || '更新教学组失败')
        return false
      }
    } catch (error) {
      console.error('更新教学组失败:', error)
      ElMessage.error('更新教学组失败')
      return false
    }
  }

  // 删除教学组
  const deleteTeachingGroup = async (id) => {
    try {
      const response = await teachingGroupApi.deleteTeachingGroup(id)

      if (response.code === 200) {
        ElMessage.success('删除教学组成功')
        await fetchTeachingGroups()
        await fetchTeachingGroupStats()
        return true
      } else {
        ElMessage.error(response.message || '删除教学组失败')
        return false
      }
    } catch (error) {
      console.error('删除教学组失败:', error)
      ElMessage.error('删除教学组失败')
      return false
    }
  }

  // 批量删除教学组
  const deleteTeachingGroups = async (ids) => {
    try {
      const response = await teachingGroupApi.deleteTeachingGroups(ids)

      if (response.code === 200) {
        ElMessage.success('批量删除教学组成功')
        await fetchTeachingGroups()
        await fetchTeachingGroupStats()
        return true
      } else {
        ElMessage.error(response.message || '批量删除教学组失败')
        return false
      }
    } catch (error) {
      console.error('批量删除教学组失败:', error)
      ElMessage.error('批量删除教学组失败')
      return false
    }
  }



  // 获取教学组教师列表
  const fetchGroupTeachers = async (groupId, params = {}) => {
    try {
      teachersLoading.value = true
      const response = await teachingGroupApi.getGroupTeachers({
        groupId,
        pageNum: 1,
        pageSize: 100,
        ...params
      })
      
      if (response.code === 200) {
        groupTeachers.value = response.data.rows ?? response.data.records
        return response.data
      } else {
        ElMessage.error(response.message || '获取教师列表失败')
        return null
      }
    } catch (error) {
      console.error('获取教师列表失败:', error)
      ElMessage.error('获取教师列表失败')
      return null
    } finally {
      teachersLoading.value = false
    }
  }

  // 分配教师到教学组
  const assignTeachers = async (groupId, teacherIds) => {
    try {
      const response = await teachingGroupApi.assignTeachers({ groupId, teacherIds })
      
      if (response.code === 200) {
        ElMessage.success('分配教师成功')
        await fetchGroupTeachers(groupId)
        await fetchAvailableTeachers()
        await fetchTeachingGroupStats()
        return true
      } else {
        ElMessage.error(response.message || '分配教师失败')
        return false
      }
    } catch (error) {
      console.error('分配教师失败:', error)
      ElMessage.error('分配教师失败')
      return false
    }
  }

  // 从教学组移除教师
  const removeTeachers = async (groupId, teacherIds) => {
    try {
      const response = await teachingGroupApi.removeTeachers({ groupId, teacherIds })
      
      if (response.code === 200) {
        ElMessage.success('移除教师成功')
        await fetchGroupTeachers(groupId)
        await fetchAvailableTeachers()
        await fetchTeachingGroupStats()
        return true
      } else {
        ElMessage.error(response.message || '移除教师失败')
        return false
      }
    } catch (error) {
      console.error('移除教师失败:', error)
      ElMessage.error('移除教师失败')
      return false
    }
  }

  // 获取可分配的教师列表
  const fetchAvailableTeachers = async () => {
    try {
      const response = await teachingGroupApi.getAvailableTeachers()
      
      if (response.code === 200) {
        availableTeachers.value = response.data
        return response.data
      } else {
        ElMessage.error(response.message || '获取可分配教师列表失败')
        return []
      }
    } catch (error) {
      console.error('获取可分配教师列表失败:', error)
      ElMessage.error('获取可分配教师列表失败')
      return []
    }
  }

  // 获取教师列表（支持搜索和分页）
  const fetchTeachers = async (params) => {
    try {
      const response = await teachingGroupApi.getTeachers(params)

      if (response.code === 200) {
        allTeachers.value = response.data.records || response.data.rows || []
        return response.data
      } else {
        ElMessage.error(response.message || '获取教师列表失败')
        return { records: [], total: 0 }
      }
    } catch (error) {
      console.error('获取教师列表失败:', error)
      ElMessage.error('获取教师列表失败')
      return { records: [], total: 0 }
    }
  }

  // 获取所有教师列表
  const fetchAllTeachers = async () => {
    try {
      const response = await teachingGroupApi.getAllTeachers()

      if (response.code === 200) {
        allTeachers.value = response.data
        return response.data
      } else {
        ElMessage.error(response.message || '获取教师列表失败')
        return []
      }
    } catch (error) {
      console.error('获取教师列表失败:', error)
      ElMessage.error('获取教师列表失败')
      return []
    }
  }

  // 获取教学组统计信息
  const fetchTeachingGroupStats = async () => {
    try {
      const response = await teachingGroupApi.getTeachingGroupStats()
      
      if (response.code === 200) {
        teachingGroupStats.value = response.data
        return response.data
      } else {
        console.error('获取统计信息失败:', response.message)
        return null
      }
    } catch (error) {
      console.error('获取统计信息失败:', error)
      return null
    }
  }

  // 获取教师时间表
  const fetchTeacherTimeSlots = async (teacherId) => {
    try {
      const response = await teachingGroupApi.getTeacherTimeSlots({ teacherId })

      if (response.code === 200) {
        return response.data
      } else {
        ElMessage.error(response.message || '获取教师时间表失败')
        return []
      }
    } catch (error) {
      console.error('获取教师时间表失败:', error)
      ElMessage.error('获取教师时间表失败')
      return []
    }
  }

  // 更新教师时间表
  const updateTeacherTimeSlots = async (params) => {
    try {
      // 支持两种调用方式：
      // 1. updateTeacherTimeSlots({ teacherId, timeSlots })
      // 2. updateTeacherTimeSlots(teacherId, { timeSlots })
      let teacherId, timeSlots
      if (typeof params === 'object' && params.teacherId) {
        teacherId = params.teacherId
        timeSlots = params.timeSlots
      } else {
        // 兼容旧的调用方式
        teacherId = arguments[0]
        timeSlots = arguments[1]?.timeSlots || arguments[1]
      }

      const response = await teachingGroupApi.updateTeacherTimeSlots({ teacherId, timeSlots })

      if (response.code === 200) {
        ElMessage.success('更新教师时间表成功')
        return true
      } else {
        ElMessage.error(response.message || '更新教师时间表失败')
        return false
      }
    } catch (error) {
      console.error('更新教师时间表失败:', error)
      ElMessage.error('更新教师时间表失败')
      return false
    }
  }

  // 检查教师时间表最后更新时间
  const checkTeacherTimeSlotUpdateTime = async (teacherId) => {
    try {
      const response = await teachingGroupApi.checkTeacherTimeSlotUpdateTime(teacherId)

      if (response.code === 200) {
        return response.data
      } else {
        console.error('检查教师时间表更新时间失败:', response.message)
        return null
      }
    } catch (error) {
      console.error('检查教师时间表更新时间失败:', error)
      return null
    }
  }

  // 获取教师详细信息
  const fetchTeacherDetail = async (teacherId) => {
    try {
      console.log('正在获取教师详细信息, teacherId:', teacherId)
      const response = await teachingGroupApi.getTeacherDetail(teacherId)
      console.log('教师详细信息API响应:', response)

      if (response.code === 200) {
        console.log('教师详细信息数据:', response.data)
        console.log('新增统计字段检查:', {
          trialCoursePassRate: response.data?.trialCoursePassRate,
          totalTeachingHours: response.data?.totalTeachingHours,
          currentStudentCount: response.data?.currentStudentCount
        })
        return response.data
      } else {
        ElMessage.error(response.message || '获取教师详细信息失败')
        return null
      }
    } catch (error) {
      console.error('获取教师详细信息失败:', error)
      ElMessage.error('获取教师详细信息失败')
      return null
    }
  }

  // 获取教师带教信息
  const fetchTeachingInfo = async (teacherId) => {
    try {
      const response = await teachingGroupApi.getTeacherTeachingInfo({ teacherId })

      if (response.code === 200) {
        return response.data
      } else {
        ElMessage.error(response.message || '获取教师带教信息失败')
        return null
      }
    } catch (error) {
      console.error('获取教师带教信息失败:', error)
      ElMessage.error('获取教师带教信息失败')
      return null
    }
  }

  // 批量获取教师带教信息
  const fetchTeachingInfoBatch = async (teacherIds) => {
    try {
      const response = await teachingGroupApi.getTeacherTeachingInfoBatch(teacherIds)

      if (response.code === 200) {
        return response.data || []
      } else {
        ElMessage.error(response.message || '批量获取教师带教信息失败')
        return []
      }
    } catch (error) {
      console.error('批量获取教师带教信息失败:', error)
      ElMessage.error('批量获取教师带教信息失败')
      return []
    }
  }

  // 获取教师课表
  const fetchTeacherSchedule = async (teacherId, params = {}) => {
    try {
      const response = await teachingGroupApi.getTeacherSchedule(teacherId, params)

      if (response.code === 200) {
        return response.data
      } else {
        ElMessage.error(response.message || '获取教师课表失败')
        return []
      }
    } catch (error) {
      console.error('获取教师课表失败:', error)
      ElMessage.error('获取教师课表失败')
      return []
    }
  }

  // 创建教师
  const createTeacher = async (teacherData) => {
    try {
      const response = await teachingGroupApi.createTeacher(teacherData)

      if (response.code === 200) {
        ElMessage.success('创建教师成功')
        return response.data
      } else {
        ElMessage.error(response.message || '创建教师失败')
        return null
      }
    } catch (error) {
      console.error('创建教师失败:', error)
      ElMessage.error('创建教师失败')
      return null
    }
  }

  // 分配学生给教师
  const assignStudentsToTeacher = async (teacherId, studentAssignments) => {
    try {
      const response = await teachingGroupApi.assignStudentsToTeacher({
        teacherId,
        studentAssignments
      })

      if (response.code === 200) {
        ElMessage.success('分配学生成功')
        return true
      } else {
        ElMessage.error(response.message || '分配学生失败')
        return false
      }
    } catch (error) {
      console.error('分配学生失败:', error)
      ElMessage.error('分配学生失败')
      return false
    }
  }

  // 更新教师信息
  const updateTeacherInfo = async (teacherData) => {
    try {
      const response = await teachingGroupApi.updateTeacherInfo(teacherData.teacherId, teacherData)

      if (response.code === 200) {
        return true
      } else {
        ElMessage.error(response.message || '更新教师信息失败')
        return false
      }
    } catch (error) {
      console.error('更新教师信息失败:', error)
      ElMessage.error('更新教师信息失败')
      return false
    }
  }

  // 更新教师暑期课上课时间
  const updateTeacherSummerSchedule = async (teacherId, summerScheduleType) => {
    try {
      const response = await teachingGroupApi.updateTeacherSummerSchedule(teacherId, { summerScheduleType })

      if (response.code === 200) {
        ElMessage.success('暑期课上课时间更新成功')
        return true
      } else {
        ElMessage.error(response.message || '更新暑期课上课时间失败')
        return false
      }
    } catch (error) {
      console.error('更新暑期课上课时间失败:', error)
      ElMessage.error('更新暑期课上课时间失败')
      return false
    }
  }

  // 删除教师
  const deleteTeacher = async (teacherId) => {
    try {
      const response = await teachingGroupApi.deleteTeacher(teacherId)

      if (response.code === 200) {
        ElMessage.success('删除教师成功')
        return true
      } else {
        ElMessage.error(response.message || '删除教师失败')
        return false
      }
    } catch (error) {
      console.error('删除教师失败:', error)
      ElMessage.error('删除教师失败')
      return false
    }
  }

  // 批量删除教师
  const batchDeleteTeachers = async (teacherIds) => {
    try {
      const response = await teachingGroupApi.batchDeleteTeachers(teacherIds)

      if (response.code === 200) {
        ElMessage.success('批量删除教师成功')
        return true
      } else {
        ElMessage.error(response.message || '批量删除教师失败')
        return false
      }
    } catch (error) {
      console.error('批量删除教师失败:', error)
      ElMessage.error('批量删除教师失败')
      return false
    }
  }

  // 重置教师密码
  const resetTeacherPassword = async (teacherId) => {
    try {
      const response = await teachingGroupApi.resetTeacherPassword(teacherId)

      if (response.code === 200) {
        ElMessage.success('重置密码成功')
        return true
      } else {
        ElMessage.error(response.message || '重置密码失败')
        return false
      }
    } catch (error) {
      console.error('重置密码失败:', error)
      ElMessage.error('重置密码失败')
      return false
    }
  }

  // 导出教师列表
  const exportTeachers = async (params = {}) => {
    try {
      const blob = await teachingGroupApi.exportTeachers(params)

      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `教师列表_${new Date().toISOString().split('T')[0]}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      return true
    } catch (error) {
      console.error('导出教师列表失败:', error)
      ElMessage.error('导出教师列表失败')
      return false
    }
  }

  // 清空当前教学组
  const clearCurrentGroup = () => {
    currentGroup.value = null
    groupTeachers.value = []
  }

  return {
    // 状态
    teachingGroups,
    currentGroup,
    groupTeachers,
    availableTeachers,
    allTeachers,
    teachingGroupStats,
    loading,
    teachersLoading,

    // 计算属性
    activeGroups,
    inactiveGroups,

    // 方法
    fetchTeachingGroups,
    fetchTeachingGroup,
    createTeachingGroup,
    updateTeachingGroup,
    deleteTeachingGroup,
    deleteTeachingGroups,
    fetchGroupTeachers,
    assignTeachers,
    removeTeachers,
    fetchAvailableTeachers,
    fetchTeachers,
    fetchAllTeachers,
    fetchTeachingGroupStats,
    fetchTeacherTimeSlots,
    updateTeacherTimeSlots,
    checkTeacherTimeSlotUpdateTime,
    fetchTeacherDetail,
    fetchTeachingInfo,
    fetchTeachingInfoBatch,
    updateTeacherInfo,
    updateTeacherSummerSchedule,
    fetchTeacherSchedule,
    createTeacher,
    deleteTeacher,
    batchDeleteTeachers,
    resetTeacherPassword,
    exportTeachers,
    assignStudentsToTeacher,
    clearCurrentGroup
  }
})
