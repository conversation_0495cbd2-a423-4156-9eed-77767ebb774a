import vue from '@vitejs/plugin-vue'

import createAutoImport from './auto-import'
// import createSvgIcon from './svg-icon'  // 临时禁用
// import createCompression from './compression'  // 临时禁用
import createSetupExtend from './setup-extend'

export default function createVitePlugins(viteEnv, isBuild = false) {
    const vitePlugins = [vue()]
    vitePlugins.push(createAutoImport())
	vitePlugins.push(createSetupExtend())
    // 临时禁用svg-icon插件以解决启动问题
    // vitePlugins.push(createSvgIcon(isBuild))
	// isBuild && vitePlugins.push(...createCompression(viteEnv))  // 临时禁用
    return vitePlugins
}
