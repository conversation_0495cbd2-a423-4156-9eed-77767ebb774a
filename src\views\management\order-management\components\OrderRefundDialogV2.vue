<template>
  <el-dialog
    v-model="visible"
    title="订单申请退款"
    width="640px"
    :close-on-click-modal="false"
    @close="onClose"
  >
    <div v-if="orderInfo" class="refund-content">
      <el-card class="order-info-card" shadow="never" style="margin-bottom: 16px;">
        <template #header>
          <span>订单信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ orderInfo.no }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="statusType(orderInfo.orderStatus)">{{ orderInfo.orderStatus }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单总金额">¥{{ (orderInfo.totalAmt / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="已支付金额">¥{{ (orderInfo.amtPaid / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="未支付金额">¥{{ (orderInfo.amtUnpaid / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="学生姓名">{{ orderInfo.studentName }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="申请退款原因" prop="refundReason">
          <el-input
            v-model="form.refundReason"
            type="textarea"
            :rows="4"
            maxlength="200"
            show-word-limit
            placeholder="请输入申请退款原因"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onClose">取 消</el-button>
        <el-button type="primary" :loading="submitting" @click="onSubmit">{{ submitting ? '处理中...' : '确认申请' }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { refundApplyApi } from '@/api/management/order'

interface OrderInfo {
  id: string
  no: string
  orderStatus: string
  totalAmt: number
  amtPaid: number
  amtUnpaid: number
  studentName?: string
}

const props = defineProps<{ modelValue: boolean; orderInfo: OrderInfo | null }>()
const emit = defineEmits<{ (e: 'update:modelValue', v: boolean): void; (e: 'success'): void }>()

const visible = ref<boolean>(props.modelValue)
watch(
  () => props.modelValue,
  (v) => (visible.value = v)
)
watch(visible, (v) => emit('update:modelValue', v))

const submitting = ref(false)
const formRef = ref<FormInstance>()
const form = ref({ refundReason: '' })

const rules: FormRules = {
  refundReason: [
    { required: true, message: '请输入退款原因', trigger: 'blur' },
    { min: 5, max: 200, message: '退款原因长度在5到200个字符', trigger: 'blur' }
  ]
}

const statusType = (status: string) => {
  const map: Record<string, string> = {
    '未付款': 'warning',
    '已付款': 'success',
    '已全额支付': 'success',
    '已部分支付': 'primary',
    '已取消': 'danger',
    '已退款': 'info',
    '退款待审核': 'primary'
  }
  return map[status] || 'info'
}

const reset = () => {
  form.value.refundReason = ''
  formRef.value?.resetFields()
}

const onSubmit = async () => {
  await formRef.value?.validate(async (valid) => {
    if (!valid || !props.orderInfo) return
    try {
      submitting.value = true
      const payload = {
        orderId: props.orderInfo.id,
        refundReason: form.value.refundReason
      }
      const res: any = await refundApplyApi(props.orderInfo.id, payload)
      if (res.code === 200) {
        ElMessage.success('退款申请提交成功')
        emit('success')
        onClose()
      } else {
        ElMessage.error(res.msg || '退款申请失败')
      }
    } finally {
      submitting.value = false
    }
  })
}

const onClose = () => {
  visible.value = false
  reset()
}
</script>

<style scoped>
.refund-content { padding: 8px 0; }
.order-info-card { border-radius: 8px; }
.dialog-footer { text-align: right; }
</style>

