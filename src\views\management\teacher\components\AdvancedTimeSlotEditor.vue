<template>
  <div class="advanced-time-slot-editor">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <span class="title">可上课时间设置</span>
        <span class="subtitle">点击空白区域添加时间段，悬停已有时间段进行编辑</span>
      </div>
      <div class="toolbar-right">
        <!-- 外部传入的操作按钮 -->
        <slot name="actions"></slot>

        <el-button size="small" @click="clearAll" :disabled="readonly">
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
        <el-button size="small" type="primary" @click="showTemplateDialog = true" :disabled="readonly">
          <el-icon><Clock /></el-icon>
          模板
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：时间网格 -->
      <div class="time-grid-wrapper">
        <div class="time-grid" ref="timeGrid">
      <!-- 表头 -->
      <div class="grid-header">
        <div class="time-header">时间</div>
        <div
          v-for="day in weekDays"
          :key="day.value"
          class="day-header"
        >
          <div class="day-name">{{ day.label }}</div>
<!--          <div class="day-date">{{ getDayDate(day.value) }}</div>-->
        </div>
      </div>

      <!-- 网格主体 -->
      <div class="grid-body">
        <!-- 时间行 -->
        <div
          v-for="hour in hours"
          :key="hour"
          class="time-row"
          :style="{ height: `${HOUR_HEIGHT}px` }"
        >
          <!-- 时间标签 -->
          <div class="time-label">
            {{ formatHour(hour) }}
          </div>

          <!-- 每天的列 -->
          <div
            v-for="day in weekDays"
            :key="`${day.value}-${hour}`"
            class="day-column"
            :data-day="day.value"
            :data-hour="hour"
            @click="handleCellClick(day.value, hour)"
            @mouseenter="handleCellHover(day.value, hour, true)"
            @mouseleave="handleCellHover(day.value, hour, false)"
          >
            <!-- 时间段背景 -->
            <div class="time-slots-container">
              <!-- 已选择的时间段（只在开始小时显示完整时间段） -->
              <div
                v-for="slot in getCompleteTimeSlotsForCell(day.value, hour)"
                :key="slot.id"
                class="time-slot"
                :style="getCompleteSlotStyle(slot)"
                :title="`${slot.startTime} - ${slot.endTime}`"
              >
                <div class="slot-content">
                  <span class="slot-time">{{ slot.startTime }}-{{ slot.endTime }}</span>
                </div>
              </div>

              <!-- 悬停时的操作按钮 -->
              <div
                v-if="hoveredCell.weekday === day.value && hoveredCell.hour === hour && hasTimeSlotInHour(day.value, hour)"
                class="cell-actions"
              >
                <el-button size="small" type="primary" @click.stop="editTimeSlot(day.value, hour)">
                  编辑
                </el-button>
                <el-button size="small" type="danger" @click.stop="deleteTimeSlot(day.value, hour)">
                  删除
                </el-button>
              </div>
            </div>

            <!-- 5分钟网格线 -->
            <div class="minute-grid">
              <div
                v-for="minute in [15, 30, 45]"
                :key="minute"
                class="minute-line"
                :style="{ top: `${(minute / 60) * HOUR_HEIGHT}px` }"
              ></div>
            </div>
          </div>
        </div>
        </div>
      </div>
      </div>

      <!-- 右侧：时间段列表 -->
      <div class="time-slots-panel">
        <div class="panel-header">
          <span class="panel-title">已选择时间段</span>
          <span class="slots-count">({{ timeSlots.length }})</span>
        </div>

        <div class="slots-container">
          <div v-if="timeSlots.length === 0" class="empty-state">
            <div class="empty-icon">📅</div>
            <div class="empty-text">暂无时间段</div>
            <div class="empty-hint">拖拽左侧日历选择时间</div>
          </div>

          <div v-else class="slots-list">
            <div
              v-for="slot in sortedTimeSlots"
              :key="slot.id"
              class="slot-item"
            >
              <div class="slot-info">
                <div class="slot-day">{{ getWeekDayName(slot.weekday) }}</div>
                <div class="slot-time">{{ slot.startTime }} - {{ slot.endTime }}</div>
                <div class="slot-duration">{{ calculateDuration(slot.startTime, slot.endTime) }}</div>
              </div>
              <el-button
                type="danger"
                link
                size="small"
                @click="removeSlot(slot)"
                class="remove-btn"
              >
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 时间提示框 -->
    <div
      v-if="showTimeTooltip && currentHoverTime"
      class="time-tooltip"
      :style="{
        left: tooltipPosition.x + 'px',
        top: tooltipPosition.y + 'px'
      }"
    >
      <div class="tooltip-content">
        <div class="tooltip-day">{{ currentHoverTime.dayName }}</div>
        <div class="tooltip-time">{{ currentHoverTime.timeStr }}</div>
      </div>
    </div>

    <!-- 模板对话框 -->
    <el-dialog
      v-model="showTemplateDialog"
      title="选择时间模板"
      width="500px"
    >
      <div class="template-list">
        <div
          v-for="template in templates"
          :key="template.id"
          class="template-item"
          @click="applyTemplate(template)"
        >
          <div class="template-name">{{ template.name }}</div>
          <div class="template-description">{{ template.description }}</div>
        </div>
      </div>
    </el-dialog>

    <!-- 时间段输入对话框 -->
    <el-dialog
      v-model="showTimeSlotDialog"
      :title="editingTimeSlot ? '编辑时间段' : '添加时间段'"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="timeSlotFormRef"
        :model="currentTimeSlot"
        :rules="timeSlotRules"
        label-width="80px"
      >
        <el-form-item label="星期" prop="weekdays">
          <div class="weekday-selection">
            <!-- 选中的星期标签显示 -->
            <div class="selected-weekdays-display" v-if="currentTimeSlot.weekdays.length > 0">
              <div class="weekday-tags">
                <el-tag
                  v-for="weekday in currentTimeSlot.weekdays"
                  :key="weekday"
                  @close="removeWeekday(weekday)"
                  type="primary"
                  size="small"
                >
                  {{ getWeekDayName(weekday) }}
                </el-tag>
              </div>
            </div>

            <el-select
              v-model="currentTimeSlot.weekdays"
              placeholder="请选择星期（可多选）"
              style="width: 100%; margin-bottom: 8px;"
              multiple
              collapse-tags
              collapse-tags-tooltip
            >
              <el-option
                v-for="day in weekDays"
                :key="day.value"
                :label="day.label"
                :value="day.value"
              />
            </el-select>

            <!-- 快速选择按钮 -->
            <div class="quick-select-buttons">
              <el-button size="small" @click="selectWorkdays">工作日</el-button>
              <el-button size="small" @click="selectWeekend">周末</el-button>
              <el-button size="small" @click="selectAllDays">全选</el-button>
              <el-button size="small" @click="clearSelection">清空</el-button>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="开始时间" prop="startTime">
          <el-select
            v-model="currentTimeSlot.startTime"
            placeholder="选择开始时间"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="time in timeOptions"
              :key="time"
              :label="time"
              :value="time"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="结束时间" prop="endTime">
          <el-select
            v-model="currentTimeSlot.endTime"
            placeholder="选择结束时间"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="time in timeOptions"
              :key="time"
              :label="time"
              :value="time"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelTimeSlotEdit">取消</el-button>
          <el-button type="primary" @click="saveTimeSlot">
            {{ editingTimeSlot ? '更新' : '添加' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Clock, Close } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 常量
const HOUR_HEIGHT = 32 // 每小时的像素高度（减小以适应完整视图）
const MIN_SELECTION_HEIGHT = 5 // 最小选择高度（5分钟）

// 响应式数据
const timeSlots = ref([])
const showTemplateDialog = ref(false)
const timeGrid = ref()

// 选择状态
const isSelecting = ref(false)
const selectionStart = ref(null)
const selectionEnd = ref(null)
const selectionPreview = ref(null)

// 鼠标悬停时间提示
const currentHoverTime = ref(null)
const showTimeTooltip = ref(false)
const tooltipPosition = ref({ x: 0, y: 0 })

// 新的交互方式相关变量
const hoveredCell = ref({ weekday: null, hour: null })
const showTimeSlotDialog = ref(false)
const editingTimeSlot = ref(null)
const timeSlotFormRef = ref(null)
const currentTimeSlot = ref({
  weekdays: [],
  startTime: '',
  endTime: ''
})

// 常量数据
const weekDays = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 7 }
]

// 生成时间选项：从6:00到24:00，每5分钟一个间隔
const timeOptions = computed(() => {
  const options = []

  // 从6:00开始到23:55结束
  for (let hour = 6; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 5) {
      const timeStr = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
      options.push(timeStr)
    }
  }

  // 添加24:00作为结束时间选项
  options.push('24:00')

  return options
})

// 表单验证规则
const timeSlotRules = {
  weekdays: [
    {
      required: true,
      message: '请至少选择一个星期',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (!value || !Array.isArray(value) || value.length === 0) {
          callback(new Error('请至少选择一个星期'))
        } else {
          callback()
        }
      }
    }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    { validator: validateEndTime, trigger: 'change' }
  ]
}

// 验证结束时间
function validateEndTime(rule, value, callback) {
  if (!value) {
    callback(new Error('请选择结束时间'))
    return
  }

  if (!currentTimeSlot.value.startTime) {
    callback()
    return
  }

  // 将24:00转换为分钟数进行比较
  const startMinutes = timeToMinutes(currentTimeSlot.value.startTime)
  const endMinutes = value === '24:00' ? 24 * 60 : timeToMinutes(value)

  if (endMinutes <= startMinutes) {
    callback(new Error('结束时间必须大于开始时间'))
    return
  }

  // 检查时间段长度（至少5分钟）
  if (endMinutes - startMinutes < 5) {
    callback(new Error('时间段至少需要5分钟'))
    return
  }

  callback()
}

const hours = Array.from({ length: 18 }, (_, i) => i + 6) // 6-23点

const templates = [
  {
    id: 'workday',
    name: '工作日',
    description: '周一至周五，12:00-14:00，19:00-23:00',
    slots: [
      ...Array.from({ length: 5 }, (_, i) => [
        { weekday: i + 1, startTime: '12:00', endTime: '14:00' },
        { weekday: i + 1, startTime: '19:00', endTime: '23:00' }
      ]).flat()
    ]
  },
  {
    id: 'workday-night',
    name: '工作日晚间',
    description: '周一至周五，19:00-23:00',
    slots: [
      ...Array.from({ length: 5 }, (_, i) => [
        { weekday: i + 1, startTime: '19:00', endTime: '23:00' }
      ]).flat()
    ]
  },
  {
    id: 'weekend',
    name: '周末模板',
    description: '周六周日，8:00-23:00',
    slots: [
      { weekday: 6, startTime: '08:00', endTime: '23:00' },
      { weekday: 7, startTime: '08:00', endTime: '23:00' }
    ]
  },
  {
    id: 'fulltime',
    name: '全职模板',
    description: '周一至周五，12:00-14:00，19:00-23:00；周六周日，8:00-23:00',
    slots: [
      ...Array.from({ length: 5 }, (_, i) => [
        { weekday: i + 1, startTime: '12:00', endTime: '14:00' },
        { weekday: i + 1, startTime: '19:00', endTime: '23:00' }
      ]).flat(),
      { weekday: 6, startTime: '08:00', endTime: '23:00' },
      { weekday: 7, startTime: '08:00', endTime: '23:00' }
    ]
  }
]

// 计算属性
const sortedTimeSlots = computed(() => {
  return [...timeSlots.value].sort((a, b) => {
    if (a.weekday !== b.weekday) {
      // 周日(7)排在第一位，然后是周一(1)到周六(6)
      const aOrder = a.weekday === 7 ? 0 : a.weekday
      const bOrder = b.weekday === 7 ? 0 : b.weekday
      return aOrder - bOrder
    }
    return a.startTime.localeCompare(b.startTime)
  })
})

// 监听器
watch(() => props.modelValue, (newValue) => {
  // 防止死循环：只有当新值与当前值不同时才更新
  if (JSON.stringify(newValue) !== JSON.stringify(timeSlots.value)) {
    // 标准化时间格式，将 "HH:mm:ss" 转换为 "HH:mm"
    const normalizedSlots = (newValue || []).map(slot => ({
      ...slot,
      startTime: normalizeTimeFormat(slot.startTime),
      endTime: normalizeTimeFormat(slot.endTime)
    }))
    timeSlots.value = normalizedSlots
  }
}, { immediate: true, deep: true })

watch(timeSlots, (newValue) => {
  // 防止死循环：只有当新值与props.modelValue不同时才发出更新事件
  if (JSON.stringify(newValue) !== JSON.stringify(props.modelValue)) {
    emit('update:modelValue', newValue)
    emit('change', newValue)
  }
}, { deep: true })

// 方法
function formatHour(hour) {
  return `${hour.toString().padStart(2, '0')}:00`
}

function getDayDate(weekday) {
  const today = new Date()
  const currentDay = today.getDay()
  // 将我们系统的周日(7)转换为JavaScript的周日(0)
  const targetDay = weekday === 7 ? 0 : weekday
  const currentDayAdjusted = currentDay

  const diff = targetDay - currentDayAdjusted
  const targetDate = new Date(today)
  targetDate.setDate(today.getDate() + diff)

  return `${targetDate.getMonth() + 1}/${targetDate.getDate()}`
}

function getWeekDayName(weekday) {
  const day = weekDays.find(d => d.value === weekday)
  return day ? day.label : '未知'
}

function calculateDuration(startTime, endTime) {
  const start = timeToMinutes(startTime)
  const end = timeToMinutes(endTime)
  const duration = end - start

  const hours = Math.floor(duration / 60)
  const minutes = duration % 60

  if (hours > 0 && minutes > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (hours > 0) {
    return `${hours}小时`
  } else {
    return `${minutes}分钟`
  }
}

// 获取在指定小时开始的完整时间段（避免重复显示）
function getCompleteTimeSlotsForCell(weekday, hour) {
  // 防止空数组或无效数据导致问题
  if (!timeSlots.value || !Array.isArray(timeSlots.value)) {
    return []
  }

  return timeSlots.value.filter(slot => {
    // 验证时间段数据的完整性
    if (!slot || slot.weekday !== weekday || !slot.startTime) {
      return false
    }

    const slotStart = timeToMinutes(slot.startTime)
    const hourStart = hour * 60

    // 只在时间段开始的小时显示完整时间段
    return slotStart >= hourStart && slotStart < hourStart + 60
  })
}

// 保留原方法用于选择预览
function getTimeSlotsForCell(weekday, hour) {
  return timeSlots.value.filter(slot => {
    if (slot.weekday !== weekday) return false

    const slotStart = timeToMinutes(slot.startTime)
    const slotEnd = timeToMinutes(slot.endTime)
    const hourStart = hour * 60
    const hourEnd = (hour + 1) * 60

    // 检查时间段是否与这个小时重叠
    return slotStart < hourEnd && slotEnd > hourStart
  })
}

// 获取完整时间段的样式（跨越多个小时）
function getCompleteSlotStyle(slot) {
  const slotStart = timeToMinutes(slot.startTime)
  const slotEnd = timeToMinutes(slot.endTime)

  // 计算开始小时
  const startHour = Math.floor(slotStart / 60)
  const startHourStart = startHour * 60

  // 计算相对于开始小时的位置
  const top = ((slotStart - startHourStart) / 60) * HOUR_HEIGHT

  // 计算总高度（跨越的总分钟数）
  const totalMinutes = slotEnd - slotStart
  const height = (totalMinutes / 60) * HOUR_HEIGHT

  return {
    position: 'absolute',
    top: `${top}px`,
    height: `${height}px`,
    left: '2px',
    right: '2px',
    backgroundColor: '#67c23a',
    borderRadius: '4px',
    cursor: 'pointer',
    transition: 'all 0.2s',
    zIndex: 2,
    border: '1px solid #5daf34'
  }
}

function getSlotStyle(slot, hour) {
  const slotStart = timeToMinutes(slot.startTime)
  const slotEnd = timeToMinutes(slot.endTime)
  const hourStart = hour * 60
  const hourEnd = (hour + 1) * 60

  // 计算在当前小时内的位置和高度
  const visibleStart = Math.max(slotStart, hourStart)
  const visibleEnd = Math.min(slotEnd, hourEnd)

  const top = ((visibleStart - hourStart) / 60) * HOUR_HEIGHT
  const height = ((visibleEnd - visibleStart) / 60) * HOUR_HEIGHT

  return {
    position: 'absolute',
    top: `${top}px`,
    height: `${height}px`,
    left: '2px',
    right: '2px',
    backgroundColor: '#67c23a',
    borderRadius: '3px',
    cursor: 'pointer',
    transition: 'all 0.2s',
    zIndex: 2
  }
}

// 检查指定天是否有选择预览
function hasSelectionPreviewForDay(weekday) {
  if (!selectionPreview.value) return false

  // 如果是数组（跨天选择），检查是否包含该天
  if (Array.isArray(selectionPreview.value)) {
    return selectionPreview.value.some(preview => preview.weekday === weekday)
  }

  // 如果是单个对象（同天选择），检查是否匹配
  return selectionPreview.value.weekday === weekday
}

// 获取指定天的选择样式
function getSelectionStyleForDay(weekday, hour) {
  if (!selectionPreview.value) return {}

  let preview = null

  // 如果是数组（跨天选择），找到对应天的预览
  if (Array.isArray(selectionPreview.value)) {
    preview = selectionPreview.value.find(p => p.weekday === weekday)
  } else if (selectionPreview.value.weekday === weekday) {
    // 如果是单个对象（同天选择）
    preview = selectionPreview.value
  }

  if (!preview) return { display: 'none' }

  const start = timeToMinutes(preview.startTime)
  const end = timeToMinutes(preview.endTime)
  const hourStart = hour * 60
  const hourEnd = (hour + 1) * 60

  // 计算在当前小时内的位置和高度
  const visibleStart = Math.max(start, hourStart)
  const visibleEnd = Math.min(end, hourEnd)

  if (visibleStart >= visibleEnd) return { display: 'none' }

  const top = ((visibleStart - hourStart) / 60) * HOUR_HEIGHT
  const height = ((visibleEnd - visibleStart) / 60) * HOUR_HEIGHT

  return {
    position: 'absolute',
    top: `${top}px`,
    height: `${height}px`,
    left: '2px',
    right: '2px',
    backgroundColor: '#409eff',
    borderRadius: '3px',
    opacity: 0.7,
    zIndex: 1,
    border: preview.isCrossDayPart ? '2px dashed #409eff' : 'none'
  }
}

function getSelectionStyle(hour) {
  if (!selectionPreview.value) return {}

  const start = timeToMinutes(selectionPreview.value.startTime)
  const end = timeToMinutes(selectionPreview.value.endTime)
  const hourStart = hour * 60
  const hourEnd = (hour + 1) * 60

  // 计算在当前小时内的位置和高度
  const visibleStart = Math.max(start, hourStart)
  const visibleEnd = Math.min(end, hourEnd)

  if (visibleStart >= visibleEnd) return { display: 'none' }

  const top = ((visibleStart - hourStart) / 60) * HOUR_HEIGHT
  const height = ((visibleEnd - visibleStart) / 60) * HOUR_HEIGHT

  return {
    position: 'absolute',
    top: `${top}px`,
    height: `${height}px`,
    left: '2px',
    right: '2px',
    backgroundColor: '#409eff',
    borderRadius: '3px',
    opacity: 0.7,
    zIndex: 1
  }
}

function timeToMinutes(timeStr) {
  // 防止空值或无效时间格式导致错误
  if (!timeStr || typeof timeStr !== 'string') {
    console.warn('Invalid time string:', timeStr)
    return 0
  }

  const parts = timeStr.split(':')
  // 兼容 "HH:mm" 和 "HH:mm:ss" 两种格式
  if (parts.length < 2 || parts.length > 3) {
    console.warn('Invalid time format:', timeStr)
    return 0
  }

  const [hours, minutes] = parts.map(Number)

  // 验证时间值的有效性
  if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
    console.warn('Invalid time values:', timeStr, { hours, minutes })
    return 0
  }

  return hours * 60 + minutes
}

function minutesToTime(minutes) {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
}

// 标准化时间格式，将 "HH:mm:ss" 转换为 "HH:mm"
function normalizeTimeFormat(timeStr) {
  if (!timeStr || typeof timeStr !== 'string') {
    return timeStr
  }

  const parts = timeStr.split(':')
  if (parts.length >= 2) {
    // 只取小时和分钟部分，忽略秒
    const hours = parts[0].padStart(2, '0')
    const minutes = parts[1].padStart(2, '0')
    return `${hours}:${minutes}`
  }

  return timeStr
}

function getPositionFromEvent(event) {
  const gridBody = timeGrid.value.querySelector('.grid-body')
  const gridBodyRect = gridBody.getBoundingClientRect()

  const x = event.clientX - gridBodyRect.left
  const y = event.clientY - gridBodyRect.top + gridBody.scrollTop

  // 计算是哪一天
  const timeColumnWidth = 60
  const dayWidth = (gridBodyRect.width - timeColumnWidth) / weekDays.length
  const dayIndex = Math.floor((x - timeColumnWidth) / dayWidth)

  if (dayIndex < 0 || dayIndex >= weekDays.length) return null

  const weekday = weekDays[dayIndex].value

  // 计算时间（精确到5分钟）
  const totalMinutes = Math.floor((y / HOUR_HEIGHT) * 60 / 5) * 5 + 6 * 60 // 从6点开始

  return {
    weekday,
    minutes: totalMinutes
  }
}

function handleMouseDown(event) {
  if (props.readonly) return

  const position = getPositionFromEvent(event)
  if (!position) return

  isSelecting.value = true
  selectionStart.value = position
  selectionEnd.value = position

  updateSelectionPreview()

  event.preventDefault()
}

function handleMouseMove(event) {
  // 更新时间提示
  updateTimeTooltip(event)

  if (!isSelecting.value || props.readonly) return

  const position = getPositionFromEvent(event)
  if (!position) return

  // 支持跨天拖拽，移除weekday限制
  selectionEnd.value = position
  updateSelectionPreview()
}

function handleMouseUp(event) {
  if (!isSelecting.value) return

  if (selectionStart.value && selectionEnd.value) {
    createTimeSlot()
  }

  isSelecting.value = false
  selectionStart.value = null
  selectionEnd.value = null
  selectionPreview.value = null
}

function handleMouseEnter(event) {
  showTimeTooltip.value = true
  updateTimeTooltip(event)
}

function handleMouseLeave(event) {
  showTimeTooltip.value = false
  currentHoverTime.value = null

  // 如果正在选择，结束选择
  if (isSelecting.value) {
    handleMouseUp(event)
  }
}

// 新的交互方法
function handleCellClick(weekday, hour) {
  if (props.readonly) return

  if (hasTimeSlotInHour(weekday, hour)) {
    // 如果已有时间段，不做任何操作（通过悬停显示编辑/删除按钮）
    return
  } else {
    // 空白区域，显示添加时间段对话框
    showAddTimeSlotDialog(weekday, hour)
  }
}

function handleCellHover(weekday, hour, isEntering) {
  if (props.readonly) return

  if (isEntering) {
    hoveredCell.value = { weekday, hour }
  } else {
    hoveredCell.value = { weekday: null, hour: null }
  }
}

function showAddTimeSlotDialog(weekday, hour) {
  editingTimeSlot.value = null

  // 确保开始时间不早于6:00
  const startHour = Math.max(hour, 6)
  const endHour = Math.min(startHour + 1, 24)

  currentTimeSlot.value = {
    weekdays: [weekday], // 默认选中当前点击的星期
    startTime: `${startHour.toString().padStart(2, '0')}:00`,
    endTime: endHour === 24 ? '24:00' : `${endHour.toString().padStart(2, '0')}:00`
  }
  showTimeSlotDialog.value = true
}

function editTimeSlot(weekday, hour) {
  // 找到这个位置的时间段
  const slot = timeSlots.value.find(slot => {
    if (slot.weekday !== weekday) return false
    const slotStart = timeToMinutes(slot.startTime)
    const slotEnd = timeToMinutes(slot.endTime)
    const hourStart = hour * 60
    const hourEnd = (hour + 1) * 60
    return slotStart < hourEnd && hourStart < slotEnd
  })

  if (slot) {
    editingTimeSlot.value = slot
    currentTimeSlot.value = {
      weekdays: [slot.weekday], // 编辑时只显示当前时间段的星期
      startTime: slot.startTime,
      endTime: slot.endTime
    }
    showTimeSlotDialog.value = true
  }
}

function deleteTimeSlot(weekday, hour) {
  ElMessageBox.confirm(
    '确定要删除这个时间段吗？',
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    removeHourTimeSlots(weekday, hour)
    ElMessage.success('时间段已删除')
  }).catch(() => {
    // 用户取消
  })
}

function saveTimeSlot() {
  if (!timeSlotFormRef.value) return

  timeSlotFormRef.value.validate((valid) => {
    if (!valid) return

    let { weekdays, startTime, endTime } = currentTimeSlot.value

    // 将24:00转换为23:59以避免数据库约束问题
    if (endTime === '24:00') {
      endTime = '23:59'
    }

    // 验证时间段
    const validation = validateTimeSlot(startTime, endTime)
    if (!validation.valid) {
      ElMessage.error(validation.message)
      return
    }

    if (editingTimeSlot.value) {
      // 编辑现有时间段
      const index = timeSlots.value.findIndex(slot => slot.id === editingTimeSlot.value.id)
      if (index >= 0) {
        // 先移除原时间段
        timeSlots.value.splice(index, 1)
      }
    }

    // 为每个选中的星期创建时间段
    const newSlots = []
    weekdays.forEach(weekday => {
      const newSlot = {
        id: editingTimeSlot.value && weekdays.length === 1
          ? editingTimeSlot.value.id
          : `slot_${Date.now()}_${weekday}_${Math.random().toString(36).substring(2, 11)}`,
        weekday,
        startTime,
        endTime,
        status: 'available'
      }
      newSlots.push(newSlot)
    })

    // 为每个新时间段执行合并
    newSlots.forEach(slot => {
      mergeTimeSlots(slot)
    })

    showTimeSlotDialog.value = false
    const message = editingTimeSlot.value
      ? '时间段已更新'
      : `已为 ${weekdays.length} 个星期添加时间段`
    ElMessage.success(message)
  })
}

function cancelTimeSlotEdit() {
  showTimeSlotDialog.value = false
  editingTimeSlot.value = null
  currentTimeSlot.value = {
    weekdays: [],
    startTime: '',
    endTime: ''
  }
}

// 快速选择方法
function selectWorkdays() {
  currentTimeSlot.value.weekdays = [1, 2, 3, 4, 5] // 周一到周五
}

function selectWeekend() {
  currentTimeSlot.value.weekdays = [6, 7] // 周六、周日
}

function selectAllDays() {
  currentTimeSlot.value.weekdays = [1, 2, 3, 4, 5, 6, 7] // 全部
}

function clearSelection() {
  currentTimeSlot.value.weekdays = []
}

// 移除单个星期
function removeWeekday(weekday) {
  const index = currentTimeSlot.value.weekdays.indexOf(weekday)
  if (index > -1) {
    currentTimeSlot.value.weekdays.splice(index, 1)
  }
}

// 验证时间段数据
function validateTimeSlot(startTime, endTime) {
  if (!startTime || !endTime) {
    return { valid: false, message: '开始时间和结束时间不能为空' }
  }

  const startMinutes = timeToMinutes(startTime)
  const endMinutes = endTime === '24:00' ? 24 * 60 : timeToMinutes(endTime)

  if (startMinutes >= endMinutes) {
    return { valid: false, message: '开始时间必须小于结束时间' }
  }

  if (endMinutes - startMinutes < 5) {
    return { valid: false, message: '时间段至少需要5分钟' }
  }

  return { valid: true }
}

// 检查指定小时是否有时间段
function hasTimeSlotInHour(weekday, hour) {
  return timeSlots.value.some(slot => {
    if (slot.weekday !== weekday) return false
    const slotStart = timeToMinutes(slot.startTime)
    const slotEnd = timeToMinutes(slot.endTime)
    const hourStart = hour * 60
    const hourEnd = (hour + 1) * 60
    return slotStart < hourEnd && hourStart < slotEnd
  })
}

// 移除指定小时内的时间段
function removeHourTimeSlots(weekday, hour) {
  timeSlots.value = timeSlots.value.filter(slot => {
    if (slot.weekday !== weekday) return true
    const slotStart = timeToMinutes(slot.startTime)
    const slotEnd = timeToMinutes(slot.endTime)
    const hourStart = hour * 60
    const hourEnd = (hour + 1) * 60
    return !(slotStart < hourEnd && hourStart < slotEnd)
  })

  // 触发更新
  emit('update:modelValue', timeSlots.value)
  emit('change', timeSlots.value)
}

function updateTimeTooltip(event) {
  if (!showTimeTooltip.value) return

  const position = getPositionFromEvent(event)
  if (!position) return

  // 更新提示框位置
  tooltipPosition.value = {
    x: event.clientX + 10,
    y: event.clientY - 10
  }

  // 计算显示的时间：如果正在拖动，显示结束时间点（下一个5分钟格子）
  let displayMinutes = position.minutes
  if (isSelecting.value) {
    // 拖动时显示结束时间点（当前位置+5分钟）
    displayMinutes = position.minutes + 5
  }

  // 更新时间信息
  const dayName = getWeekDayName(position.weekday)
  const timeStr = minutesToTime(displayMinutes)

  currentHoverTime.value = {
    dayName,
    timeStr,
    weekday: position.weekday,
    minutes: displayMinutes
  }
}

function updateSelectionPreview() {
  if (!selectionStart.value || !selectionEnd.value) return

  // 支持跨天选择
  const startWeekday = selectionStart.value.weekday
  const endWeekday = selectionEnd.value.weekday
  const startMinutes = selectionStart.value.minutes
  const endMinutes = selectionEnd.value.minutes

  // 如果是跨天选择，需要创建多个时间段预览
  if (startWeekday !== endWeekday) {
    // 跨天选择：创建多个连续天的时间段
    selectionPreview.value = createCrossDayPreview(startWeekday, endWeekday, startMinutes, endMinutes)
  } else {
    // 同一天选择
    const minMinutes = Math.min(startMinutes, endMinutes)
    const maxMinutes = Math.max(startMinutes, endMinutes) + 5 // 至少5分钟

    selectionPreview.value = {
      weekday: startWeekday,
      startTime: minutesToTime(minMinutes),
      endTime: minutesToTime(maxMinutes)
    }
  }
}

function createCrossDayPreview(startWeekday, endWeekday, startMinutes, endMinutes) {
  // 计算时间段范围：从开始时间到结束时间
  const timeStartMinutes = Math.min(startMinutes, endMinutes)
  const timeEndMinutes = Math.max(startMinutes, endMinutes) + 5 // 至少5分钟

  // 计算涉及的天数范围：从开始天到结束天
  const weekdayStartDay = Math.min(startWeekday, endWeekday)
  const weekdayEndDay = Math.max(startWeekday, endWeekday)

  const previews = []

  // 生成涉及的天数列表
  const weekdaysToProcess = getWeekdayRange(weekdayStartDay, weekdayEndDay)

  // 为每一天创建相同的时间段
  weekdaysToProcess.forEach(weekday => {
    previews.push({
      weekday: weekday,
      startTime: minutesToTime(timeStartMinutes),
      endTime: minutesToTime(timeEndMinutes),
      isCrossDayPart: true
    })
  })

  return previews
}

function getWeekdayRange(startWeekday, endWeekday) {
  const weekdays = []

  if (startWeekday === endWeekday) {
    // 同一天
    weekdays.push(startWeekday)
  } else {
    // 需要处理周日是7的情况
    // 将周日(7)转换为0来进行比较，但保持原值存储
    const startDay = startWeekday === 7 ? 0 : startWeekday
    const endDay = endWeekday === 7 ? 0 : endWeekday

    if (startDay < endDay || (startWeekday !== 7 && endWeekday !== 7 && startWeekday < endWeekday)) {
      // 正常情况：不跨周，比如周一到周三，或周一到周六
      for (let day = startWeekday; day <= endWeekday; day++) {
        weekdays.push(day)
      }
    } else {
      // 跨周情况：比如周六到周一，或周日到周二
      if (startWeekday === 7) {
        // 从周日开始
        weekdays.push(7)
        for (let day = 1; day <= endWeekday; day++) {
          weekdays.push(day)
        }
      } else if (endWeekday === 7) {
        // 到周日结束
        for (let day = startWeekday; day <= 6; day++) {
          weekdays.push(day)
        }
        weekdays.push(7)
      } else {
        // 普通跨周：比如周六到周一
        for (let day = startWeekday; day <= 6; day++) {
          weekdays.push(day)
        }
        weekdays.push(7)
        for (let day = 1; day <= endWeekday; day++) {
          weekdays.push(day)
        }
      }
    }
  }

  return weekdays
}

function createTimeSlot() {
  if (!selectionPreview.value) return

  // 如果是跨天选择（数组），创建多个时间段
  if (Array.isArray(selectionPreview.value)) {
    const timestamp = Date.now()
    selectionPreview.value.forEach((preview, index) => {
      const newSlot = {
        id: `slot_${timestamp}_${index}`,
        weekday: preview.weekday,
        startTime: preview.startTime,
        endTime: preview.endTime,
        status: 'available'
      }
      mergeTimeSlots(newSlot)
    })
  } else {
    // 单天选择
    const newSlot = {
      id: `slot_${Date.now()}`,
      weekday: selectionPreview.value.weekday,
      startTime: selectionPreview.value.startTime,
      endTime: selectionPreview.value.endTime,
      status: 'available'
    }
    mergeTimeSlots(newSlot)
  }
}

function mergeTimeSlots(newSlot) {
  // 移除与新时间段重叠的现有时间段
  const nonOverlappingSlots = timeSlots.value.filter(slot => {
    if (slot.weekday !== newSlot.weekday) return true

    const slotStart = timeToMinutes(slot.startTime)
    const slotEnd = timeToMinutes(slot.endTime)
    const newStart = timeToMinutes(newSlot.startTime)
    const newEnd = timeToMinutes(newSlot.endTime)

    // 检查是否重叠
    return !(slotStart < newEnd && newStart < slotEnd)
  })

  // 添加新时间段
  nonOverlappingSlots.push(newSlot)

  // 合并相邻的时间段
  const sameDaySlots = nonOverlappingSlots.filter(slot => slot.weekday === newSlot.weekday)
  const otherDaySlots = nonOverlappingSlots.filter(slot => slot.weekday !== newSlot.weekday)

  // 按开始时间排序
  sameDaySlots.sort((a, b) => timeToMinutes(a.startTime) - timeToMinutes(b.startTime))

  // 合并相邻时间段
  const merged = []
  for (const slot of sameDaySlots) {
    if (merged.length === 0) {
      merged.push(slot)
    } else {
      const lastSlot = merged[merged.length - 1]
      const lastEnd = timeToMinutes(lastSlot.endTime)
      const currentStart = timeToMinutes(slot.startTime)

      if (lastEnd >= currentStart) {
        // 合并时间段
        const currentEnd = timeToMinutes(slot.endTime)
        lastSlot.endTime = minutesToTime(Math.max(lastEnd, currentEnd))
        lastSlot.id = `slot_${Date.now()}_merged`
      } else {
        merged.push(slot)
      }
    }
  }

  timeSlots.value = [...otherDaySlots, ...merged]
}

function removeSlot(slot) {
  const index = timeSlots.value.findIndex(s => s.id === slot.id)
  if (index >= 0) {
    timeSlots.value.splice(index, 1)
  }
}

function clearAll() {
  if (timeSlots.value.length === 0) {
    ElMessage.info('没有时间段需要清空')
    return
  }

  ElMessageBox.confirm(
    '确定要清空所有时间段吗？',
    '确认清空',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    timeSlots.value = []
    ElMessage.success('已清空所有时间段')
  }).catch(() => {
    // 用户取消
  })
}

function applyTemplate(template) {
  timeSlots.value = template.slots.map((slot, index) => ({
    ...slot,
    id: `template_${template.id}_${index}`,
    status: 'available'
  }))

  showTemplateDialog.value = false
  ElMessage.success(`已应用${template.name}`)
}

// 生命周期
onMounted(() => {
  // 防止页面滚动时的选择问题
  document.addEventListener('selectstart', (e) => {
    if (isSelecting.value) {
      e.preventDefault()
    }
  })
})

onUnmounted(() => {
  document.removeEventListener('selectstart', () => {})
})
</script>

<style lang="scss" scoped>
.advanced-time-slot-editor {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 12px;

    .toolbar-left {
      .title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .subtitle {
        font-size: 12px;
        color: #909399;
        margin-left: 8px;
      }
    }

    .toolbar-right {
      display: flex;
      gap: 8px;
    }
  }

  .main-content {
    display: flex;
    gap: 16px;
    align-items: flex-start;

    .time-grid-wrapper {
      flex: 1;
      min-width: 0; // 防止flex子项溢出
    }

    .time-slots-panel {
      width: 280px;
      flex-shrink: 0;
      background-color: #fafbfc;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      overflow: hidden;

      .panel-header {
        padding: 12px 16px;
        background-color: #f0f2f5;
        border-bottom: 1px solid #e4e7ed;
        display: flex;
        align-items: center;
        gap: 8px;

        .panel-title {
          font-weight: 600;
          color: #303133;
          font-size: 14px;
        }

        .slots-count {
          color: #909399;
          font-size: 12px;
        }
      }

      .slots-container {
        max-height: 576px; // 与时间网格高度一致
        overflow-y: auto;

        .empty-state {
          padding: 40px 20px;
          text-align: center;
          color: #909399;

          .empty-icon {
            font-size: 32px;
            margin-bottom: 8px;
          }

          .empty-text {
            font-size: 14px;
            margin-bottom: 4px;
            color: #606266;
          }

          .empty-hint {
            font-size: 12px;
            color: #c0c4cc;
          }
        }

        .slots-list {
          padding: 8px;

          .slot-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            margin-bottom: 6px;
            background-color: #fff;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            transition: all 0.2s;

            &:hover {
              border-color: #409eff;
              box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
            }

            &:last-child {
              margin-bottom: 0;
            }

            .slot-info {
              flex: 1;

              .slot-day {
                font-weight: 600;
                color: #303133;
                font-size: 13px;
                margin-bottom: 2px;
              }

              .slot-time {
                color: #606266;
                font-size: 12px;
                margin-bottom: 1px;
                word-break: break-all;
                line-height: 1.3;
              }

              .slot-duration {
                color: #909399;
                font-size: 11px;
              }
            }

            .remove-btn {
              margin-left: 8px;
              color: #f56c6c;

              &:hover {
                color: #f56c6c;
                background-color: rgba(245, 108, 108, 0.1);
              }
            }
          }
        }
      }
    }
  }

  .time-grid {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    overflow: hidden;
    background-color: #fff;
    user-select: none;

    .grid-header {
      display: flex;
      background-color: #f8f9fa;
      border-bottom: 2px solid #e4e7ed;

      .time-header {
        width: 60px;
        padding: 8px 4px;
        border-right: 1px solid #e4e7ed;
        font-weight: 600;
        text-align: center;
        background-color: #f0f2f5;
        font-size: 12px;
      }

      .day-header {
        flex: 1;
        padding: 6px 4px;
        text-align: center;
        border-right: 1px solid #e4e7ed;

        &:last-child {
          border-right: none;
        }

        .day-name {
          font-weight: 600;
          color: #303133;
          margin-bottom: 1px;
          font-size: 13px;
        }

        .day-date {
          font-size: 10px;
          color: #909399;
        }
      }
    }

    .grid-body {
      position: relative;
      // 移除滚动，显示完整日历

      .time-row {
        display: flex;
        border-bottom: 1px solid #f0f0f0;
        position: relative;

        &:last-child {
          border-bottom: none;
        }

        .time-label {
          width: 60px;
          padding: 4px;
          border-right: 1px solid #e4e7ed;
          font-size: 11px;
          color: #909399;
          display: flex;
          align-items: flex-start;
          justify-content: center;
          background-color: #fafbfc;
          font-weight: 500;
        }

        .day-column {
          flex: 1;
          border-right: 1px solid #f0f0f0;
          position: relative;
          cursor: crosshair;

          &:last-child {
            border-right: none;
          }

          &:hover {
            background-color: rgba(64, 158, 255, 0.05);
          }

          .time-slots-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;

            .cell-actions {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              display: flex;
              gap: 4px;
              z-index: 10;
              background: rgba(255, 255, 255, 0.95);
              padding: 2px;
              border-radius: 4px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

              .el-button {
                padding: 2px 6px;
                font-size: 12px;
                height: auto;
                min-height: 20px;
              }
            }
          }

          .minute-grid {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;

            .minute-line {
              position: absolute;
              left: 0;
              right: 0;
              height: 1px;
              background-color: rgba(0, 0, 0, 0.05);
            }
          }

          .time-slot {
            .slot-content {
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 11px;
              font-weight: 500;
              text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

              .slot-time {
                word-break: break-all;
                line-height: 1.2;
                text-align: center;
              }
            }

            &:hover {
              background-color: #5daf34 !important;
              transform: scale(1.02);
              box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
            }
          }

          .selection-preview {
            border: 2px dashed #409eff;
            background-color: rgba(64, 158, 255, 0.1);
          }
        }
      }
    }
  }

  .template-list {
    .template-item {
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
      }

      .template-name {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }

      .template-description {
        font-size: 14px;
        color: #606266;
      }
    }
  }

  // 时间提示框样式
  .time-tooltip {
    position: fixed;
    z-index: 9999;
    pointer-events: none;
    transform: translateX(-50%);

    .tooltip-content {
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 6px 10px;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

      .tooltip-day {
        font-weight: 600;
        margin-bottom: 2px;
        text-align: center;
      }

      .tooltip-time {
        font-family: 'Courier New', monospace;
        text-align: center;
        color: #67c23a;
      }
    }
  }

  // 星期选择相关样式
  .weekday-selection {
    .selected-weekdays-display {
      display: flex;
      align-items: flex-start;
      margin-bottom: 12px;
      padding: 8px;
      background: #f6f8fa;
      border-radius: 4px;
      border: 1px solid #e1e4e8;

      .selected-label {
        font-size: 12px;
        color: #666;
        margin-right: 8px;
        margin-top: 2px;
        white-space: nowrap;
        font-weight: 500;
      }

      .weekday-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        flex: 1;

        .el-tag {
          margin: 0;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }

    .quick-select-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;

      .el-button {
        font-size: 12px;
        padding: 4px 8px;
        height: auto;
      }
    }
  }

  // 对话框样式优化
  :deep(.el-dialog__body) {
    padding: 20px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  :deep(.el-select .el-tag) {
    margin: 2px;
  }
}
</style>
