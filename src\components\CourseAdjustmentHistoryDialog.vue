<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1400px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 内嵌独立查询页面 -->
    <CourseAdjustmentHistoryQuery
      v-if="visible"
      :key="queryKey"
      :course-hours-id="courseHoursId"
      :student-id="studentInfo?.studentId"
      :student-name="studentInfo?.studentName"
      :student-phone="studentInfo?.studentPhone"
      :subject="studentInfo?.subject"
      :specification="studentInfo?.specification"
      :is-dialog-mode="true"
    />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, watch, ref } from 'vue'
import CourseAdjustmentHistoryQuery from '@/views/management/course-adjustment-history/index.vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  studentInfo: {
    type: Object,
    default: null
  },
  courseHoursId: {
    type: String,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const dialogTitle = computed(() => {
  return props.studentInfo
    ? `调整历史 - ${props.studentInfo.studentName} (${props.studentInfo.subject} - ${props.studentInfo.specification})`
    : '调整历史'
})

// 查询组件的key，用于强制重新渲染
const queryKey = ref(0)

// 监听关键参数变化，强制重新渲染查询组件
watch([() => props.courseHoursId, () => props.studentInfo?.id], () => {
  if (props.visible) {
    queryKey.value++
  }
}, { immediate: false })

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

/* 对话框内容样式调整 */
:deep(.el-dialog__body) {
  padding: 10px 20px;
}

/* 隐藏内嵌页面的卡片边框 */
:deep(.box-card) {
  border: none;
  box-shadow: none;
}

:deep(.box-card .el-card__header) {
  display: none;
}

:deep(.box-card .el-card__body) {
  padding: 0;
}
</style>
