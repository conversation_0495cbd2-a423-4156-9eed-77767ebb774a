<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${group?.groupName || ''} - 趋势分析`"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="group-trend-container">
      <!-- 趋势概览 -->
      <el-card class="trend-overview-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>趋势概览</span>
            <div class="date-range-info">
              {{ dateRange.startDate }} 至 {{ dateRange.endDate }}
            </div>
          </div>
        </template>

        <div class="overview-stats">
          <div class="stat-item">
            <div class="stat-icon total">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ trendData.totalSlots || 0 }}</div>
              <div class="stat-label">总课次</div>
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-icon average">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ trendData.averageSlots || 0 }}</div>
              <div class="stat-label">日均课次</div>
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-icon peak">
              <el-icon><Top /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ trendData.peakSlots || 0 }}</div>
              <div class="stat-label">峰值课次</div>
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-icon teachers">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ trendData.teacherCount || 0 }}</div>
              <div class="stat-label">教师数量</div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 趋势图表 -->
      <el-card class="trend-chart-card">
        <template #header>
          <div class="card-header">
            <span>课次趋势图</span>
            <div class="chart-controls">
              <el-radio-group v-model="chartType" size="small">
                <el-radio-button value="line">折线图</el-radio-button>
                <el-radio-button value="bar">柱状图</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>

        <div ref="trendChart" class="trend-chart"></div>
      </el-card>

      <!-- 详细数据表格 -->
      <el-card class="trend-table-card">
        <template #header>
          <div class="card-header">
            <span>详细数据</span>
            <el-button type="text" size="small" @click="exportTrendData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
        </template>

        <el-table
          :data="trendData.dailyData || []"
          stripe
          style="width: 100%"
          max-height="300"
        >
          <el-table-column prop="date" label="日期" width="120" />
          <el-table-column prop="weekdayName" label="星期" width="80" />
          <el-table-column prop="availableSlots" label="可排课课次" width="120" sortable>
            <template #default="{ row }">
              <el-tag :type="getSlotsTagType(row.availableSlots)" size="small">
                {{ row.availableSlots }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="availableTeachers" label="有课时教师" width="120" />
          <el-table-column prop="averageSlots" label="平均课次" width="120">
            <template #default="{ row }">
              {{ row.averageSlots?.toFixed(1) || '0.0' }}
            </template>
          </el-table-column>
          <el-table-column prop="utilization" label="利用率" width="100">
            <template #default="{ row }">
              <el-progress
                :percentage="row.utilization || 0"
                :stroke-width="8"
                :show-text="false"
                :color="getUtilizationColor(row.utilization)"
              />
              <span style="margin-left: 8px; font-size: 12px;">
                {{ row.utilization?.toFixed(1) || '0.0' }}%
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="exportChart">
          <el-icon><Camera /></el-icon>
          导出图表
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { TrendCharts, DataAnalysis, Top, User, Download, Camera } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { teacherScheduleDashboardApi } from '@/api/management/teacher-schedule-dashboard'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  group: {
    type: Object,
    default: null
  },
  dateRange: {
    type: Object,
    default: () => ({ startDate: '', endDate: '' })
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const loading = ref(false)
const trendData = ref({})
const chartType = ref('line')
const trendChart = ref(null)
let chartInstance = null

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框打开
watch(dialogVisible, (newVal) => {
  if (newVal && props.group) {
    loadTrendData()
  }
})

// 监听图表类型变化
watch(chartType, () => {
  if (chartInstance && trendData.value.dailyData) {
    renderChart()
  }
})

// 组件卸载时清理图表
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

// 方法
const loadTrendData = async () => {
  if (!props.group?.groupId) return

  loading.value = true
  try {
    const response = await teacherScheduleDashboardApi.getGroupTrend({
      groupId: props.group.groupId,
      startDate: props.dateRange.startDate,
      endDate: props.dateRange.endDate
    })
    
    trendData.value = response.data || {}
    
    // 渲染图表
    await nextTick()
    renderChart()
  } catch (error) {
    console.error('加载教学组趋势数据失败:', error)
    ElMessage.error('加载趋势数据失败')
    trendData.value = {}
  } finally {
    loading.value = false
  }
}

const renderChart = () => {
  if (!trendChart.value || !trendData.value.dailyData) return

  // 销毁已存在的图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建新的图表实例
  chartInstance = echarts.init(trendChart.value)

  const dailyData = trendData.value.dailyData || []
  const dates = dailyData.map(item => `${item.date.slice(5)}\n${item.weekdayName}`)
  const slots = dailyData.map(item => item.availableSlots || 0)
  const teachers = dailyData.map(item => item.availableTeachers || 0)

  const option = {
    title: {
      text: `${props.group?.groupName || ''} 课次趋势`,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: (params) => {
        const dataIndex = params[0].dataIndex
        const dayData = dailyData[dataIndex]
        return `
          <div>
            <div><strong>${dayData.date} ${dayData.weekdayName}</strong></div>
            <div>可排课课次: ${dayData.availableSlots}</div>
            <div>有课时教师: ${dayData.availableTeachers}人</div>
            <div>平均课次: ${dayData.averageSlots?.toFixed(1) || '0.0'}</div>
            <div>利用率: ${dayData.utilization?.toFixed(1) || '0.0'}%</div>
          </div>
        `
      }
    },
    legend: {
      data: ['可排课课次', '有课时教师'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        interval: 0,
        fontSize: 12
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '课次',
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 12
        }
      },
      {
        type: 'value',
        name: '教师数',
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 12
        }
      }
    ],
    series: [
      {
        name: '可排课课次',
        type: chartType.value,
        data: slots,
        itemStyle: {
          color: '#409EFF'
        },
        smooth: chartType.value === 'line',
        emphasis: {
          focus: 'series'
        }
      },
      {
        name: '有课时教师',
        type: chartType.value,
        yAxisIndex: 1,
        data: teachers,
        itemStyle: {
          color: '#67C23A'
        },
        smooth: chartType.value === 'line',
        emphasis: {
          focus: 'series'
        }
      }
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    }
  }

  chartInstance.setOption(option)
}

const handleClose = () => {
  dialogVisible.value = false
  trendData.value = {}
  
  // 清理图表
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}

const exportChart = () => {
  if (!chartInstance) {
    ElMessage.warning('暂无图表数据')
    return
  }
  
  const url = chartInstance.getDataURL({
    type: 'png',
    pixelRatio: 2,
    backgroundColor: '#fff'
  })
  
  const link = document.createElement('a')
  link.href = url
  link.download = `${props.group?.groupName || '教学组'}_趋势分析_${props.dateRange.startDate}_${props.dateRange.endDate}.png`
  link.click()
}

const exportTrendData = () => {
  if (!trendData.value.dailyData || trendData.value.dailyData.length === 0) {
    ElMessage.warning('暂无数据可导出')
    return
  }
  
  ElMessage.info('数据导出功能开发中...')
  // 这里可以实现Excel导出功能
}

// 工具方法
const getSlotsTagType = (slots) => {
  if (slots >= 20) return 'success'
  if (slots >= 10) return 'warning'
  if (slots >= 5) return 'info'
  return 'danger'
}

const getUtilizationColor = (utilization) => {
  if (utilization >= 80) return '#67C23A'
  if (utilization >= 60) return '#E6A23C'
  if (utilization >= 40) return '#F56C6C'
  return '#909399'
}
</script>

<style scoped>
.group-trend-container {
  max-height: 70vh;
  overflow-y: auto;
}

.trend-overview-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.date-range-info {
  font-size: 14px;
  color: #606266;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stat-icon.total { background: #409EFF; }
.stat-icon.average { background: #67C23A; }
.stat-icon.peak { background: #E6A23C; }
.stat-icon.teachers { background: #F56C6C; }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.trend-chart-card {
  margin-bottom: 16px;
}

.trend-chart {
  height: 400px;
  width: 100%;
}

.trend-table-card {
  margin-bottom: 16px;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
