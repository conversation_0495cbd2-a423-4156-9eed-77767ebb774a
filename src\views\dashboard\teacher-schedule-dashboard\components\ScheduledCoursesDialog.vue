<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="scheduled-courses-content">
      <!-- 教师信息 -->
      <div class="teacher-info" v-if="teacher">
        <el-avatar :size="40" :src="teacher.avatar">
          {{ teacher.teacherName?.charAt(0) }}
        </el-avatar>
        <div class="teacher-details">
          <div class="teacher-name">{{ teacher.teacherName }}</div>
          <div class="teacher-meta">{{ teacher.phone }} | {{ teacher.groupName }}</div>
        </div>
      </div>

      <!-- 课程列表 -->
      <div class="courses-list">
        <el-table
          :data="courses"
          stripe
          border
          v-loading="loading"
          empty-text="当天暂无已排课程"
        >
          <el-table-column prop="courseName" label="课程名称" width="150">
            <template #default="{ row }">
              <div class="course-name">{{ row.courseName || '未命名课程' }}</div>
            </template>
          </el-table-column>

          <el-table-column prop="studentName" label="学生" width="120">
            <template #default="{ row }">
              <span class="student-name">{{ row.studentName || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="courseType" label="课程类型" width="100">
            <template #default="{ row }">
              <el-tag 
                :type="getCourseTypeTag(row.courseType)" 
                size="small"
              >
                {{ row.courseType || '学习课' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="上课时间" width="180">
            <template #default="{ row }">
              <div class="course-time">
                <div class="time-range">
                  {{ formatTime(row.scheduledStartTime) }} - {{ formatTime(row.scheduledEndTime) }}
                </div>
                <div class="duration">
                  时长: {{ row.durationMinutes || 60 }}分钟
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="courseStatus" label="状态" width="100">
            <template #default="{ row }">
              <el-tag 
                :type="getStatusTag(row.courseStatus)" 
                size="small"
              >
                {{ row.courseStatus || '待开始' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewCourseDetail(row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 统计信息 -->
      <div class="course-stats" v-if="courses.length > 0">
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-value">{{ courses.length }}</div>
              <div class="stat-label">总课程数</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-value">{{ totalDuration }}</div>
              <div class="stat-label">总时长(分钟)</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-value">{{ occupiedSlots }}</div>
              <div class="stat-label">占用课次</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="exportCourses" :loading="exporting">
          <el-icon><Download /></el-icon>
          导出课程
        </el-button>
        <el-button type="primary" @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import { teacherScheduleDashboardApi } from '@/api/management/teacher-schedule-dashboard'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  teacher: {
    type: Object,
    default: null
  },
  date: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const courses = ref([])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  if (!props.teacher || !props.date) return '已排课程'
  return `${props.teacher.teacherName} - ${formatDate(props.date)} 已排课程`
})

const totalDuration = computed(() => {
  return courses.value.reduce((total, course) => {
    return total + (course.durationMinutes || 60)
  }, 0)
})

const occupiedSlots = computed(() => {
  return Math.ceil(totalDuration.value / 60)
})

// 方法
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    month: 'long',
    day: 'numeric'
  })
}

const formatTime = (timeStr) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getCourseTypeTag = (type) => {
  switch (type) {
    case '学习课': return 'primary'
    case '复习课': return 'success'
    case '试听课': return 'warning'
    default: return 'info'
  }
}

const getStatusTag = (status) => {
  switch (status) {
    case '待开始': return 'info'
    case '进行中': return 'success'
    case '已完成': return 'primary'
    case '已取消': return 'danger'
    default: return 'info'
  }
}

const loadCourses = async () => {
  if (!props.teacher || !props.date) return

  loading.value = true
  try {
    const response = await teacherScheduleDashboardApi.getTeacherScheduledCourses(
      props.teacher.teacherId, 
      props.date
    )
    courses.value = response.data || []
  } catch (error) {
    ElMessage.error('加载课程数据失败: ' + error.message)
    courses.value = []
  } finally {
    loading.value = false
  }
}

const viewCourseDetail = () => {
  // 这里可以跳转到课程详情页面或打开课程详情对话框
  ElMessage.info('课程详情功能开发中...')
}

const exportCourses = async () => {
  exporting.value = true
  try {
    // 这里实现课程数据导出功能
    ElMessage.info('导出功能开发中...')
  } catch (error) {
    ElMessage.error('导出失败: ' + error.message)
  } finally {
    exporting.value = false
  }
}

const handleClose = () => {
  visible.value = false
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadCourses()
  }
})

watch(() => [props.teacher, props.date], () => {
  if (props.modelValue) {
    loadCourses()
  }
})
</script>

<style lang="scss" scoped>
.scheduled-courses-content {
  .teacher-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;

    .teacher-details {
      .teacher-name {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 4px;
      }

      .teacher-meta {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .courses-list {
    margin-bottom: 20px;

    .course-name {
      font-weight: 500;
      color: #303133;
    }

    .student-name {
      color: #606266;
    }

    .course-time {
      .time-range {
        font-weight: 500;
        color: #303133;
        margin-bottom: 2px;
      }

      .duration {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .course-stats {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;

    .stat-item {
      text-align: center;

      .stat-value {
        font-size: 20px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
