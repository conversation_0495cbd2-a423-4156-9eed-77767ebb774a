import request from '@/utils/request'

/**
 * 预约课申请API
 */

// 提交预约课申请（原有接口，保持兼容性）
export function submitCourseBookingApplicationApi(data) {
  return request({
    url: '/course-booking/apply',
    method: 'post',
    data
  })
}

// 提交预约课申请（支持试听课时间）
export function submitCourseBookingApplicationWithTrialTimeApi(data) {
  return request({
    url: '/course-booking/apply-with-trial-time',
    method: 'post',
    data
  })
}

// 获取待审批申请列表（教学组长视角）
export function getPendingApplicationsApi(params) {
  return request({
    url: '/course-booking/pending',
    method: 'get',
    params
  })
}

// 获取申请历史（销售视角）
export function getApplicationHistoryApi(params) {
  return request({
    url: '/course-booking/history',
    method: 'get',
    params
  })
}

// 获取申请详情
export function getApplicationDetailApi(id) {
  return request({
    url: `/course-booking/${id}`,
    method: 'get'
  })
}

// 确认申请（教学组长操作）
export function confirmApplicationApi(id, data) {
  return request({
    url: `/course-booking/${id}/confirm`,
    method: 'post',
    data
  })
}

// 拒绝申请（教学组长操作）
export function rejectApplicationApi(id, data) {
  return request({
    url: `/course-booking/${id}/reject`,
    method: 'post',
    data
  })
}

// 催促审批（销售操作）
export function remindApplicationApi(id, data) {
  return request({
    url: `/course-booking/${id}/remind`,
    method: 'post',
    data
  })
}

// 检查是否可以催促
export function canRemindApplicationApi(id) {
  return request({
    url: `/course-booking/${id}/can-remind`,
    method: 'get'
  })
}

// 获取销售组选项列表
export function getSalesGroupOptionsApi() {
  return request({
    url: '/sales/group/options',
    method: 'get'
  })
}

// 获取销售人员选项列表
export function getSalesStaffOptionsApi(params) {
  return request({
    url: '/sales/staff/options',
    method: 'get',
    params
  })
}
