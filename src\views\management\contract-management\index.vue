<template>
  <div class="contracts-container">
    <el-card class="search-card" shadow="never">
      <el-form :model="query" inline label-width="90px">
        <el-form-item label="订单编号">
          <el-input v-model="query.orderNo" placeholder="订单编号" clearable style="width:220px" @keyup.enter="onSearch" />
        </el-form-item>
        <el-form-item label="学生姓名">
          <el-input v-model="query.studentName" placeholder="学生姓名" clearable style="width:220px" @keyup.enter="onSearch" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="query.status" placeholder="请选择" clearable style="width: 180px">
            <el-option label="未签署" value="未签署" />
            <el-option label="已签署" value="已签署" />
            <el-option label="已拒签" value="已拒签" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch"><el-icon><Search /></el-icon>搜索</el-button>
          <el-button @click="onReset"><el-icon><Refresh /></el-icon>重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card shadow="never">
      <el-table :data="list" v-loading="loading" stripe style="width:100%">
        <el-table-column label="合同编号" prop="contractNo" width="220" />
        <el-table-column label="订单编号" prop="orderNo" width="220" />
        <el-table-column label="订单名称" prop="orderTitle" width="180" />
        <el-table-column label="学生姓名" prop="studentName" width="180" />
        <el-table-column label="状态" prop="status" width="120" />
        <el-table-column label="签署时间" prop="signTime" width="170">
          <template #default="{ row }">{{ formatDateTime(row.signTime) }}</template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="170">
          <template #default="{ row }">{{ formatDateTime(row.createTime) }}</template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="onDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          :current-page="query.pageNum"
          :page-size="query.pageSize"
          :total="total"
          :page-sizes="[10,20,50,100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onPageChange"
        />
      </div>
    </el-card>

    <ContractDetailDialog v-model="detailVisible" :id="currentId" />
  </div>
</template>

<script setup lang="ts" name="ContractManagement">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date.js'
import { getContractsList } from '@/api/management/contracts'
import ContractDetailDialog from './components/ContractDetailDialog.vue'

interface QueryReq { pageNum: number; pageSize: number; orderNo?: string; studentName?: string; status?: string }
interface ContractItem { id: string; orderNo: string; docTemplateId?: string; studentName?: string; status?: string; fillTime?: any; signTime?: any; createTime?: any }

const loading = ref(false)
const list = ref<ContractItem[]>([])
const total = ref(0)
const query = reactive<QueryReq>({ pageNum: 1, pageSize: 10 })
const detailVisible = ref(false)
const currentId = ref<string>('')

const fetchList = async () => {
  try {
    loading.value = true
    const res: any = await getContractsList(query)
    if (res.code === 200) {
      list.value = res.rows || []
      total.value = res.total || 0
    } else {
      ElMessage.error(res.msg || '获取合同列表失败')
    }
  } catch (e) {
    console.error(e)
    ElMessage.error('获取合同列表失败')
  } finally {
    loading.value = false
  }
}

const onSearch = () => { query.pageNum = 1; fetchList() }
const onReset = () => { Object.assign(query, { pageNum: 1, pageSize: 10, orderNo: '', studentName: '', status: '' }); fetchList() }
const onSizeChange = (size: number) => { query.pageSize = size; query.pageNum = 1; fetchList() }
const onPageChange = (page: number) => { query.pageNum = page; fetchList() }
const onDetail = (row: ContractItem) => { currentId.value = row.id; detailVisible.value = true }

onMounted(fetchList)
</script>

<style scoped>
.contracts-container { padding: 16px; }
.search-card { margin-bottom: 16px; }
.pagination { margin-top: 16px; display: flex; justify-content: flex-end; }
</style>

