<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑教学组' : '创建教学组'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="right"
    >
      <el-form-item label="组名" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入教学组名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="组长" prop="leaderId">
        <el-select
          v-model="form.leaderId"
          placeholder="请选择组长（可选）"
          clearable
          filterable
          style="width: 100%"
        >
          <el-option
            v-for="teacher in allTeachers"
            :key="teacher.id"
            :label="`${teacher.name} (${teacher.phone})`"
            :value="teacher.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="教务" prop="adminId">
        <el-select
          v-model="form.adminId"
          placeholder="请选择教务（可选）"
          clearable
          filterable
          style="width: 100%"
        >
          <el-option
            v-for="teacher in allTeachers"
            :key="teacher.id"
            :label="`${teacher.name} (${teacher.phone})`"
            :value="teacher.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入教学组描述（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="2"
          placeholder="请输入备注信息（可选）"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ isEdit ? "更新" : "创建" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { useTeachingGroupStore } from "@/stores/teachingGroup";

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  group: {
    type: Object,
    default: null,
  },
  allTeachers: {
    type: Array,
    default: () => [],
  },
});

// Emits
const emit = defineEmits(["update:modelValue", "success"]);

// 使用store
const teachingGroupStore = useTeachingGroupStore();

// 响应式数据
const formRef = ref();
const submitting = ref(false);

const form = reactive({
  name: "",
  leaderId: "",
  adminId: "",
  description: "",
  remark: "",
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: "请输入教学组名称", trigger: "blur" },
    { min: 2, max: 50, message: "教学组名称长度在 2 到 50 个字符", trigger: "blur" },
  ],
  leaderId: [
    {
      validator: (rule, value, callback) => {
        if (value && value === form.adminId) {
          callback(new Error("组长和教务不能是同一人"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
  adminId: [
    {
      validator: (rule, value, callback) => {
        if (value && value === form.leaderId) {
          callback(new Error("组长和教务不能是同一人"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
};

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

const isEdit = computed(() => !!props.group);

// 方法
const resetForm = () => {
  Object.assign(form, {
    name: "",
    leaderId: "",
    adminId: "",
    description: "",
    remark: "",
  });
};

// 监听器
watch(
  () => props.group,
  (newGroup) => {
    if (newGroup) {
      // 编辑模式，填充表单数据
      Object.assign(form, {
        name: newGroup.name || "",
        leaderId: newGroup.leaderId || "",
        adminId: newGroup.adminId || "",
        description: newGroup.description || "",
        remark: newGroup.remark || "",
      });
    } else {
      // 创建模式，重置表单
      resetForm();
    }
  },
  { immediate: true }
);

watch(
  () => props.modelValue,
  (visible) => {
    if (visible) {
      nextTick(() => {
        formRef.value?.clearValidate();
      });
    }
  }
);

const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate();

    submitting.value = true;

    const formData = { ...form };

    let success = false;
    if (isEdit.value) {
      // 编辑模式
      success = await teachingGroupStore.updateTeachingGroup({
        id: props.group.id,
        ...formData,
      });
    } else {
      // 创建模式
      success = await teachingGroupStore.createTeachingGroup(formData);
    }

    if (success) {
      emit("success");
      handleClose();
    }
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    submitting.value = false;
  }
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 90% !important;
    margin: 5vh auto;
  }

  :deep(.el-form) {
    .el-form-item {
      margin-bottom: 20px;
    }
  }
}
</style>
