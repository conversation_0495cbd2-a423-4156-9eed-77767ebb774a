<template>
  <div class="sales-student-management">

    <!-- 搜索表单 -->
    <el-card shadow="never" class="search-card">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="学生姓名">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入学生姓名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="手机号码">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入手机号码"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="销售组">
          <el-select
            v-model="searchForm.salesGroupId"
            placeholder="请选择销售组"
            clearable
            style="width: 200px"
            @change="handleSalesGroupChange"
          >
            <el-option
              v-for="group in salesGroupOptions"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="销售人员">
          <el-select
            v-model="searchForm.salesId"
            placeholder="请选择销售人员"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="sales in salesOptions"
              :key="sales.id"
              :label="sales.name"
              :value="sales.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="年级">
          <el-select
            v-model="searchForm.grade"
            placeholder="请选择年级"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="grade in GRADE_OPTIONS"
              :key="grade.value"
              :label="grade.label"
              :value="grade.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="在读" value="active" />
            <el-option label="暂停" value="inactive" />
            <el-option label="毕业" value="graduated" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格容器 -->
    <div class="table-container">

      <!-- 学生列表 -->
      <el-card shadow="never" class="table-card">
        <div class="table-header">
          <div class="table-actions">
            <el-button type="primary" size="small" @click="handleCreate" v-hasPermi="['sales:student:add']">
              <el-icon><Plus /></el-icon>
              新建学生
            </el-button>
            <el-button size="small" @click="handleImport" v-hasPermi="['sales:student:import']">
              <el-icon><Upload /></el-icon>
              导入学生
            </el-button>
            <el-button size="small" @click="handleDownloadTemplate" v-hasPermi="['sales:student:import']">
              <el-icon><Download /></el-icon>
              下载模板
            </el-button>
            <el-button
              type="primary"
              size="small"
              :disabled="!multipleSelection.length"
              @click="handleBatchAssign"
              v-hasPermi="['sales:student:assign']"
            >
              <el-icon><User /></el-icon>
              批量分配
            </el-button>
            <!-- 批量删除按钮已屏蔽 -->
            <!-- <el-button
              type="danger"
              size="small"
              :disabled="!multipleSelection.length"
              @click="handleBatchDelete"
              v-hasPermi="['sales:student:remove']"
            >
              <el-icon><Delete /></el-icon>
              批量删除
            </el-button> -->
          </div>
        </div>

        <div class="table-wrapper">
          <el-table
            v-loading="loading"
            :data="studentList"
            stripe
            style="width: 100%"
            :max-height="tableMaxHeight"
          >
            <el-table-column label="序号" width="80" fixed="left">
              <template #header>
                <div style="display: flex; align-items: center; gap: 8px;">
                  <el-checkbox
                    :indeterminate="isIndeterminate"
                    v-model="checkAll"
                    @change="handleCheckAllChange"
                  />
                  <span>序号</span>
                </div>
              </template>
              <template #default="{ row, $index }">
                <div style="display: flex; align-items: center; gap: 8px;">
                  <el-checkbox
                    :model-value="multipleSelection.some(item => item.id === row.id)"
                    @change="(checked) => handleSingleCheck(checked, row)"
                  />
                  <span>{{ getTableIndex($index) }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="学生姓名" width="120" fixed="left" />
        <el-table-column prop="phone" label="手机号码" width="130" />
        <el-table-column prop="grade" label="年级" width="100">
          <template #default="{ row }">
            <el-tag type="success" size="small">{{ getGradeText(row.grade) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="school" label="学校" width="150" show-overflow-tooltip />
        <el-table-column prop="salesName" label="销售人员" width="120">
          <template #default="{ row }">
            <span v-if="row.salesName">{{ row.salesName }}</span>
            <el-tag v-else type="warning" size="small">未分配</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="salesGroupName" label="销售组" width="120">
          <template #default="{ row }">
            <span>{{ row.salesGroupName || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalHours" label="总课时" width="100" align="center">
          <template #default="{ row }">
            <span>{{ row.totalHours || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="remainingHours" label="剩余课时" width="100" align="center">
          <template #default="{ row }">
            <span>{{ row.remainingHours || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="teachers" label="老师" width="180">
          <template #default="{ row }">
            <div v-if="row.teachers && row.teachers.length > 0" class="teacher-tags">
              <el-tag
                v-for="teacher in row.teachers"
                :key="teacher.teacherId"
                type="success"
                size="small"
                class="teacher-tag"
              >
                {{ teacher.teacherName }}
              </el-tag>
            </div>
            <span v-else class="no-teacher">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="{ row }">
            <span>{{ formatDate(row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="380" fixed="right">
          <template #default="{ row }">
            <div class="operation-buttons">
              <!-- 主要操作按钮 -->
              <el-button
                type="primary"
                size="small"
                @click="handleEdit(row)"
                v-hasPermi="['sales:student:edit']"
              >
                编辑
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="handleViewSchedule(row)"
              >
                课表
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="handleMatchTeacher(row)"
                v-hasPermi="['sales:student:booking']"
              >
                预约试听课
              </el-button>
              <el-button
                type="warning"
                size="small"
                @click="handleAssign(row)"
                v-hasPermi="['sales:student:assign','sales_director', 'sales_group_leader']"
              >
                {{ row.salesName ? '更换销售' : '分配销售' }}
              </el-button>
              <!-- 产品下单 -->
              <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="家长信息设置不完整，无法下单"
                  placement="top-start"
                  v-hasPermi="['order:pay']"
                  v-if="row.parentPhone === undefined || row.parentName === undefined || row.parentPhone === '' || row.parentName === ''"
              >
                <el-button type="primary"
                           :disabled="true"
                           size="small" @click="handleOrder(row)">
                  产品下单
                </el-button>
              </el-tooltip>
              <el-button type="primary" v-else
                         v-hasPermi="['order:pay']"
                         size="small" @click="handleOrder(row)">
                产品下单
              </el-button>


              <!-- 产品核销 -->
              <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="家长信息设置不完整，无法核销"
                  placement="top-start"
                  v-if="row.parentPhone === undefined || row.parentName === undefined || row.parentPhone === '' || row.parentName === ''"
              >
                <el-button type="success"
                           :disabled="true"
                           size="small" @click="handleWriteoff(row)">
                  订单核销
                </el-button>
              </el-tooltip>
              <el-button type="success" v-else
                         size="small" @click="handleWriteoff(row)">
                订单核销
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
        </div>
      </el-card>
    </div>

    <!-- 固定分页 -->
    <div class="fixed-pagination">
      <el-pagination
        v-model:current-page="searchForm.pageNum"
        v-model:page-size="searchForm.pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 学生详情对话框 -->
    <StudentDetailDialog
      v-model="detailDialogVisible"
      :student-id="currentStudentId"
      @refresh="getList"
    />

    <!-- 创建/编辑学生对话框 - 复用教学管理的表单结构 -->
    <StudentEditWrapper
      v-model="createEditDialogVisible"
      :student="currentStudent"
      @success="getList"
    />

    <!-- 分配学生对话框 -->
    <AssignStudentDialog
      v-model="assignDialogVisible"
      :student-data="currentStudent"
      :student-list="multipleSelection"
      :is-batch="isBatchAssign"
      @refresh="getList"
    />

    <!-- 导入学生对话框 -->
    <ImportStudentDialog
      v-model="importDialogVisible"
      @refresh="getList"
    />

    <!-- 老师匹配对话框 -->
    <el-dialog
      v-model="teacherMatchDialogVisible"
      title="预约试听课 - 匹配老师"
      width="95%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="teacher-match-dialog"
      @close="handleTeacherMatchClose"
    >
      <div style="height: 80vh; overflow: hidden;">
        <SalesBookingPage
          :student-info="currentStudent"
          :is-dialog-mode="true"
          @success="handleTeacherMatchSuccess"
        />
      </div>
    </el-dialog>

    <!-- 学生课表对话框 -->
    <StudentCourseScheduleDialog
      v-model="scheduleDialogVisible"
      :student="currentStudent"
    />

    <!-- 订单核销弹窗 -->
    <OrderWriteroffDialog 
      v-model="writeoffDialogVisible"
      :student-info="currentStudent"
      @success="handleWriteoffSuccess"
    />
  </div>
</template>

<script setup name="sales-student">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Upload, Download, Search, Refresh, User } from '@element-plus/icons-vue'
import {
  getSalesStudentListApi as getSalesStudentList,
  getSalesStudentDetailApi,
  deleteSalesStudentApi as deleteSalesStudent,
  downloadStudentTemplateApi as downloadTemplate,
  getSalesOptionsApi,
  getSalesGroupOptionsApi
} from '@/api/management/salesStudent'
import StudentDetailDialog from './components/StudentDetailDialog.vue'
import StudentEditWrapper from './components/StudentEditWrapper.vue'
import AssignStudentDialog from './components/AssignStudentDialog.vue'
import StudentCourseScheduleDialog from '@/views/management/student/components/StudentCourseScheduleDialog.vue'
import ImportStudentDialog from './components/ImportStudentDialog.vue'
import SalesBookingPage from '@/views/management/teacher-match/sales-booking.vue'
import {useRouter} from "vue-router";
import OrderWriteroffDialog from "@/views/management/order-writeroff/components/OrderWriteroffDialog.vue";

// 年级选项
const GRADE_OPTIONS = [
  { label: '一年级', value: 1 },
  { label: '二年级', value: 2 },
  { label: '三年级', value: 3 },
  { label: '四年级', value: 4 },
  { label: '五年级', value: 5 },
  { label: '六年级', value: 6 },
  { label: '初一', value: 7 },
  { label: '初二', value: 8 },
  { label: '初三', value: 9 },
  { label: '高一', value: 10 },
  { label: '高二', value: 11 },
  { label: '高三', value: 12 }
]

// 响应式数据
const loading = ref(false)
const studentList = ref([])
const total = ref(0)
const multipleSelection = ref([])

const salesGroupOptions = ref([])
const salesOptions = ref([])

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 20,
  name: '',
  phone: '',
  salesGroupId: '',
  salesId: '',
  grade: '',
  status: ''
})

// 对话框状态
const detailDialogVisible = ref(false)
const createEditDialogVisible = ref(false)
const assignDialogVisible = ref(false)
const importDialogVisible = ref(false)
const teacherMatchDialogVisible = ref(false)
const scheduleDialogVisible = ref(false)
const writeoffDialogVisible = ref(false)

// 当前操作的学生
const currentStudent = ref(null)
const currentStudentId = ref('')
const isBatchAssign = ref(false)
const router = useRouter();

// 计算属性
const getGradeText = (grade) => {
  const option = GRADE_OPTIONS.find(item => item.value === parseInt(grade))
  return option ? option.label : grade
}

const getStatusText = (status) => {
  const statusMap = {
    'active': '在读',
    'inactive': '暂停',
    'graduated': '毕业'
  }
  return statusMap[status] || status
}

const getStatusType = (status) => {
  const typeMap = {
    'active': 'success',
    'inactive': 'warning',
    'graduated': 'info'
  }
  return typeMap[status] || 'info'
}

const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

// 计算表格序号
const getTableIndex = (index) => {
  return (searchForm.pageNum - 1) * searchForm.pageSize + index + 1
}

// 计算表格最大高度
const tableMaxHeight = ref(600)

// 复选框相关状态
const checkAll = ref(false)
const isIndeterminate = ref(false)

// 计算表格高度
const calculateTableHeight = () => {
  // 计算可用高度：视窗高度 - 顶部padding - 搜索表单 - 表格头部 - 固定分页 - 其他间距
  const windowHeight = window.innerHeight
  const usedHeight = 40 + 120 + 80 + 80 + 40 // 大概的固定高度
  tableMaxHeight.value = Math.max(400, windowHeight - usedHeight)
}

// 方法
const getList = async () => {
  loading.value = true
  try {
    const response = await getSalesStudentList(searchForm)
    studentList.value = response.data?.records || []
    total.value = response.data?.total || 0
    // 清空选择并更新复选框状态
    multipleSelection.value = []
    updateCheckboxState()
  } catch (error) {
    ElMessage.error('获取学生列表失败')
  } finally {
    loading.value = false
  }
}



const getSalesGroupOptionList = async () => {
  try {
    const response = await getSalesGroupOptionsApi()
    salesGroupOptions.value = response.data
  } catch (error) {
    console.error('获取销售组选项失败:', error)
  }
}

const getSalesOptionList = async (groupId = '') => {
  try {
    const response = await getSalesOptionsApi({ groupId })
    salesOptions.value = response.data
  } catch (error) {
    console.error('获取销售人员选项失败:', error)
  }
}

// 事件处理
const handleSearch = () => {
  searchForm.pageNum = 1
  getList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    pageNum: 1,
    pageSize: 20,
    name: '',
    phone: '',
    salesGroupId: '',
    salesId: '',
    grade: '',
    status: ''
  })
  getList()
}

const handleSalesGroupChange = (groupId) => {
  searchForm.salesId = ''
  getSalesOptionList(groupId)
}

// 全选/取消全选
const handleCheckAllChange = (checked) => {
  if (checked) {
    multipleSelection.value = [...studentList.value]
  } else {
    multipleSelection.value = []
  }
  updateCheckboxState()
}

// 单个复选框变化
const handleSingleCheck = (checked, row) => {
  if (checked) {
    if (!multipleSelection.value.some(item => item.id === row.id)) {
      multipleSelection.value.push(row)
    }
  } else {
    multipleSelection.value = multipleSelection.value.filter(item => item.id !== row.id)
  }
  updateCheckboxState()
}

// 更新复选框状态
const updateCheckboxState = () => {
  const selectedCount = multipleSelection.value.length
  const totalCount = studentList.value.length

  checkAll.value = selectedCount === totalCount && totalCount > 0
  isIndeterminate.value = selectedCount > 0 && selectedCount < totalCount
}

const handleSizeChange = (size) => {
  searchForm.pageSize = size
  getList()
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getList()
}

const handleCreate = () => {
  currentStudent.value = null
  createEditDialogVisible.value = true
}

const handleView = (row) => {
  currentStudentId.value = row.id
  detailDialogVisible.value = true
}

const handleEdit = async (row) => {
  try {
    console.log('开始编辑学生，获取最新数据:', row.id)
    // 重新获取学生详情，确保数据是最新的
    const response = await getSalesStudentDetailApi(row.id)
    console.log('获取到的学生详情:', response.data)
    currentStudent.value = response.data
    createEditDialogVisible.value = true
  } catch (error) {
    console.error('获取学生详情失败:', error)
    ElMessage.error('获取学生信息失败')
  }
}

const handleViewSchedule = (row) => {
  // 使用弹窗显示学生课表
  currentStudent.value = { ...row }
  scheduleDialogVisible.value = true
}

const handleMatchTeacher = (row) => {
  // 使用弹窗模式显示老师匹配页面
  // 先清空当前学生信息，确保组件能检测到变化
  currentStudent.value = null
  // 使用nextTick确保DOM更新后再设置新的学生信息
  nextTick(() => {
    currentStudent.value = { ...row }
    teacherMatchDialogVisible.value = true
  })
}

const handleAssign = (row) => {
  currentStudent.value = { ...row }
  isBatchAssign.value = false
  assignDialogVisible.value = true
}

const handleBatchAssign = () => {
  if (!multipleSelection.value.length) {
    ElMessage.warning('请选择要分配的学生')
    return
  }
  isBatchAssign.value = true
  assignDialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除学生"${row.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteSalesStudent([row.id])
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 下拉菜单命令处理
const handleDropdownCommand = (command, row) => {
  switch (command) {
    case 'view':
      handleView(row)
      break
    case 'delete':
      handleDelete(row)
      break
    case 'export':
      handleExportStudent(row)
      break
    default:
      console.warn('未知的下拉菜单命令:', command)
  }
}

// 导出单个学生信息
const handleExportStudent = (row) => {
  ElMessage.info('导出功能开发中...')
}

const handleBatchDelete = async () => {
  if (!multipleSelection.value.length) {
    ElMessage.warning('请选择要删除的学生')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的${multipleSelection.value.length}个学生吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = multipleSelection.value.map(item => item.id)
    await deleteSalesStudent(ids)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleImport = () => {
  importDialogVisible.value = true
}

const handleDownloadTemplate = async () => {
  try {
    const response = await downloadTemplate()
    // 创建下载链接 - 修复：直接使用response而不是response.data
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '学生导入模板.xlsx'
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('模板下载失败')
  }
}

// 老师匹配对话框事件处理
const handleTeacherMatchSuccess = () => {
  teacherMatchDialogVisible.value = false
  currentStudent.value = null
  // 可以在这里刷新学生列表或显示成功消息
  getList()
}

const handleTeacherMatchClose = () => {
  teacherMatchDialogVisible.value = false
  currentStudent.value = null
}


const handleOrder = async (row) => {
  await router.push({ path: '/product/order', query: { studentId: row.id } })
}

// 订单核销处理
const handleWriteoff = (row) => {
  currentStudent.value = { ...row }
  writeoffDialogVisible.value = true
}

// 订单核销成功回调
const handleWriteoffSuccess = () => {
  getList() // 刷新列表
}

// 生命周期
onMounted(() => {
  getList()
  getSalesGroupOptionList()
  getSalesOptionList()
  calculateTableHeight()

  // 监听窗口大小变化
  window.addEventListener('resize', calculateTableHeight)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight)
})
</script>

<style scoped>
.sales-student-management {
  padding: 20px 20px 0 20px;
  background-color: #f5f5f5;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* 搜索条件样式 */
.search-card {
  margin-bottom: 12px;
  flex-shrink: 0;
}

.search-card .el-form {
  padding: 8px 0;
}

.search-card .el-form-item {
  margin-bottom: 12px;
}

/* 表格容器样式 */
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
  padding-bottom: 80px; /* 为固定分页留出空间 */
}

.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  overflow: hidden;
  min-height: 0;
}

.table-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 8px;
}

.table-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

.table-wrapper .el-table {
  width: 100% !important;
}

.table-wrapper .el-table .el-table__cell {
  padding: 8px 12px;
}

/* 固定分页样式 */
.fixed-pagination {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 20px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: center;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  align-items: center;
}

/* 操作按钮样式优化 */
.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.operation-buttons .el-button {
  margin: 0;
  min-width: auto;
}

.operation-buttons .el-button + .el-button {
  margin-left: 0;
}

/* 老师标签样式 */
.teacher-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.teacher-tag {
  margin: 0;
  font-size: 12px;
}

.no-teacher {
  color: #909399;
  font-style: italic;
}

/* 确保按钮在小屏幕上也能正常显示 */
@media (max-width: 1200px) {
  .operation-buttons {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .operation-buttons .el-button {
    width: 100%;
    justify-content: center;
  }
}
</style>
