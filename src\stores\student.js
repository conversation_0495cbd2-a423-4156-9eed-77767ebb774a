import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import { studentApi } from '@/api/management/student'

export const useStudentStore = defineStore('student', () => {
  // 状态
  const students = ref([])
  const currentStudent = ref(null)
  const studentStats = ref({
    totalStudents: 0,
    activeStudents: 0,
    inactiveStudents: 0,
    graduatedStudents: 0,
    newStudentsThisMonth: 0
  })
  const loading = ref(false)
  const pagination = ref({
    pageNum: 1,
    pageSize: 20,
    total: 0
  })

  // 计算属性
  const activeStudents = computed(() => {
    return students.value.filter(student => student.status === 'active')
  })

  const inactiveStudents = computed(() => {
    return students.value.filter(student => student.status === 'inactive')
  })

  const graduatedStudents = computed(() => {
    return students.value.filter(student => student.status === 'graduated')
  })

  // 方法
  const fetchStudents = async (params = {}) => {
    loading.value = true
    try {
      const response = await studentApi.getStudents({
        pageNum: pagination.value.pageNum,
        pageSize: pagination.value.pageSize,
        ...params
      })

      if (response.code === 200) {
        students.value = response.data.records || []
        pagination.value.total = response.data.total || 0
        pagination.value.pageNum = response.data.current || 1
        pagination.value.pageSize = response.data.size || 20
      } else {
        ElMessage.error(response.message || '获取学生列表失败')
      }
    } catch (error) {
      console.error('获取学生列表失败:', error)
      ElMessage.error('获取学生列表失败')
    } finally {
      loading.value = false
    }
  }

  const fetchStudentDetail = async (studentId) => {
    try {
      const response = await studentApi.getStudentDetail(studentId)

      if (response.code === 200) {
        currentStudent.value = response.data
        return response.data
      } else {
        ElMessage.error(response.message || '获取学生详情失败')
        return null
      }
    } catch (error) {
      console.error('获取学生详情失败:', error)
      ElMessage.error('获取学生详情失败')
      return null
    }
  }

  const createStudent = async (studentData) => {
    try {
      const response = await studentApi.createStudent(studentData)

      if (response.code === 200) {
        ElMessage.success('创建学生成功')
        return response.data
      } else {
        ElMessage.error(response.message || '创建学生失败')
        return null
      }
    } catch (error) {
      console.error('创建学生失败:', error)
      ElMessage.error('创建学生失败')
      return null
    }
  }

  const updateStudent = async (studentId, studentData) => {
    try {
      const response = await studentApi.updateStudent(studentId, studentData)

      if (response.code === 200) {
        ElMessage.success('更新学生信息成功')
        return true
      } else {
        ElMessage.error(response.message || '更新学生信息失败')
        return false
      }
    } catch (error) {
      console.error('更新学生信息失败:', error)
      ElMessage.error('更新学生信息失败')
      return false
    }
  }

  const deleteStudent = async (studentId) => {
    try {
      const response = await studentApi.deleteStudent(studentId)

      if (response.code === 200) {
        ElMessage.success('删除学生成功')
        return true
      } else {
        ElMessage.error(response.message || '删除学生失败')
        return false
      }
    } catch (error) {
      console.error('删除学生失败:', error)
      ElMessage.error('删除学生失败')
      return false
    }
  }

  const deleteStudents = async (studentIds) => {
    try {
      const response = await studentApi.deleteStudents(studentIds)

      if (response.code === 200) {
        ElMessage.success('批量删除学生成功')
        return true
      } else {
        ElMessage.error(response.message || '批量删除学生失败')
        return false
      }
    } catch (error) {
      console.error('批量删除学生失败:', error)
      ElMessage.error('批量删除学生失败')
      return false
    }
  }

  const fetchStudentStats = async () => {
    try {
      const response = await studentApi.getStudentStats()

      if (response.code === 200) {
        studentStats.value = response.data
      } else {
        ElMessage.error(response.message || '获取学生统计信息失败')
      }
    } catch (error) {
      console.error('获取学生统计信息失败:', error)
      ElMessage.error('获取学生统计信息失败')
    }
  }

  const fetchStudentSchedule = async (studentId, params = {}) => {
    try {
      const response = await studentApi.getStudentSchedule(studentId, params)

      if (response.code === 200) {
        return response.data
      } else {
        ElMessage.error(response.message || '获取学生课表失败')
        return []
      }
    } catch (error) {
      console.error('获取学生课表失败:', error)
      ElMessage.error('获取学生课表失败')
      return []
    }
  }

  const fetchAvailableStudents = async () => {
    try {
      const response = await studentApi.getAvailableStudents()

      if (response.code === 200) {
        return response.data
      } else {
        ElMessage.error(response.message || '获取可分配学生列表失败')
        return []
      }
    } catch (error) {
      console.error('获取可分配学生列表失败:', error)
      ElMessage.error('获取可分配学生列表失败')
      return []
    }
  }

  const fetchStudentCourseStats = async (studentId) => {
    try {
      const response = await studentApi.getStudentCourseStats(studentId)

      if (response.code === 200) {
        return response.data
      } else {
        ElMessage.error(response.message || '获取学生课程统计失败')
        return null
      }
    } catch (error) {
      console.error('获取学生课程统计失败:', error)
      ElMessage.error('获取学生课程统计失败')
      return null
    }
  }

  const fetchStudentRecentCourses = async (studentId, limit = 5) => {
    try {
      const response = await studentApi.getStudentRecentCourses(studentId, { limit })

      if (response.code === 200) {
        return response.data
      } else {
        ElMessage.error(response.message || '获取学生最近课程失败')
        return []
      }
    } catch (error) {
      console.error('获取学生最近课程失败:', error)
      ElMessage.error('获取学生最近课程失败')
      return []
    }
  }

  const fetchStudentsByTeacher = async (teacherId) => {
    try {
      const response = await studentApi.getStudentsByTeacher(teacherId)

      if (response.code === 200) {
        return response.data
      } else {
        // ElMessage.error(response.message || '获取教师学生列表失败')
        return []
      }
    } catch (error) {
      console.error('获取教师学生列表失败:', error)
    //   ElMessage.error('获取教师学生列表失败')
      return []
    }
  }

  const assignTeacherToStudent = async (studentId, teacherId) => {
    try {
      const response = await studentApi.assignTeacherToStudent(studentId, teacherId)

      if (response.code === 200) {
        ElMessage.success('分配教师成功')
        return true
      } else {
        ElMessage.error(response.message || '分配教师失败')
        return false
      }
    } catch (error) {
      console.error('分配教师失败:', error)
      ElMessage.error('分配教师失败')
      return false
    }
  }

  const unassignTeacherFromStudent = async (studentId) => {
    try {
      const response = await studentApi.unassignTeacherFromStudent(studentId)

      if (response.code === 200) {
        ElMessage.success('取消教师分配成功')
        return true
      } else {
        ElMessage.error(response.message || '取消教师分配失败')
        return false
      }
    } catch (error) {
      console.error('取消教师分配失败:', error)
      ElMessage.error('取消教师分配失败')
      return false
    }
  }

  const fetchAvailableTeachers = async () => {
    try {
      const response = await studentApi.getAvailableTeachers()

      if (response.code === 200) {
        return response.data || []
      } else {
        ElMessage.error(response.message || '获取可用教师列表失败')
        return []
      }
    } catch (error) {
      console.error('获取可用教师列表失败:', error)
      ElMessage.error('获取可用教师列表失败')
      return []
    }
  }

  const setPagination = (pageNum, pageSize) => {
    pagination.value.pageNum = pageNum
    pagination.value.pageSize = pageSize
  }

  const clearCurrentStudent = () => {
    currentStudent.value = null
  }

  return {
    // 状态
    students,
    currentStudent,
    studentStats,
    loading,
    pagination,

    // 计算属性
    activeStudents,
    inactiveStudents,
    graduatedStudents,

    // 方法
    fetchStudents,
    fetchStudentDetail,
    createStudent,
    updateStudent,
    deleteStudent,
    deleteStudents,
    fetchStudentStats,
    fetchStudentSchedule,
    fetchAvailableStudents,
    fetchStudentCourseStats,
    fetchStudentRecentCourses,
    fetchStudentsByTeacher,
    assignTeacherToStudent,
    unassignTeacherFromStudent,
    fetchAvailableTeachers,
    setPagination,
    clearCurrentStudent
  }
})
