<template>
  <div class="multi-file-upload">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="headers"
      :file-list="fileList"
      :limit="limit"
      :accept="acceptTypes"
      :before-upload="handleBeforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
      :show-file-list="true"
      :auto-upload="true"
      multiple
      class="upload-demo"
    >
      <el-button type="primary" :disabled="fileList.length >= limit">
        <el-icon><Upload /></el-icon>
        选择文件
      </el-button>
      
      <template #tip>
        <div class="el-upload__tip" v-if="showTip">
          <div>{{ tipText }}</div>
          <div v-if="fileSize">单个文件大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b></div>
          <div v-if="fileTypes && fileTypes.length">
            支持格式：<b style="color: #f56c6c">{{ fileTypes.join(', ') }}</b>
          </div>
        </div>
      </template>
    </el-upload>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Upload } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getToken } from '@/utils/auth'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  // 上传地址
  uploadUrl: {
    type: String,
    required: true
  },
  // 文件数量限制
  limit: {
    type: Number,
    default: 5
  },
  // 文件大小限制(MB)
  fileSize: {
    type: Number,
    default: 200
  },
  // 支持的文件类型
  fileTypes: {
    type: Array,
    default: () => []
  },
  // 提示文本
  tipText: {
    type: String,
    default: '请选择文件上传'
  },
  // 是否显示提示
  showTip: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const uploadRef = ref()
const fileList = ref([])

// 计算属性
const headers = computed(() => ({
  Authorization: `Bearer ${getToken()}`
}))

const acceptTypes = computed(() => {
  if (!props.fileTypes || props.fileTypes.length === 0) return ''
  return props.fileTypes.map(type => `.${type}`).join(',')
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  if (newVal && Array.isArray(newVal)) {
    fileList.value = newVal.map((url, index) => ({
      name: `文件${index + 1}`,
      url: url,
      status: 'success'
    }))
  } else {
    fileList.value = []
  }
}, { immediate: true })

// 方法
const handleBeforeUpload = (file) => {
  // 检查文件类型
  if (props.fileTypes && props.fileTypes.length > 0) {
    const fileExtension = file.name.split('.').pop().toLowerCase()
    if (!props.fileTypes.includes(fileExtension)) {
      ElMessage.error(`不支持的文件格式，请上传 ${props.fileTypes.join(', ')} 格式的文件`)
      return false
    }
  }

  // 检查文件大小
  if (props.fileSize && file.size > props.fileSize * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过 ${props.fileSize}MB`)
    return false
  }

  return true
}

const handleSuccess = (response, file) => {
  console.log('Upload success response:', response)

  // 处理不同的响应格式
  let fileUrl = null
  if (response.success && response.data && response.data.fileUrl) {
    fileUrl = response.data.fileUrl
  } else if (response.data && typeof response.data === 'string') {
    // 如果响应是字符串格式
    try {
      const parsed = JSON.parse(response.data)
      if (parsed.success && parsed.data && parsed.data.fileUrl) {
        fileUrl = parsed.data.fileUrl
      }
    } catch (e) {
      console.error('Failed to parse response:', e)
    }
  }

  if (fileUrl) {
    // 更新文件列表
    const urls = fileList.value
      .filter(item => item.status === 'success' && item.uid !== file.uid)
      .map(item => item.response?.data?.fileUrl || item.url)

    // 添加新上传的文件
    urls.push(fileUrl)

    // 更新 v-model
    emit('update:modelValue', urls)
    emit('change', urls)

    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error('文件上传失败：响应格式错误')
    // 移除失败的文件
    const index = fileList.value.findIndex(item => item.uid === file.uid)
    if (index > -1) {
      fileList.value.splice(index, 1)
    }
  }
}

const handleError = (error, file) => {
  console.error('文件上传失败:', error)
  ElMessage.error('文件上传失败，请重试')
  
  // 移除失败的文件
  const index = fileList.value.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
}

const handleRemove = (file) => {
  // 更新文件列表
  const urls = fileList.value
    .filter(item => item.uid !== file.uid && item.status === 'success')
    .map(item => item.response?.data?.fileUrl || item.url)
  
  // 更新 v-model
  emit('update:modelValue', urls)
  emit('change', urls)
}

const handleExceed = () => {
  ElMessage.warning(`最多只能上传 ${props.limit} 个文件`)
}

// 清空文件列表
const clearFiles = () => {
  uploadRef.value?.clearFiles()
  emit('update:modelValue', [])
  emit('change', [])
}

// 暴露方法
defineExpose({
  clearFiles
})
</script>

<style scoped>
.multi-file-upload {
  width: 100%;
}

.upload-demo {
  width: 100%;
}

:deep(.el-upload-list) {
  margin-top: 10px;
}

:deep(.el-upload__tip) {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

:deep(.el-upload__tip b) {
  font-weight: 600;
}
</style>
