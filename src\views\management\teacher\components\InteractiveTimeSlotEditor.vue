<template>
  <div class="interactive-time-slot-editor">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <span class="title">可上课时间设置</span>
        <span class="subtitle">点击空白区域添加时间段，悬停已有时间段进行编辑</span>
      </div>
      <div class="toolbar-right">
        <el-button size="small" @click="clearAll" :disabled="readonly">
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
        <el-button size="small" type="primary" @click="applyTemplate" :disabled="readonly">
          <el-icon><Clock /></el-icon>
          模板
        </el-button>
      </div>
    </div>

    <!-- 时间网格 -->
    <div class="time-grid-container" ref="gridContainer">
      <!-- 表头 -->
      <div class="grid-header">
        <div class="time-column-header">时间</div>
        <div
          v-for="day in weekDays"
          :key="day.value"
          class="day-header"
        >
          <div class="day-name">{{ day.label }}</div>
          <div class="day-date">{{ getDayDate(day.value) }}</div>
        </div>
      </div>

      <!-- 时间网格主体 -->
      <div class="grid-body">
        <div
          v-for="timeSlot in displayTimeSlots"
          :key="timeSlot.hour + '-' + timeSlot.minute"
          class="time-row"
        >
          <!-- 时间标签 -->
          <div class="time-label">
            {{ formatTime(timeSlot.hour, timeSlot.minute) }}
          </div>

          <!-- 每天的时间格子 -->
          <div
            v-for="day in weekDays"
            :key="`${day.value}-${timeSlot.hour}`"
            class="time-cell"
            :class="getHourCellClass(day.value, timeSlot.hour)"
            :data-weekday="day.value"
            :data-hour="timeSlot.hour"
            @click="handleCellClick(day.value, timeSlot.hour)"
            @mouseenter="handleCellHover(day.value, timeSlot.hour, true)"
            @mouseleave="handleCellHover(day.value, timeSlot.hour, false)"
          >
            <div class="cell-content">
              <!-- 时间段标签 -->
              <div
                v-if="isHourSlotStart(day.value, timeSlot.hour)"
                class="slot-label"
              >
                {{ getHourSlotLabel(day.value, timeSlot.hour) }}
              </div>

              <!-- 悬停时的操作按钮 -->
              <div
                v-if="hoveredCell.weekday === day.value && hoveredCell.hour === timeSlot.hour && isHourSelected(day.value, timeSlot.hour)"
                class="cell-actions"
              >
                <el-button size="small" type="primary" @click.stop="editTimeSlot(day.value, timeSlot.hour)">
                  编辑
                </el-button>
                <el-button size="small" type="danger" @click.stop="deleteTimeSlot(day.value, timeSlot.hour)">
                  删除
                </el-button>
              </div>

              <!-- 5分钟精度指示器 -->
              <div class="minute-indicators">
                <div
                  v-for="minute in [0, 15, 30, 45]"
                  :key="minute"
                  class="minute-indicator"
                  :class="{ 'selected': isCellSelected(day.value, timeSlot.hour, minute) }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 选中时间段信息 -->
    <div v-if="selectedSlots.length > 0" class="selected-info">
      <div class="info-header">
        <span>已选择 {{ selectedSlots.length }} 个时间段</span>
        <el-button size="small" @click="selectedSlots = []">清除选择</el-button>
      </div>
      <div class="slots-preview">
        <div
          v-for="slot in selectedSlots"
          :key="slot.id"
          class="slot-preview"
        >
          <span class="slot-day">{{ getWeekDayName(slot.weekday) }}</span>
          <span class="slot-time">{{ slot.startTime }} - {{ slot.endTime }}</span>
          <el-button
            type="danger"
            link
            size="small"
            @click="removeSlot(slot)"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 时间段输入对话框 -->
    <el-dialog
      v-model="showTimeSlotDialog"
      :title="editingTimeSlot ? '编辑时间段' : '添加时间段'"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="timeSlotFormRef"
        :model="currentTimeSlot"
        :rules="timeSlotRules"
        label-width="80px"
      >
        <el-form-item label="星期" prop="weekday">
          <el-select v-model="currentTimeSlot.weekday" placeholder="请选择星期" style="width: 100%">
            <el-option
              v-for="day in weekDays"
              :key="day.value"
              :label="day.label"
              :value="day.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="开始时间" prop="startTime">
          <el-time-picker
            v-model="currentTimeSlot.startTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择开始时间"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="结束时间" prop="endTime">
          <el-time-picker
            v-model="currentTimeSlot.endTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择结束时间"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelTimeSlotEdit">取消</el-button>
          <el-button type="primary" @click="saveTimeSlot">
            {{ editingTimeSlot ? '更新' : '添加' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 模板选择对话框 -->
    <el-dialog
      v-model="showTemplateDialog"
      title="选择时间模板"
      width="500px"
    >
      <div class="template-list">
        <div
          v-for="template in templates"
          :key="template.id"
          class="template-item"
          @click="applySelectedTemplate(template)"
        >
          <div class="template-name">{{ template.name }}</div>
          <div class="template-description">{{ template.description }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Clock, Close } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  readonly: {
    type: Boolean,
    default: false
  },
  startHour: {
    type: Number,
    default: 6
  },
  endHour: {
    type: Number,
    default: 23
  },
  timeInterval: {
    type: Number,
    default: 5 // 5分钟间隔，精确控制
  },
  displayInterval: {
    type: Number,
    default: 60 // 1小时显示间隔
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const selectedSlots = ref([])
const showTemplateDialog = ref(false)
const isSelecting = ref(false)
const selectionStart = ref(null)
const selectionEnd = ref(null)
const currentWeekday = ref(null)
const gridContainer = ref()
const crossDaySelection = ref(false) // 标记是否为跨天选择

// 新的交互方式相关变量
const hoveredCell = ref({ weekday: null, hour: null })
const showTimeSlotDialog = ref(false)
const editingTimeSlot = ref(null)
const timeSlotFormRef = ref(null)
const currentTimeSlot = ref({
  weekday: 1,
  startTime: '',
  endTime: ''
})

// 常量
const weekDays = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 7 }
]

// 表单验证规则
const timeSlotRules = {
  weekday: [
    { required: true, message: '请选择星期', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    { validator: validateEndTime, trigger: 'change' }
  ]
}

// 验证结束时间
function validateEndTime(rule, value, callback) {
  if (!value) {
    callback(new Error('请选择结束时间'))
    return
  }

  if (!currentTimeSlot.value.startTime) {
    callback()
    return
  }

  if (value <= currentTimeSlot.value.startTime) {
    callback(new Error('结束时间必须大于开始时间'))
    return
  }

  // 检查是否有无效的00:00结束时间
  if (value === '00:00') {
    callback(new Error('结束时间不能为00:00，请选择有效的结束时间'))
    return
  }

  callback()
}

// 预设模板
const templates = [
  {
    id: 'workday',
    name: '工作日模板',
    description: '周一至周五，9:00-12:00，14:00-18:00',
    slots: generateTemplateSlots([1, 2, 3, 4, 5], [
      { start: '09:00', end: '12:00' },
      { start: '14:00', end: '18:00' }
    ])
  },
  {
    id: 'weekend',
    name: '周末模板',
    description: '周六周日，9:00-17:00',
    slots: generateTemplateSlots([6, 7], [
      { start: '09:00', end: '17:00' }
    ])
  },
  {
    id: 'evening',
    name: '晚间模板',
    description: '周一至周五，18:30-21:30',
    slots: generateTemplateSlots([1, 2, 3, 4, 5], [
      { start: '18:30', end: '21:30' }
    ])
  }
]

// 计算属性
const displayTimeSlots = computed(() => {
  const slots = []
  // 按小时显示
  for (let hour = props.startHour; hour <= props.endHour; hour++) {
    slots.push({ hour, minute: 0 })
  }
  return slots
})

// 内部时间精度（5分钟）
const timeSlots = computed(() => {
  const slots = []
  for (let hour = props.startHour; hour <= props.endHour; hour++) {
    for (let minute = 0; minute < 60; minute += props.timeInterval) {
      slots.push({ hour, minute })
    }
  }
  return slots
})

// 工具函数
// 标准化时间格式，将 "HH:mm:ss" 转换为 "HH:mm"
function normalizeTimeFormat(timeStr) {
  if (!timeStr || typeof timeStr !== 'string') {
    return timeStr
  }

  const parts = timeStr.split(':')
  if (parts.length >= 2) {
    // 只取小时和分钟部分，忽略秒
    const hours = parts[0].padStart(2, '0')
    const minutes = parts[1].padStart(2, '0')
    return `${hours}:${minutes}`
  }

  return timeStr
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  // 标准化时间格式，将 "HH:mm:ss" 转换为 "HH:mm"
  const normalizedSlots = (newValue || []).map(slot => ({
    ...slot,
    startTime: normalizeTimeFormat(slot.startTime),
    endTime: normalizeTimeFormat(slot.endTime)
  }))
  selectedSlots.value = normalizedSlots
}, { immediate: true, deep: true })

watch(selectedSlots, (newValue) => {
  emit('update:modelValue', newValue)
  emit('change', newValue)
}, { deep: true })

// 方法
function formatTime(hour, minute) {
  return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
}

function getDayDate(weekday) {
  const today = new Date()
  const currentDay = today.getDay()
  // 现在weekday使用1-7格式，需要转换为JavaScript的0-6格式进行计算
  const targetDay = weekday === 7 ? 0 : weekday
  const currentDayAdjusted = currentDay

  const diff = targetDay - currentDayAdjusted
  const targetDate = new Date(today)
  targetDate.setDate(today.getDate() + diff)

  return `${targetDate.getMonth() + 1}/${targetDate.getDate()}`
}

function getWeekDayName(weekday) {
  const day = weekDays.find(d => d.value === weekday)
  return day ? day.label : '未知'
}

function generateTemplateSlots(weekdays, timeRanges) {
  const slots = []
  weekdays.forEach(weekday => {
    timeRanges.forEach(range => {
      slots.push({
        id: `template_${weekday}_${range.start}_${range.end}`,
        weekday,
        startTime: range.start,
        endTime: range.end,
        status: 'available'
      })
    })
  })
  return slots
}

function getHourCellClass(weekday, hour) {
  const classes = []

  if (props.readonly) {
    classes.push('readonly')
  }

  // 检查这个小时内是否有任何时间段被选中
  if (isHourSelected(weekday, hour)) {
    classes.push('selected')
  }

  if (isSelecting.value) {
    if (isHourInSelectionRange(weekday, hour)) {
      classes.push('selecting')
    }
  }

  return classes
}

function isHourSelected(weekday, hour) {
  // 检查这个小时内是否有任何5分钟时间段被选中
  for (let minute = 0; minute < 60; minute += props.timeInterval) {
    if (isCellSelected(weekday, hour, minute)) {
      return true
    }
  }
  return false
}

function isHourInSelectionRange(weekday, hour) {
  if (!selectionStart.value || !selectionEnd.value) return false

  if (crossDaySelection.value) {
    // 跨天选择：检查当前weekday和hour是否在选择范围内
    return isHourInCrossDaySelection(weekday, hour)
  } else {
    // 同一天选择：只检查小时范围
    if (weekday !== selectionStart.value.weekday) return false

    const startHour = selectionStart.value.hour
    const endHour = selectionEnd.value.hour
    const minHour = Math.min(startHour, endHour)
    const maxHour = Math.max(startHour, endHour)

    return hour >= minHour && hour <= maxHour
  }
}

function isHourInCrossDaySelection(weekday, hour) {
  const startWeekday = selectionStart.value.weekday
  const endWeekday = selectionEnd.value.weekday
  const startHour = selectionStart.value.hour
  const endHour = selectionEnd.value.hour

  // 计算时间段范围：从开始小时到结束小时
  const timeStartHour = Math.min(startHour, endHour)
  const timeEndHour = Math.max(startHour, endHour)

  // 获取涉及的天数列表
  const weekdaysInRange = getWeekdayRange(startWeekday, endWeekday)

  // 检查当前weekday是否在范围内，并且hour在时间段范围内
  if (weekdaysInRange.includes(weekday)) {
    return hour >= timeStartHour && hour <= timeEndHour
  }

  return false
}

function isCellSelected(weekday, hour, minute) {
  return selectedSlots.value.some(slot => {
    if (slot.weekday !== weekday) return false

    const slotStart = timeToMinutes(slot.startTime)
    const slotEnd = timeToMinutes(slot.endTime)
    const cellTime = hour * 60 + minute

    return cellTime >= slotStart && cellTime < slotEnd
  })
}

function timeToMinutes(timeStr) {
  const [hours, minutes] = timeStr.split(':').map(Number)
  return hours * 60 + minutes
}

function minutesToTime(minutes) {
  // 处理超过24小时的情况，转换为当天时间
  const totalMinutes = minutes % (24 * 60)
  const hours = Math.floor(totalMinutes / 60)
  const mins = totalMinutes % 60

  // 如果是24:00，转换为23:59，避免跨天问题
  if (hours === 24 || (hours === 0 && minutes >= 24 * 60)) {
    return '23:59'
  }

  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
}

function isInSelectionRange(hour, minute) {
  if (!selectionStart.value || !selectionEnd.value) return false

  const startTime = selectionStart.value.hour * 60 + selectionStart.value.minute
  const endTime = selectionEnd.value.hour * 60 + selectionEnd.value.minute
  const currentTime = hour * 60 + minute

  const minTime = Math.min(startTime, endTime)
  const maxTime = Math.max(startTime, endTime)

  return currentTime >= minTime && currentTime <= maxTime
}

// 新的交互方法
function handleCellClick(weekday, hour) {
  if (props.readonly) return

  if (isHourSelected(weekday, hour)) {
    // 如果已有时间段，不做任何操作（通过悬停显示编辑/删除按钮）
    return
  } else {
    // 空白区域，显示添加时间段对话框
    showAddTimeSlotDialog(weekday, hour)
  }
}

function handleCellHover(weekday, hour, isEntering) {
  if (props.readonly) return

  if (isEntering) {
    hoveredCell.value = { weekday, hour }
  } else {
    hoveredCell.value = { weekday: null, hour: null }
  }
}

function showAddTimeSlotDialog(weekday, hour) {
  editingTimeSlot.value = null
  currentTimeSlot.value = {
    weekday: weekday,
    startTime: `${hour.toString().padStart(2, '0')}:00`,
    endTime: `${(hour + 1).toString().padStart(2, '0')}:00`
  }
  showTimeSlotDialog.value = true
}

function editTimeSlot(weekday, hour) {
  // 找到这个位置的时间段
  const slot = selectedSlots.value.find(slot => {
    if (slot.weekday !== weekday) return false
    const slotStart = timeToMinutes(slot.startTime)
    const slotEnd = timeToMinutes(slot.endTime)
    const hourStart = hour * 60
    const hourEnd = (hour + 1) * 60
    return slotStart < hourEnd && hourStart < slotEnd
  })

  if (slot) {
    editingTimeSlot.value = slot
    currentTimeSlot.value = {
      weekday: slot.weekday,
      startTime: slot.startTime,
      endTime: slot.endTime
    }
    showTimeSlotDialog.value = true
  }
}

function deleteTimeSlot(weekday, hour) {
  ElMessageBox.confirm(
    '确定要删除这个时间段吗？',
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    removeHourTimeSlots(weekday, hour)
    ElMessage.success('时间段已删除')
  }).catch(() => {
    // 用户取消
  })
}

// 这些方法已被新的点击交互方式替代，保留用于兼容性
// function startSelection(event, weekday, hour, minute) { ... }
// function updateSelection(weekday, hour, minute) { ... }

function saveTimeSlot() {
  if (!timeSlotFormRef.value) return

  timeSlotFormRef.value.validate((valid) => {
    if (!valid) return

    const { weekday, startTime, endTime } = currentTimeSlot.value

    // 验证时间段
    const validation = validateTimeSlot(startTime, endTime)
    if (!validation.valid) {
      ElMessage.error(validation.message)
      return
    }

    if (editingTimeSlot.value) {
      // 编辑现有时间段
      const index = selectedSlots.value.findIndex(slot => slot.id === editingTimeSlot.value.id)
      if (index >= 0) {
        // 先移除原时间段
        selectedSlots.value.splice(index, 1)
      }
    }

    // 创建新时间段
    const newSlot = {
      id: editingTimeSlot.value ? editingTimeSlot.value.id : `slot_${Date.now()}`,
      weekday,
      startTime,
      endTime,
      status: 'available'
    }

    // 合并重叠的时间段
    mergeTimeSlots(newSlot)

    showTimeSlotDialog.value = false
    ElMessage.success(editingTimeSlot.value ? '时间段已更新' : '时间段已添加')
  })
}

function cancelTimeSlotEdit() {
  showTimeSlotDialog.value = false
  editingTimeSlot.value = null
  currentTimeSlot.value = {
    weekday: 1,
    startTime: '',
    endTime: ''
  }
}

// 验证时间段是否有效
function validateTimeSlot(startTime, endTime) {
  if (!startTime || !endTime) {
    return { valid: false, message: '开始时间和结束时间不能为空' }
  }

  const startMinutes = timeToMinutes(startTime)
  const endMinutes = timeToMinutes(endTime)

  if (startMinutes >= endMinutes) {
    return { valid: false, message: '开始时间必须小于结束时间' }
  }

  if (endMinutes - startMinutes < 15) {
    return { valid: false, message: '时间段至少需要15分钟' }
  }

  return { valid: true }
}

function createTimeSlot() {
  if (!selectionStart.value || !selectionEnd.value) return

  if (crossDaySelection.value) {
    // 跨天选择：为每个涉及的天创建时间段
    createCrossDayTimeSlots()
  } else {
    // 同一天选择：创建单个时间段
    const weekday = selectionStart.value.weekday
    const startHour = Math.min(selectionStart.value.hour, selectionEnd.value.hour)
    const endHour = Math.max(selectionStart.value.hour, selectionEnd.value.hour)

    // 按小时创建时间段，确保不超过23:59
    const startTime = startHour * 60 // 小时开始
    let endTime = (endHour + 1) * 60 // 下一个小时开始

    // 如果结束时间超过23:59，设置为23:59
    if (endTime >= 24 * 60) {
      endTime = 23 * 60 + 59 // 23:59
    }

    const startTimeStr = minutesToTime(startTime)
    const endTimeStr = minutesToTime(endTime)

    // 验证时间段
    const validation = validateTimeSlot(startTimeStr, endTimeStr)
    if (!validation.valid) {
      ElMessage.warning(validation.message)
      return
    }

    const newSlot = {
      id: `slot_${Date.now()}`,
      weekday,
      startTime: startTimeStr,
      endTime: endTimeStr,
      status: 'available'
    }

    // 合并重叠的时间段
    mergeTimeSlots(newSlot)
  }
}

function createCrossDayTimeSlots() {
  const startWeekday = selectionStart.value.weekday
  const endWeekday = selectionEnd.value.weekday
  const startHour = selectionStart.value.hour
  const endHour = selectionEnd.value.hour

  // 计算时间段范围：从开始小时到结束小时
  const timeStartHour = Math.min(startHour, endHour)
  const timeEndHour = Math.max(startHour, endHour)

  const newSlots = []

  // 生成涉及的天数列表
  const weekdaysToProcess = getWeekdayRange(startWeekday, endWeekday)

  // 为每一天创建相同的时间段
  weekdaysToProcess.forEach(weekday => {
    const startTime = timeStartHour * 60
    let endTime = (timeEndHour + 1) * 60

    // 如果结束时间超过23:59，设置为23:59
    if (endTime >= 24 * 60) {
      endTime = 23 * 60 + 59 // 23:59
    }

    const startTimeStr = minutesToTime(startTime)
    const endTimeStr = minutesToTime(endTime)

    // 验证时间段
    const validation = validateTimeSlot(startTimeStr, endTimeStr)
    if (!validation.valid) {
      ElMessage.warning(`第${weekday}天的时间段无效: ${validation.message}`)
      return
    }

    const newSlot = {
      id: `slot_${Date.now()}_${weekday}_${Math.random()}`,
      weekday: weekday,
      startTime: startTimeStr,
      endTime: endTimeStr,
      status: 'available'
    }
    newSlots.push(newSlot)
  })

  // 为每个新时间段执行合并
  newSlots.forEach(slot => {
    mergeTimeSlots(slot)
  })
}

function getWeekdayRange(startWeekday, endWeekday) {
  const weekdays = []

  if (startWeekday === endWeekday) {
    // 同一天
    weekdays.push(startWeekday)
  } else if (startWeekday < endWeekday) {
    // 正常情况：不跨周，比如周一到周三
    for (let day = startWeekday; day <= endWeekday; day++) {
      weekdays.push(day)
    }
  } else {
    // 跨周情况：比如周六到周一
    // 先添加从开始天到周日
    for (let day = startWeekday; day <= 7; day++) {
      weekdays.push(day)
    }
    // 再添加从周一到结束天
    for (let day = 1; day <= endWeekday; day++) {
      weekdays.push(day)
    }
  }

  return weekdays
}

function mergeTimeSlots(newSlot) {
  // 移除与新时间段重叠的现有时间段
  const nonOverlappingSlots = selectedSlots.value.filter(slot => {
    if (slot.weekday !== newSlot.weekday) return true

    const slotStart = timeToMinutes(slot.startTime)
    const slotEnd = timeToMinutes(slot.endTime)
    const newStart = timeToMinutes(newSlot.startTime)
    const newEnd = timeToMinutes(newSlot.endTime)

    // 检查是否重叠
    return !(slotStart < newEnd && newStart < slotEnd)
  })

  // 添加新时间段
  nonOverlappingSlots.push(newSlot)

  // 合并相邻的时间段
  const sameDaySlots = nonOverlappingSlots.filter(slot => slot.weekday === newSlot.weekday)
  const otherDaySlots = nonOverlappingSlots.filter(slot => slot.weekday !== newSlot.weekday)

  // 按开始时间排序
  sameDaySlots.sort((a, b) => timeToMinutes(a.startTime) - timeToMinutes(b.startTime))

  // 合并相邻时间段
  const merged = []
  for (const slot of sameDaySlots) {
    if (merged.length === 0) {
      merged.push(slot)
    } else {
      const lastSlot = merged[merged.length - 1]
      const lastEnd = timeToMinutes(lastSlot.endTime)
      const currentStart = timeToMinutes(slot.startTime)

      if (lastEnd >= currentStart) {
        // 合并时间段
        const currentEnd = timeToMinutes(slot.endTime)
        lastSlot.endTime = minutesToTime(Math.max(lastEnd, currentEnd))
        lastSlot.id = `slot_${Date.now()}_merged`
      } else {
        merged.push(slot)
      }
    }
  }

  selectedSlots.value = [...otherDaySlots, ...merged]
}

// toggleHourCell 方法已被 handleCellClick 替代

function toggleCell(weekday, hour, minute) {
  if (props.readonly || isSelecting.value) return

  if (isCellSelected(weekday, hour, minute)) {
    // 删除包含此单元格的时间段
    removeTimeSlotAt(weekday, hour, minute)
  }
}

function removeHourTimeSlots(weekday, hour) {
  selectedSlots.value = selectedSlots.value.filter(slot => {
    if (slot.weekday !== weekday) return true

    const slotStart = timeToMinutes(slot.startTime)
    const slotEnd = timeToMinutes(slot.endTime)
    const hourStart = hour * 60
    const hourEnd = (hour + 1) * 60

    // 保留不在这个小时范围内的时间段
    return !(slotStart < hourEnd && hourStart < slotEnd)
  })
}

function removeTimeSlotAt(weekday, hour, minute) {
  const cellTime = hour * 60 + minute

  selectedSlots.value = selectedSlots.value.filter(slot => {
    if (slot.weekday !== weekday) return true

    const slotStart = timeToMinutes(slot.startTime)
    const slotEnd = timeToMinutes(slot.endTime)

    return !(cellTime >= slotStart && cellTime < slotEnd)
  })
}

function isHourSlotStart(weekday, hour) {
  return selectedSlots.value.some(slot => {
    if (slot.weekday !== weekday) return false

    const slotStart = timeToMinutes(slot.startTime)
    const hourStart = hour * 60

    return slotStart === hourStart
  })
}

function getHourSlotLabel(weekday, hour) {
  const slot = selectedSlots.value.find(slot => {
    if (slot.weekday !== weekday) return false

    const slotStart = timeToMinutes(slot.startTime)
    const hourStart = hour * 60

    return slotStart === hourStart
  })

  return slot ? `${slot.startTime}-${slot.endTime}` : ''
}

function isSlotStart(weekday, hour, minute) {
  return selectedSlots.value.some(slot => {
    if (slot.weekday !== weekday) return false

    const slotStart = timeToMinutes(slot.startTime)
    const cellTime = hour * 60 + minute

    return cellTime === slotStart
  })
}

function getSlotLabel(weekday, hour, minute) {
  const slot = selectedSlots.value.find(slot => {
    if (slot.weekday !== weekday) return false

    const slotStart = timeToMinutes(slot.startTime)
    const cellTime = hour * 60 + minute

    return cellTime === slotStart
  })

  return slot ? `${slot.startTime}-${slot.endTime}` : ''
}

function removeSlot(slot) {
  const index = selectedSlots.value.findIndex(s => s.id === slot.id)
  if (index >= 0) {
    selectedSlots.value.splice(index, 1)
  }
}

function clearAll() {
  if (selectedSlots.value.length === 0) {
    ElMessage.info('没有时间段需要清空')
    return
  }

  ElMessageBox.confirm(
    '确定要清空所有时间段吗？',
    '确认清空',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    selectedSlots.value = []
    ElMessage.success('已清空所有时间段')
  }).catch(() => {
    // 用户取消
  })
}

function applyTemplate() {
  showTemplateDialog.value = true
}

function applySelectedTemplate(template) {
  selectedSlots.value = template.slots.map((slot, index) => ({
    ...slot,
    id: `template_${template.id}_${index}`
  }))

  showTemplateDialog.value = false
  ElMessage.success(`已应用${template.name}`)
}
</script>

<style lang="scss" scoped>
.interactive-time-slot-editor {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 16px;

    .toolbar-left {
      .title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .subtitle {
        font-size: 12px;
        color: #909399;
        margin-left: 8px;
      }
    }

    .toolbar-right {
      display: flex;
      gap: 8px;
    }
  }

  .time-grid-container {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    overflow: hidden;
    background-color: #fff;
    user-select: none;

    .grid-header {
      display: flex;
      background-color: #f8f9fa;
      border-bottom: 1px solid #e4e7ed;

      .time-column-header {
        width: 80px;
        padding: 12px 8px;
        border-right: 1px solid #e4e7ed;
        font-weight: 600;
        text-align: center;
      }

      .day-header {
        flex: 1;
        padding: 8px;
        text-align: center;
        border-right: 1px solid #e4e7ed;

        &:last-child {
          border-right: none;
        }

        .day-name {
          font-weight: 600;
          color: #303133;
          margin-bottom: 2px;
        }

        .day-date {
          font-size: 11px;
          color: #909399;
        }
      }
    }

    .grid-body {
      max-height: 500px;
      overflow-y: auto;

      .time-row {
        display: flex;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .time-label {
          width: 80px;
          padding: 4px 8px;
          border-right: 1px solid #e4e7ed;
          font-size: 11px;
          color: #909399;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #fafbfc;
        }

        .time-cell {
          flex: 1;
          height: 48px;
          border-right: 1px solid #f0f0f0;
          position: relative;
          cursor: pointer;
          transition: all 0.15s ease;

          &:last-child {
            border-right: none;
          }

          &:hover:not(.readonly):not(.selected) {
            background-color: #e1f3d8;
            transform: scale(1.01);
            box-shadow: inset 0 0 0 1px #67c23a;
          }

          &.selected {
            background-color: #67c23a;
            box-shadow: inset 0 0 0 1px #5daf34;

            &:hover {
              background-color: #5daf34;
              transform: scale(0.99);
            }
          }

          &.selecting {
            background-color: #a0cfff;
            box-shadow: inset 0 0 0 1px #409eff;
            animation: pulse 0.8s infinite;
          }

          &.readonly {
            cursor: not-allowed;
            opacity: 0.6;
          }

          .cell-content {
            height: 100%;
            position: relative;
            display: flex;
            flex-direction: column;

            .slot-label {
              position: absolute;
              top: 2px;
              left: 2px;
              right: 2px;
              font-size: 10px;
              color: #fff;
              background-color: rgba(0, 0, 0, 0.2);
              padding: 1px 4px;
              border-radius: 2px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              z-index: 2;
            }

            .cell-actions {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              display: flex;
              gap: 4px;
              z-index: 10;
              background: rgba(255, 255, 255, 0.95);
              padding: 2px;
              border-radius: 4px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

              .el-button {
                padding: 2px 6px;
                font-size: 12px;
                height: auto;
                min-height: 20px;
              }
            }

            .minute-indicators {
              position: absolute;
              bottom: 2px;
              left: 2px;
              right: 2px;
              display: flex;
              gap: 1px;
              height: 4px;

              .minute-indicator {
                flex: 1;
                height: 100%;
                background-color: rgba(255, 255, 255, 0.3);
                border-radius: 1px;
                transition: all 0.2s;

                &.selected {
                  background-color: rgba(255, 255, 255, 0.8);
                  box-shadow: 0 0 2px rgba(255, 255, 255, 0.5);
                }
              }
            }
          }
        }
      }
    }
  }

  .selected-info {
    margin-top: 16px;
    padding: 12px 16px;
    background-color: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 6px;

    .info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-weight: 500;
      color: #409eff;
    }

    .slots-preview {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .slot-preview {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 4px 8px;
        background-color: #fff;
        border: 1px solid #d9ecff;
        border-radius: 4px;
        font-size: 12px;

        .slot-day {
          color: #606266;
          font-weight: 500;
        }

        .slot-time {
          color: #303133;
        }
      }
    }
  }

  .template-list {
    .template-item {
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
      }

      .template-name {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }

      .template-description {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}
</style>
