# 产品配置列表订单核销功能实现

## 功能概述

在产品配置列表页面添加了订单核销入口，用户可以从某个产品进入订单核销，系统会默认选中该产品，然后用户选择学生进行核销。

## 实现的功能

### 1. 产品列表页面新增订单核销按钮

- 在产品配置列表的操作列中添加了"订单核销"按钮
- 只有上架状态的产品才显示核销按钮
- 需要 `order:writeoff:apply` 权限才能看到按钮

### 2. 学生选择对话框

当点击产品的"订单核销"按钮时，会打开学生选择对话框，包含以下功能：

#### 产品信息展示
- 显示选中产品的基本信息：产品名称、学科信息、适用年级、客户类型

#### 学生搜索和筛选
- 支持按学生姓名搜索
- 支持按手机号搜索
- 分页显示学生列表

#### 学生适用性验证
系统会自动验证每个学生是否适用于选中的产品，验证规则包括：

1. **年级匹配**：学生年级必须在产品的适用年级范围内
2. **家长信息完整性**：学生必须设置了家长姓名和家长手机号
3. **客户类型匹配**：根据产品的客户类型限制进行验证
   - 通用：所有学生都可以选择
   - 仅新客可用：学生之前没有购买过该学科和课型的产品
   - 仅老客可用：学生之前购买过该学科和课型的产品

#### 学生状态显示
- 适用的学生：显示绿色的"适用"标签，可以选择
- 不适用的学生：
  - 年级标签显示为红色
  - 显示具体的不适用原因（年级不适用、家长信息不完整等）
  - 选择按钮被禁用

### 3. 订单核销流程

选择学生后，会打开订单核销对话框：
- 自动填入选中的学生信息
- 自动选中当前产品（不再显示产品选择界面）
- 直接显示选中产品的详细信息
- 用户填写订单信息和上传订单截图
- 提交核销申请

### 重要改进
**从产品进入核销时，不再需要重新选择产品**：
- 系统会直接显示选中产品的信息卡片
- 产品信息以绿色边框的卡片形式展示
- 显示"已选中"标签，用户无需再次选择
- 避免了用户选择学生后又跳转到产品选择页面的问题

## 技术实现

### 文件修改

1. **src/views/management/product/index.vue**
   - 添加了订单核销按钮
   - 新增学生选择对话框
   - 实现学生搜索、分页、适用性验证等功能

2. **src/views/management/order-writeroff/components/OrderWriteroffDialog.vue**
   - 添加了 `productInfo` prop 支持
   - 当传入产品信息时自动选中该产品

### 核心功能函数

- `handleWriteoff(row)`: 处理产品核销按钮点击
- `getStudentList()`: 获取学生列表
- `isStudentApplicable(student)`: 检查学生是否适用于产品
- `getStudentIncompatibleReasons(student)`: 获取学生不适用的具体原因
- `isProductApplicableForStudent(product, student)`: 检查产品年级是否适用
- `isParentInfoComplete(student)`: 检查家长信息是否完整

### 样式优化

- 添加了学生选择对话框的样式
- 不适用原因的标签展示
- 响应式布局支持

## 使用流程

1. 用户进入产品配置列表页面
2. 找到需要核销的产品，点击"订单核销"按钮
3. 在学生选择对话框中搜索并选择合适的学生
4. 系统会自动验证学生是否适用，不适用的学生会显示具体原因
5. 选择适用的学生后，进入订单核销流程
6. 填写订单信息并提交核销申请

## 权限控制

- 订单核销按钮需要 `order:writeoff:apply` 权限
- 与现有的销售学生管理中的订单核销功能保持一致的权限控制

## 与现有功能的一致性

该功能与销售学生管理的订单核销入口逻辑保持完全一致：
- 使用相同的学生选择验证规则
- 使用相同的OrderWriteroffDialog组件
- 保持相同的用户体验和交互流程
