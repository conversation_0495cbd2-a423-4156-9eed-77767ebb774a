<template>
  <div class="stage-container">
    <div class="stage-header">
      <div class="stage-title">{{ (stepIndex || 0) + 1 }}. 单词填空测验</div>
    </div>

    <div class="stage-content">
      <div class="word-card" style="display: flex">
        <!-- 翻译和音标显示区域 -->
        <div class="word-info-section">
          <!-- 音标和发音区域 -->
          <div class="pronunciation-section">
            <!-- 英式音标 -->
            <div class="pronunciation-item">
              <span class="detail-label">英式：</span>
              <span class="phonetic" @click="playAudio('uk')">
                /{{ currentStepInfo?.wordInfo?.phoneticUk }}/
                <el-icon class="play-icon">
                  <VideoPlay />
                </el-icon>
              </span>
            </div>

            <!-- 美式音标 -->
            <div class="pronunciation-item">
              <span class="detail-label">美式：</span>
              <span class="phonetic" @click="playAudio('us')">
                /{{ currentStepInfo?.wordInfo?.phoneticUs }}/
                <el-icon class="play-icon">
                  <VideoPlay />
                </el-icon>
              </span>
            </div>
          </div>
          <!-- 中文翻译 -->
          <div class="translation-section">
            <span class="detail-label">翻译：</span>
            <div class="meaning">
              <span style="margin-left: 5px;" v-for="(pos, index) in currentStepInfo?.wordInfo?.meanings?.pos" :key="index">
                {{ pos.pos }}.{{ pos.def }} <span v-if="index < currentStepInfo?.wordInfo?.meanings?.pos.length - 1">；</span>
              </span>
            </div>
          </div>

        </div>

        <!-- 点击选择字母区域 -->
        <div v-if="showResult === false" class="clickable-letters-container">
          <div class="word-details" v-if="showResult === false">
            <div class="fill-blank-container">
              <template v-if="isMounted">
                <template v-for="(char, index) in currentStepInfo?.wordInfo?.word" :key="index">
                  <!-- 点击目标区域 -->
                  <div
                      v-if="shouldShowInput(index)"
                      class="click-zone"
                      :class="{
                    'click-zone-filled': droppedLetters[index]
                  }"
                      @click="clearPosition(index)"
                  >
                    {{ droppedLetters[index] || '' }}
                  </div>
                  <!-- 其他字符渲染为 span -->
                  <span v-else class="text">{{ char }}</span>
                </template>
              </template>
            </div>
          </div>
          <div class="letters-title">点击字母按钮填入空白处：</div>
          <div class="letters-area">
            <div
              v-for="(letter, index) in shuffledLetters"
              :key="`letter-${index}`"
              class="clickable-letter"
              :class="{ 'letter-used': letter.used }"
              @click="handleLetterClick(letter, index)"
            >
              {{ letter.char }}
            </div>
          </div>
          <div class="reset-button-container">
            <el-button type="info" @click="undoLastLetter" class="undo-btn" :disabled="fillHistory.length === 0">
              撤销上一步
            </el-button>
            <el-button type="warning" @click="resetClick" class="reset-btn">
              重新开始
            </el-button>
            <el-button type="primary" class="submit-btn" @click="handleSubmit">
              检查答案
            </el-button>
          </div>
        </div>
        <!-- 结果显示 -->
        <div v-else class="clickable-letters-container">
          <div class="word-details">
            <div class="fill-blank-container">
              <template v-if="isMounted ">
                <template v-for="(char, index) in currentStepInfo?.wordInfo?.word" :key="index">
                  <div
                      v-if="shouldShowInput(index)"
                      class="click-zone click-zone-result"
                  >
                    {{ droppedLetters[index] || getStudentAnswerChar(index) }}
                  </div>
                  <!-- 其他字符渲染为 span -->
                  <span v-else class="text">{{ char }}</span>
                </template>
              </template>
            </div>
          </div>
          <div class="letters-title">答题结果：</div>
          <div class="letters-area">
            <div
                v-for="(letter, index) in shuffledLetters"
                :key="`letter-${index}`"
                class="clickable-letter"
                :class="{ 'letter-used': letter.used }"
            >
              {{ letter.char }}
            </div>
          </div>
        </div>
      </div>
    </div>


    <!-- 答对时的星星特效 -->
    <div v-if="showStars" class="stars-container">
      <div v-for="n in 10" :key="n" class="star" :style="getRandomStarStyle()"></div>
    </div>

    <!-- 答对时的奖励徽章 -->
    <div v-if="showReward" class="reward-badge">
      <el-icon class="reward-icon">
        <Trophy />
      </el-icon>
      <span class="reward-text">答对啦</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, nextTick, onMounted, ref} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Trophy, VideoPlay} from '@element-plus/icons-vue'
import {
  COURSE_sessionStorage_INFO_KEY,
  CurrentStepInfo,
  getStepInfoByType,
  getWordStepResultByWordId,
  submitCourseStepApi
} from "@/api/course";
import {getRandomStarStyle, playAudioUtil} from '@/api/course/util'


const props = defineProps({
  selectWordText: String,
  courseId: {
    type: [String, Number],
    required: true
  },
  stepIndex: Number,
  sectionId: String
})
const emit = defineEmits(['complete'])

// const courseRequiredInfo = ref<CourseRequiredInfo | null>(null);
const currentStepInfo = ref<CurrentStepInfo | null>(null);
// 基础数据
const correctAnswer = ref('') // 当前单词的正确答案
const isMounted = ref(false)
const showStars = ref(false)
const showReward = ref(false)
const showResult = ref(false)
const answerFlag = ref<string>("错误")
const studentAnswerText = ref<string>('')

// 点击选择相关变量
const droppedLetters = ref<Record<number, string>>({}) // 存储每个位置放置的字母
const shuffledLetters = ref<Array<{char: string, used: boolean, originalIndex: number}>>([]) // 乱序的字母
const fillHistory = ref<Array<{position: number, letter: string, letterIndex: number}>>([]) // 填入历史记录，用于撤销

// 发音功能
const playAudio = (type: 'uk' | 'us') => {
  const audioUrl = type === 'uk'
      ? currentStepInfo.value?.wordInfo?.audioUkUrl
      : currentStepInfo.value?.wordInfo?.audioUsUrl;

  if (audioUrl) {
    // 使用预设音频
    const audio = new Audio(audioUrl);
    audio.play().catch(error => {
      console.error('音频播放失败:', error);
      // 降级到 Web Speech API
      playTextToSpeech(type);
    });
  } else {
    // 使用 Web Speech API
    playTextToSpeech(type);
  }
};

// 使用 Web Speech API 播放发音
const playTextToSpeech = (type: 'uk' | 'us') => {
  if ('speechSynthesis' in window) {
    const utterance = new SpeechSynthesisUtterance(currentStepInfo.value?.wordInfo?.word || '');
    utterance.lang = type === 'uk' ? 'en-GB' : 'en-US';
    utterance.rate = 0.8;
    speechSynthesis.speak(utterance);
  } else {
    ElMessage.warning('您的浏览器不支持语音播放功能');
  }
};

// 点击选择功能相关方法

// 提取需要填空的字母并打乱顺序
const generateShuffledLetters = () => {
  const word = currentStepInfo.value?.wordInfo?.word || ''
  const blankIndices = currentStepInfo.value?.step?.options || []

  // 提取需要填空的字母
  const lettersToShuffle = blankIndices.map((indexStr: string) => {
    const index = Number(indexStr)
    return {
      char: word[index],
      used: false,
      originalIndex: index
    }
  })

  // 打乱顺序
  for (let i = lettersToShuffle.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [lettersToShuffle[i], lettersToShuffle[j]] = [lettersToShuffle[j], lettersToShuffle[i]]
  }

  shuffledLetters.value = lettersToShuffle
}

// 点击字母处理
const handleLetterClick = (letter: {char: string, used: boolean, originalIndex: number}, letterIndex: number) => {
  if (letter.used) return

  // 找到下一个空白位置
  const blankIndices = currentStepInfo.value?.step?.options || []
  // 将字符串索引转换为数字并排序，然后找到第一个空白位置
  const sortedIndices = blankIndices
    .map(indexStr => Number(indexStr))
    .sort((a, b) => a - b)

  const nextEmptyPosition = sortedIndices.find(index => !droppedLetters.value[index])

  if (nextEmptyPosition !== undefined) {
    const position = nextEmptyPosition

    // 记录填入历史
    fillHistory.value.push({
      position,
      letter: letter.char,
      letterIndex
    })

    // 填入字母
    droppedLetters.value[position] = letter.char
    letter.used = true

    // 检查是否所有位置都填满了，如果是则自动验证
    checkAutoSubmit()
  }
}

// 点击位置清空
const clearPosition = (index: number) => {
  if (!droppedLetters.value[index]) return

  const letter = droppedLetters.value[index]

  // 找到对应的字母并标记为未使用
  const letterObj = shuffledLetters.value.find(l => l.char === letter && l.used)
  if (letterObj) {
    letterObj.used = false
  }

  // 清空位置
  delete droppedLetters.value[index]

  // 从历史记录中移除相关记录
  const historyIndex = fillHistory.value.findIndex(h => h.position === index)
  if (historyIndex > -1) {
    fillHistory.value.splice(historyIndex, 1)
  }
}

// 撤销上一步
const undoLastLetter = () => {
  if (fillHistory.value.length === 0) return

  const lastAction = fillHistory.value.pop()
  if (lastAction) {
    // 清空位置
    delete droppedLetters.value[lastAction.position]

    // 恢复字母可用状态
    const letterObj = shuffledLetters.value[lastAction.letterIndex]
    if (letterObj) {
      letterObj.used = false
    }
  }
}

// 重置点击选择
const resetClick = () => {
  droppedLetters.value = {}
  fillHistory.value = []
  shuffledLetters.value.forEach(letter => {
    letter.used = false
  })
}

// 检查是否可以自动提交
const checkAutoSubmit = () => {
  const blankIndices = currentStepInfo.value?.step?.options || []
  const allFilled = blankIndices.every((indexStr: string) => {
    const index = Number(indexStr)
    return droppedLetters.value[index]
  })
  
  if (allFilled) {
    // 延迟一下再自动提交，给用户一个反应时间
    setTimeout(() => {
      handleSubmit()
    }, 500)
  }
}

const handleSubmit = () => {
  // 从currentStepInfo中获取当前单词
  const currentWord = currentStepInfo.value?.wordInfo?.word || '';
  // 用户拖拽的结果
  if (finalText?.value !== currentWord) {
    answerFlag.value = "错误"
    ElMessage({
      message: '答案不正确，请再试一次！',
      type: 'error',
      duration: 2000
    })
    return;
  }
  answerFlag.value = "正确"
  showSuccessEffects()
  playAudioUtil(currentStepInfo.value?.wordInfo?.audioUkUrl)
}

// 判断是否应该显示 input
const shouldShowInput = (index: number) => {
  return currentStepInfo.value?.step?.options.some(i => Number(i) === index)
}

// 获取学生答案中指定位置的字符
const getStudentAnswerChar = (index: number) => {
  if (!studentAnswerText.value) return ''
  return studentAnswerText.value[index] || ''
}

// 计算最终拼接的字符串
const finalText = computed(() => {
  if (currentStepInfo.value?.wordInfo?.word == null) {
    return null;
  }
  return Array.from(currentStepInfo.value?.wordInfo?.word)
    .map((char, index) =>
      shouldShowInput(index) ? droppedLetters.value[index] || '' : char
    )
    .join('')
})

const showSuccessEffects = () => {
  showStars.value = true
  showReward.value = true
  setTimeout(() => {
    showStars.value = false
    showReward.value = false
  }, 2000)
}

/**
 * 提交课程步骤
 */
const submitCourseStep = ():number => {
  // debugger
  // 如果是已完成状态，不用重复提交
  let status = currentStepInfo.value.step.status
  if (status === "已完成" || showResult.value === true) {
    return 1;
  }
  // 检查是否已拖拽完成
  const blankIndices = currentStepInfo.value?.step?.options || []
  const hasAnswers = blankIndices.some((indexStr: string) => {
    const index = Number(indexStr)
    return droppedLetters.value[index]
  })
  
  if(!hasAnswers) {
    // ElMessageBox.alert("还未拖拽答案哦，请先拖拽字母到空白位置吧");
    return -1;
  }
  let wordId = currentStepInfo.value.wordId

  let submitResult = {
    stepId: currentStepInfo.value.step.id,
    result: answerFlag.value,
    studentAnswer: finalText.value
  }
  submitCourseStepApi(props.courseId, props.sectionId, wordId, submitResult, true)
  return 1;
}

// 模拟数据获取 - 仅在API调用失败时使用
const loadMockData = () => {
  const word = props.selectWordText || 'example';
  currentStepInfo.value = {
    sectionId: 'section1',
    wordId: 'word1',
    wordInfo: {
      word: word,
      syllables: word,
      phoneticUk: '',
      phoneticUs: '',
      difficulty: 'easy',
      audioUkUrl: '',
      audioUsUrl: '',
      videoUrl: '',
      meanings: {
        pos: [
          { pos: 'n.', def: '暂无翻译数据' }
        ],
        practices: []
      },
      sentences: []
    },
    step: {
      id: 'step1',
      type: '单词填空',
      status: '进行中',
      result: '',
      startTime: '',
      endTime: '',
      sentenceOrder: [],
      options: [
        { text: '0', isCorrect: false },
        { text: '2', isCorrect: false }
      ], // 需要填空的位置索引
      studentAnswer: '',
      answer: word
    }
  }
  correctAnswer.value = currentStepInfo.value?.step?.answer || word
}

// 数据验证和fallback处理
const validateAndSetStepInfo = (stepInfo: any) => {
  if (!stepInfo || !stepInfo.wordInfo) {
    console.warn('API返回的数据不完整，使用fallback数据');
    loadMockData();
    return;
  }

  // 确保必要字段存在
  const wordInfo = stepInfo.wordInfo;
  if (!wordInfo.phoneticUk) wordInfo.phoneticUk = '';
  if (!wordInfo.phoneticUs) wordInfo.phoneticUs = '';
  if (!wordInfo.meanings || !wordInfo.meanings.pos || wordInfo.meanings.pos.length === 0) {
    wordInfo.meanings = {
      pos: [{ pos: 'n.', def: '暂无翻译数据' }],
      practices: []
    };
  }

  currentStepInfo.value = stepInfo;
  correctAnswer.value = stepInfo.step?.answer || stepInfo.wordInfo?.word || '';
}

onMounted(async () => {
  // 先从sessionStorage获取
  let obj = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + props.courseId)
  if (obj !== null) {
    await nextTick();
    try {
      let courseInfo = JSON.parse(obj);
      currentStepInfo.value = await getStepInfoByType(courseInfo, '单词听写', props.selectWordText)
      if (currentStepInfo.value) {
        validateAndSetStepInfo(currentStepInfo.value);
        correctAnswer.value = currentStepInfo.value.step.answer
        // 生成打乱的字母
        generateShuffledLetters()
        isMounted.value = true
        if(currentStepInfo.value?.status == '待开始') {
          return ;
        }
        // 获取当前阶段的提交结果
        let submitCourseStepApiParam = getWordStepResultByWordId(props.courseId, props.sectionId, currentStepInfo.value.wordId, courseInfo) || {}
        // 如果本地存在提交结果，直接渲染出结果
        submitCourseStepApiParam?.params?.find((item) => {
          if (item.stepId == currentStepInfo.value.step.id && (item.studentAnswer != null && item.studentAnswer != '自动结束')) {
            showResult.value = true
            studentAnswerText.value = item.studentAnswer
          }
        })
      } else {
        console.warn('API返回空数据，使用fallback');
        loadMockData();
      }

      return;
    } catch (e) {
      console.error("Error parsing sessionStorage data:", e);
    }
  }

  // 如果没有真实数据，使用模拟数据
  loadMockData();
  generateShuffledLetters()
  isMounted.value = true
})

defineExpose({
  submitCourseStep
})
</script>

<style scoped>
.stage-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  overflow-y: auto;
}

.stage-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stage-title {
  font-size: 24px;
  font-weight: bold;
  color: #5d4037;
}

.word-card {
  width: 100%;
  padding: 20px 30px 30px 30px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  margin-top: 20px;
}

/* 新增样式：单词信息区域 */
.word-info-section {
  padding: 20px;
  background-color: #fff9e6;
  border-radius: 12px;
  border-left: 4px solid #FF9800;
}

.translation-section {
  margin-top: 5px;
  margin-bottom: 5px;
}

.pronunciation-section {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.pronunciation-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.phonetic {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px 12px;
  background-color: white;
  border-radius: 8px;
  border: 2px solid #FF9800;
  font-family: 'Times New Roman', serif;
  font-size: 18px;
  color: #5d4037;
}

.phonetic:hover {
  color: #FF9800;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 152, 0, 0.2);
}

.play-icon {
  margin-left: 8px;
  color: #FF9800;
  font-size: 18px;
  transition: all 0.3s ease;
}

.phonetic:hover .play-icon {
  transform: scale(1.2);
}

.detail-label {
  font-weight: bold;
  color: #FF9800;
  margin-right: 10px;
  font-size: 16px;
  min-width: 50px;
}

.meaning {
  font-size: 16px;
  line-height: 1.6;
  color: #5d4037;
}

.meaning div {
  margin-bottom: 8px;
  padding: 5px 0;
  border-bottom: 1px dashed #f2ad47;
}

.meaning div:last-child {
  border-bottom: none;
}

.example {
  font-size: 18px;
  color: #5d4037;
  line-height: 1.8;
  padding: 15px;
  background-color: #fff9e6;
  border-radius: 8px;
  border-left: 4px solid #f2ad47;
}

.stage-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-height: 0;
}
.word-display {
  font-size: 70px;
  font-weight: bold;
  color: #5d4037;
}
.word-display-text {
  font-family: "Comic Sans MS", cursive, sans-serif;
  margin-right: 40px;
}

.fill-blank-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 20px;
}

.fill-blank-container > div {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.fill-blank-container .text {
  font-size: 64px;
  color: #5d4037;
  font-family: "Comic Sans MS", cursive, sans-serif;
  padding: 10px;
}

.letter-input {
  width: 80px !important;
}

.letter-input :deep(.el-input__inner) {
  font-size: 64px;
  height: 80px;
  text-align: center;
  padding: 0;
  border-radius: 8px;
  border: 2px solid #FF9800;
  transition: all 0.3s ease;
  font-family: "Comic Sans MS", cursive, sans-serif;
}

.letter-input :deep(.el-input__inner):focus {
  border-color: #f2ad47;
  box-shadow: 0 0 0 2px rgba(242, 173, 71, 0.2);
}

.action-buttons {
  display: flex;
  justify-content: center;
}

.submit-btn {
  transition: all 0.3s ease;
}

.submit-btn:hover {
  transform: translateY(-2px);
}

.submit-btn:active {
  transform: translateY(0);
}

/* 星星特效样式 */
.stars-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
}

.star {
  position: absolute;
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFD700"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>');
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0;
}

@keyframes starAnimation {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    transform: scale(1) rotate(360deg);
    opacity: 0;
  }
}

/* 奖励徽章样式 */
.reward-badge {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  background-color: #ffd700;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  z-index: 101;
  pointer-events: none;
}

.reward-icon {
  font-size: 32px;
  color: #ffffff;
  margin-bottom: 4px;
}

.reward-text {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
}

@keyframes badgeAnimation {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }

  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }

  70% {
    transform: translate(-50%, -50%) scale(1);
  }

  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}
.sentence-parts {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
  margin-bottom: 20px;
}

.part-item {
  padding: 10px 15px;
  background: #fbd4a2;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #eee;
  font-size: 20px;
}

:deep(.el-input__wrapper){
  box-shadow: none;
}

/* 点击选择相关样式 */
.click-zone {
  width: 80px;
  height: 80px;
  border: 3px dashed #FF9800;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 64px;
  font-family: "Comic Sans MS", cursive, sans-serif;
  color: #5d4037;
  background-color: #fff9e6;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  flex-shrink: 0;
  box-sizing: border-box;
}

.click-zone:hover {
  border-color: #f2ad47;
  background-color: #fef7e0;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(242, 173, 71, 0.3);
}

.click-zone-filled {
  border-color: #4CAF50;
  background-color: #f1f8e9;
  border-style: solid;
}

.click-zone-filled:hover {
  border-color: #f44336;
  background-color: #ffebee;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.click-zone-result {
  border-color: #4CAF50;
  background-color: #f1f8e9;
  border-style: solid;
  cursor: default;
}

.drop-zone-result {
  border-color: #4CAF50;
  background-color: #f1f8e9;
  border-style: solid;
}

.clickable-letters-container {
  width: 100%;
  margin-left: 10px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 15px;
  border: 2px solid #e9ecef;
}

.letters-title {
  font-size: 18px;
  font-weight: bold;
  color: #5d4037;
  margin-bottom: 15px;
  text-align: center;
}

.letters-area {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
  margin-bottom: 20px;
  padding: 20px;
  background-color: white;
  border-radius: 10px;
  border: 1px solid #dee2e6;
}

.clickable-letter {
  width: 60px;
  height: 60px;
  background-color: #FF9800;
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
  font-family: "Comic Sans MS", cursive, sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
  user-select: none;
  border: none;
}

.clickable-letter:hover:not(.letter-used) {
  transform: translateY(-3px);
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.4);
  background-color: #f2ad47;
}

.clickable-letter:active:not(.letter-used) {
  transform: scale(0.95);
}

.letter-used {
  opacity: 0.3;
  cursor: not-allowed;
  background-color: #ccc;
  transform: none !important;
  box-shadow: none !important;
}

.letter-used:hover {
  transform: none !important;
  box-shadow: none !important;
  background-color: #ccc !important;
}

.reset-button-container {
  display: flex;
  justify-content: center;
}

.undo-btn {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 6px;
  background-color: #6c757d;
  border-color: #6c757d;
  transition: all 0.3s ease;
}

.undo-btn:hover:not(:disabled) {
  background-color: #5a6268;
  border-color: #5a6268;
}

.undo-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.reset-btn {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 6px;
  background-color: #ff9800;
  border-color: #ff9800;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  background-color: #f57c00;
  border-color: #f57c00;
}

/* 拖拽动画效果 */
@keyframes dragAnimation {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}



/* 动态字体缩放以避免滚动 */
@media (max-height: 800px) {
  .stage-container {
    font-size: 14px;
    padding: 18px;
  }
  
  .stage-title {
    font-size: 22px !important;
  }
  
  .word-display {
    font-size: 65px !important;
  }
  
  .fill-blank-container .text {
    font-size: 60px !important;
  }
  
  .letter-input :deep(.el-input__inner) {
    font-size: 60px !important;
    height: 75px !important;
  }
  
  .drop-zone {
    width: 75px !important;
    height: 75px !important;
    font-size: 60px !important;
  }
  
  .clickable-letter {
    width: 55px !important;
    height: 55px !important;
    font-size: 30px !important;
  }
  
  .detail-label {
    font-size: 15px !important;
  }
  
  .meaning {
    font-size: 15px !important;
  }
  
  .phonetic {
    font-size: 17px !important;
  }
}

@media (max-height: 700px) {
  .stage-container {
    font-size: 12px;
    padding: 15px;
  }
  
  .stage-title {
    font-size: 20px !important;
  }
  
  .word-display {
    font-size: 55px !important;
  }
  
  .fill-blank-container .text {
    font-size: 50px !important;
  }
  
  .letter-input :deep(.el-input__inner) {
    font-size: 50px !important;
    height: 65px !important;
  }
  
  .drop-zone {
    width: 65px !important;
    height: 65px !important;
    font-size: 50px !important;
  }
  
  .clickable-letter {
    width: 50px !important;
    height: 50px !important;
    font-size: 26px !important;
  }
  
  .word-card {
    padding: 15px 20px 20px 20px !important;
  }
  
  .detail-label {
    font-size: 14px !important;
  }
  
  .meaning {
    font-size: 14px !important;
  }
  
  .phonetic {
    font-size: 16px !important;
  }
  
  .reward-badge {
    width: 70px !important;
    height: 70px !important;
  }
  
  .reward-icon {
    font-size: 28px !important;
  }
  
  .reward-text {
    font-size: 14px !important;
  }
}

@media (max-height: 600px) {
  .stage-container {
    font-size: 11px;
    padding: 12px;
  }
  
  .stage-title {
    font-size: 18px !important;
  }
  
  .word-display {
    font-size: 45px !important;
  }
  
  .fill-blank-container .text {
    font-size: 40px !important;
  }
  
  .letter-input :deep(.el-input__inner) {
    font-size: 40px !important;
    height: 55px !important;
  }
  
  .drop-zone {
    width: 55px !important;
    height: 55px !important;
    font-size: 40px !important;
  }
  
  .clickable-letter {
    width: 45px !important;
    height: 45px !important;
    font-size: 22px !important;
  }
  
  .word-card {
    padding: 12px 15px 15px 15px !important;
  }
  
  .detail-label {
    font-size: 13px !important;
  }
  
  .meaning {
    font-size: 13px !important;
  }
  
  .phonetic {
    font-size: 15px !important;
  }
  
  .reward-badge {
    width: 60px !important;
    height: 60px !important;
  }
  
  .reward-icon {
    font-size: 24px !important;
  }
  
  .reward-text {
    font-size: 12px !important;
  }
}

@media (max-height: 500px) {
  .stage-container {
    font-size: 10px;
    padding: 10px;
  }
  
  .stage-title {
    font-size: 16px !important;
  }
  
  .word-display {
    font-size: 35px !important;
  }
  
  .fill-blank-container .text {
    font-size: 30px !important;
  }
  
  .letter-input :deep(.el-input__inner) {
    font-size: 30px !important;
    height: 45px !important;
  }
  
  .drop-zone {
    width: 45px !important;
    height: 45px !important;
    font-size: 30px !important;
  }
  
  .clickable-letter {
    width: 40px !important;
    height: 40px !important;
    font-size: 18px !important;
  }
  
  .word-card {
    padding: 10px 12px 12px 12px !important;
  }
  
  .detail-label {
    font-size: 12px !important;
  }
  
  .meaning {
    font-size: 12px !important;
  }
  
  .phonetic {
    font-size: 14px !important;
  }
  
  .reward-badge {
    width: 50px !important;
    height: 50px !important;
  }
  
  .reward-icon {
    font-size: 20px !important;
  }
  
  .reward-text {
    font-size: 10px !important;
  }
}
</style>