<template>
  <el-dialog 
    title="订单详情" 
    v-model="visible" 
    width="1200px" 
    append-to-body
    :before-close="handleClose"
    class="order-detail-dialog"
  >
    <div v-if="orderDetail" class="order-detail">
      <!-- 基本信息 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">基本信息</span>
          </div>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="订单号" :span="2">
            {{ orderDetail.no }}
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusType(orderDetail.orderStatus)">{{ orderDetail.orderStatus }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单标题" :span="3">
            <el-tag v-if="orderDetail.orderType === '首购'">{{orderDetail.orderType}}</el-tag>
            <el-tag v-else type="success">{{orderDetail.orderType}}</el-tag>
            <span class="order-title">&nbsp; {{ orderDetail.body }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="学生姓名">
            <div class="user-info">
              <span>{{ orderDetail.student.nickName }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="学生手机">
            {{ orderDetail.student.phone || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="学生邮箱">
            {{ orderDetail.student.email || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="销售员">
            <div class="user-info">
              <span>{{ orderDetail.saler.nickName }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="销售员手机">
            {{ orderDetail.saler.phone || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">
            <el-tag :type="orderDetail.trxMethod === '一次性支付' ? 'success' : 'warning'" size="small">
              {{ orderDetail.trxMethod }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 产品信息 -->
      <el-card v-if="orderDetail.product" class="detail-card" shadow="never">
        <template #header>
          <span class="card-title">产品信息</span>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="产品名称">
            {{ orderDetail.product.name }}
          </el-descriptions-item>
          <el-descriptions-item label="学科">
            <el-tag size="small">{{ orderDetail.product.subject }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="课型">
            <el-tag size="small">{{ orderDetail.product.courseType }}</el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="适用年级" :span="3">
            <el-tag size="small" style="margin-right: 10px" v-for="grade in orderDetail.product.applicableGrades">{{ grade }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="正课课时">
            <span>{{ orderDetail.product.quantity || '-' }}课时</span>
          </el-descriptions-item>
          <el-descriptions-item label="赠送课时">
            {{ orderDetail.product.bonusHoursQuantity || '-' }}课时
          </el-descriptions-item>
          <el-descriptions-item label="是否包含教材费用">
            <span v-if="orderDetail.product.hasMaterialFee">是</span>
            <span v-else>否</span>
          </el-descriptions-item>

          <el-descriptions-item label="产品描述" :span="3">
            {{ orderDetail.product.description || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 金额信息 -->
      <el-card class="detail-card" shadow="never" v-if="orderDetail.source === '系统创建'">
        <template #header>
          <span class="card-title">金额信息</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="amount-item">
              <div class="amount-label">订单金额</div>
              <div class="amount-value total">¥{{ (orderDetail.totalAmt / 100).toFixed(2) }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="amount-item">
              <div class="amount-label">已付款</div>
              <div class="amount-value paid">¥{{ (orderDetail.amtPaid / 100).toFixed(2) }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="amount-item">
              <div class="amount-label">未支付</div>
              <div class="amount-value unpaid">¥{{ (orderDetail.amtUnpaid / 100).toFixed(2) }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="amount-item">
              <div class="amount-label">支付进度</div>
              <div class="progress-container">
                <el-progress 
                  :percentage="getPaymentProgress()" 
                  :stroke-width="8"
                  :show-text="false"
                />
                <span class="progress-text">{{ getPaymentProgress() }}%</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 核销信息 -->
      <el-card class="detail-card" shadow="never" v-else>
        <template #header>
          <span class="card-title">核销信息</span>
        </template>
        <el-descriptions :column="3" border v-if="orderDetail.writerOffs">
          <el-descriptions-item label="核销人员">
            {{ orderDetail.writerOffs.woffUserName }}
          </el-descriptions-item>
          <el-descriptions-item label="审核人员">
            {{ orderDetail.writerOffs.approveUserName }}
          </el-descriptions-item>
          <el-descriptions-item label="审核结果">
            <el-tag size="small">{{ orderDetail.writerOffs.status }}</el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="审核时间">
            {{ formatDateTime(orderDetail.writerOffs.approveTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="核销备注">
            <span>{{ orderDetail.writerOffs.remark || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="审核备注">
            <span>{{ orderDetail.writerOffs.reason || '-' }}</span>
          </el-descriptions-item>
        </el-descriptions>
        <el-alert title="暂无核销信息" type="warning" v-else/>
      </el-card>

      <!-- 时间信息 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <span class="card-title">时间信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(orderDetail.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后支付时间">
            {{ orderDetail.lastPayTime ? formatDateTime(orderDetail.lastPayTime) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ orderDetail.updateTime ? formatDateTime(orderDetail.updateTime) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="合同签署状态">
            <el-tag :type="getSignStatusType(orderDetail.signStatus)" size="small">
              {{ orderDetail.signStatus || '-' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 交易流水 -->
      <el-card v-if="orderDetail.transactions && orderDetail.transactions.length > 0" class="detail-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">交易流水</span>
          </div>
        </template>
        <el-table :data="orderDetail.transactions" border stripe>
          <el-table-column label="流水号" prop="cusTrxSeq" width="180" show-overflow-tooltip />
          <el-table-column label="期数" prop="trxIdx" width="80" align="center">
            <template #default="{ row }">
              <el-tag size="small" type="info">第{{ row.trxIdx }}期</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="金额" prop="trxAmt" width="120" align="center">
            <template #default="{ row }">
              <span class="amount-text">¥{{ (row.trxAmt / 100).toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="购买课时" prop="buyHours" width="120" align="center">
            <template #default="{ row }">
              {{ row.courseHours?.releasedPurchasedHours }}
            </template>
          </el-table-column>
          <el-table-column label="赠送课时" prop="giftHours" width="120" align="center">
            <template #default="{ row }">
              {{ row.courseHours?.releasedGiftHours }}
            </template>
          </el-table-column>
          <el-table-column label="消耗课时" prop="buyHours" width="120" align="center">
            <template #default="{ row }">
              {{ row.calcCourseHours?.consumedFromThisPayment }}
            </template>
          </el-table-column>
          <el-table-column label="退款课时" prop="giftHours" width="120" align="center">
            <template #default="{ row }">
              {{ row.calcCourseHours?.refundableHours }}
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="trxStatus" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.trxStatus)" size="small">{{ row.trxStatus }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="支付方式" prop="payMethod" width="120" align="center">
            <template #default="{ row }">
              {{ row.payType || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="支付时间" prop="payTime" width="160">
            <template #default="{ row }">
              {{ row.payTime ? formatDateTime(row.payTime) : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="操作"  fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="row.trxStatus === '未付款'"
                size="small"
                type="primary"
                @click="handlePayTransaction(row)"
              >发起支付</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 状态变更历史 -->
      <el-card v-if="orderDetail.statusHistory && orderDetail.statusHistory.length > 0" class="detail-card" shadow="never">
        <template #header>
          <span class="card-title">状态变更历史</span>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in orderDetail.statusHistory"
            :key="index"
            :timestamp="formatDateTime(item.createTime)"
            :type="getTimelineType(item.status)"
          >
            <div class="timeline-content">
              <div class="status-change">
                <span class="from-status">{{ item.fromStatus || '初始状态' }}</span>
                <el-icon class="arrow-icon"><ArrowRight /></el-icon>
                <span class="to-status">{{ item.toStatus }}</span>
              </div>
              <div v-if="item.remark" class="status-remark">{{ item.remark }}</div>
              <div class="operator-info">操作人：{{ item.operatorName || '系统' }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>

      <!-- 备注信息 -->
      <el-card v-if="orderDetail.remark" class="detail-card" shadow="never">
        <template #header>
          <span class="card-title">备注信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单备注"  :span="3">
            {{ orderDetail.remark }}
          </el-descriptions-item>
          <el-descriptions-item label="申请退款备注">
            {{ orderDetail.memo.refundApplyReasonType || '-' }}
            <span v-if="orderDetail.memo.refundApplyMemo">（{{ orderDetail.memo.refundApplyMemo || '-' }}）</span>
          </el-descriptions-item>
          <el-descriptions-item label="退款审核备注" >
            {{ orderDetail.memo.refundApproveMemo || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="核销申请备注" >
            {{ orderDetail.memo.writerOffApplyMemo || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="核销审核备注">
            {{ orderDetail.memo.writerOffApproveMemo || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 支付对话框 -->
  <PaymentDialog
    v-model="paymentDialogVisible"
    :transaction="currentTransaction"
    @success="handlePaymentSuccess"
  />
</template>

<script setup>
import {computed, ref} from 'vue'
import {ElMessage} from 'element-plus'
import {ArrowRight} from '@element-plus/icons-vue'
import {formatDateTime} from '@/utils/date.js'
import PaymentDialog from './PaymentDialog.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderDetail: {
    type: Object,
    default: null
  },
  showActions: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh', 'edit', 'pay-transaction'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const paymentDialogVisible = ref(false)
const currentTransaction = ref(null)

// 方法
const handleClose = () => {
  visible.value = false
}

const handleEdit = () => {
  emit('edit', props.orderDetail)
}

const handlePayTransaction = (transaction) => {
  currentTransaction.value = transaction
  // paymentDialogVisible.value = true
  emit('pay-transaction', transaction)
}

const handlePaymentSuccess = () => {
  emit('refresh')
  ElMessage.success('支付成功')
}

const getPaymentProgress = () => {
  if (!props.orderDetail || props.orderDetail.totalAmt === 0) return 0
  return Math.round((props.orderDetail.amtPaid / props.orderDetail.totalAmt) * 100)
}

const getStatusType = (status) => {
  const statusMap = {
    '未支付': 'warning',
    '已全额支付': 'success',
    '已取消': 'danger',
    '已退款': 'info',
    '已部分支付': 'primary'
  }
  return statusMap[status] || 'info'
}

const getSignStatusType = (signStatus) => {
  const statusMap = {
    '未签署': 'warning',
    '已签署': 'success',
    '签署中': 'primary',
    '签署失败': 'danger'
  }
  return statusMap[signStatus] || 'warning'
}

const getTimelineType = (status) => {
  const typeMap = {
    '已支付': 'success',
    '已取消': 'danger',
    '已退款': 'warning'
  }
  return typeMap[status] || 'primary'
}
</script>

<style scoped>
.order-detail-dialog {
  .order-detail {
    max-height: 70vh;
    overflow-y: auto;
  }
}

.detail-card {
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.order-title {
  font-weight: 500;
  color: #409EFF;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  background-color: #409EFF;
  color: white;
  font-size: 12px;
}

.amount-item {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  background: #f8f9fa;
}

.amount-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.amount-value {
  font-size: 24px;
  font-weight: bold;
  
  &.total {
    color: #409EFF;
  }
  
  &.paid {
    color: #67C23A;
  }
  
  &.unpaid {
    color: #F56C6C;
  }
}

.progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 14px;
  font-weight: 500;
  color: #409EFF;
}

.amount-text {
  font-weight: 500;
  color: #67C23A;
}

.timeline-content {
  .status-change {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    
    .from-status {
      color: #909399;
    }
    
    .arrow-icon {
      color: #409EFF;
    }
    
    .to-status {
      color: #303133;
      font-weight: 500;
    }
  }
  
  .status-remark {
    color: #666;
    font-size: 14px;
    margin-bottom: 4px;
  }
  
  .operator-info {
    color: #909399;
    font-size: 12px;
  }
}

.remark-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #666;
  line-height: 1.6;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>