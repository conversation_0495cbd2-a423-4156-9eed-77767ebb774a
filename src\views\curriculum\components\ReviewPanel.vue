<template>
  <div class="review-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon class="title-icon"><Reading /></el-icon>
        抗遗忘复习
      </h3>
      <el-button
        type="primary"
        size="small"
        :icon="Refresh"
        @click="handleRefresh"
        :loading="loading"
        circle
      />
    </div>

    <!-- 标签页切换 -->
    <el-tabs v-model="activeTab" class="review-tabs" @tab-change="handleTabChange">
      <el-tab-pane name="today">
        <template #label>
          <span class="tab-label">
            今日
            <el-badge
              v-if="todayCount > 0"
              :value="todayCount"
              class="tab-badge"
              :max="99"
            />
          </span>
        </template>

        <!-- 今日的复习列表 -->
        <div class="review-list" v-loading="loading">
          <div v-if="todayReviews.length === 0" class="empty-state">
            <el-empty description="今日无抗遗忘复习" :image-size="100">
              <template #image>
                <el-icon class="empty-icon"><Calendar /></el-icon>
              </template>
            </el-empty>
          </div>

          <ReviewItem
            v-for="review in todayReviews"
            :key="review.id"
            :review="review"
            @review-start="handleReviewStart"
            @review-complete="handleReviewComplete"
            @review-view="handleReviewView"
            @upload-click="handleUploadClick"
            @view-upload="handleViewUpload"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane name="overdue">
        <template #label>
          <span class="tab-label">
            已逾期
            <el-badge
              v-if="overdueCount > 0"
              :value="overdueCount"
              class="tab-badge"
              :max="99"
              type="danger"
            />
          </span>
        </template>

        <!-- 已逾期的复习列表 -->
        <div class="review-list" v-loading="loading">
          <div v-if="overdueReviews.length === 0" class="empty-state">
            <el-empty description="无逾期抗遗忘复习" :image-size="100">
              <template #image>
                <el-icon class="empty-icon"><WarningFilled /></el-icon>
              </template>
            </el-empty>
          </div>

          <!-- 按日期分组显示 -->
          <div
            v-for="group in groupedOverdueReviews"
            :key="group.date"
            class="date-group"
          >
            <div class="date-header overdue-header">
              <el-icon class="date-icon"><WarningFilled /></el-icon>
              <span class="date-text">{{ formatGroupDate(group.date) }}</span>
              <span class="count-text">({{ group.reviews.length }}项)</span>
            </div>

            <ReviewItem
              v-for="review in group.reviews"
              :key="review.id"
              :review="review"
              @review-start="handleReviewStart"
              @review-complete="handleReviewComplete"
              @review-view="handleReviewView"
              @upload-click="handleUploadClick"
              @view-upload="handleViewUpload"
            />
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane name="future">
        <template #label>
          <span class="tab-label">
            未来
            <el-badge
              v-if="futureCount > 0"
              :value="futureCount"
              class="tab-badge"
              :max="99"
            />
          </span>
        </template>

        <!-- 未来复习列表 -->
        <div class="review-list" v-loading="loading">
          <div v-if="futureReviews.length === 0" class="empty-state">
            <el-empty description="暂无未来抗遗忘复习" :image-size="100">
              <template #image>
                <el-icon class="empty-icon"><Clock /></el-icon>
              </template>
            </el-empty>
          </div>

          <!-- 按日期分组显示 -->
          <div v-for="group in groupedFutureReviews" :key="group.date" class="date-group">
            <div class="date-header">
              <el-icon class="date-icon"><Calendar /></el-icon>
              <span class="date-text">{{ formatGroupDate(group.date) }}</span>
              <span class="count-text">({{ group.reviews.length }}项)</span>
            </div>

            <ReviewItem
              v-for="review in group.reviews"
              :key="review.id"
              :review="review"
              @review-start="handleReviewStart"
              @review-complete="handleReviewComplete"
              @review-view="handleReviewView"
              @upload-click="handleUploadClick"
              @view-upload="handleViewUpload"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 快速统计 -->
    <div class="review-stats" v-if="showStats">
      <div class="stat-item">
        <span class="stat-label">待复习</span>
        <span class="stat-value 待开始">{{ WaittingCount }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">进行中</span>
        <span class="stat-value 进行中">{{ inProcessingCount }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已完成</span>
        <span class="stat-value 已完成">{{ completedCount }}</span>
      </div>
    </div>

    <!-- 上传对话框 -->
    <ReviewUploadDialog
      v-model="uploadDialog.visible"
      :review-schedule-id="uploadDialog.reviewScheduleId"
      :mode="uploadDialog.mode"
      @uploaded="handleUploadSuccess"
      @close="handleUploadDialogClose"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import {
  Reading,
  Refresh,
  Calendar,
  Clock,
  WarningFilled,
} from "@element-plus/icons-vue";
import { useCurriculumStore } from "@/stores/curriculum";
import ReviewItem from "./ReviewItem.vue";
import ReviewUploadDialog from "./ReviewUploadDialog.vue";

const props = defineProps({
  teacherId: {
    type: String,
    default: "",
  },
  studentId: {
    type: String,
    default: "",
  },
  showStats: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["reviewStart", "reviewComplete", "reviewView"]);

const curriculumStore = useCurriculumStore();
const activeTab = ref("today");
const loading = ref(false);

// 存储所有复习数据
const allReviews = ref([]);

// 上传对话框状态
const uploadDialog = ref({
  visible: false,
  reviewScheduleId: '',
  mode: 'upload' // 'upload' | 'view'
});

// 计算属性
// 确定查询参数：老师查看所有，学生查看自己
const queryParams = computed(() => {
  // 如果有studentId，说明是学生查看自己的
  if (props.studentId) {
    return { studentId: props.studentId };
  }

  // 如果有teacherId，说明是老师查看所有学生的
  if (props.teacherId) {
    return { teacherId: props.teacherId };
  }

  return {};
});

// 根据时间和状态分类复习数据
const todayReviews = computed(() => {
  const today = new Date();
  const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const todayEnd = new Date(todayStart);
  todayEnd.setDate(todayEnd.getDate() + 1);

  return allReviews.value.filter((review) => {
    const reviewDate = new Date(review.scheduledTime);
    return reviewDate >= todayStart && reviewDate < todayEnd;
  });
});

const overdueReviews = computed(() => {
  const today = new Date();
  const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());

  return allReviews.value.filter((review) => {
    const reviewDate = new Date(review.scheduledTime);
    return reviewDate < todayStart && review.status !== "已完成";
  });
});

const futureReviews = computed(() => {
  const today = new Date();
  const tomorrowStart = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate() + 1
  );

  return allReviews.value.filter((review) => {
    const reviewDate = new Date(review.scheduledTime);
    return reviewDate >= tomorrowStart;
  });
});

// 数量统计
const todayCount = computed(() => todayReviews.value.length);
const overdueCount = computed(() => overdueReviews.value.length);
const futureCount = computed(() => futureReviews.value.length);

// 分组显示逻辑
const groupedOverdueReviews = computed(() => {
  return groupReviewsByDate(overdueReviews.value);
});

const groupedFutureReviews = computed(() => {
  return groupReviewsByDate(futureReviews.value);
});

// 通用的按日期分组函数
const groupReviewsByDate = (reviews) => {
  const groups = {};

  reviews.forEach((review) => {
    const reviewDate = review.scheduledTime;
    if (!reviewDate) return;

    const date = new Date(reviewDate).toDateString();

    if (!groups[date]) {
      groups[date] = {
        date: date,
        reviews: [],
      };
    }
    groups[date].reviews.push(review);
  });

  // 按日期排序
  return Object.values(groups).sort((a, b) => new Date(a.date) - new Date(b.date));
};

// 统计数据
const WaittingCount = computed(() => {
  return allReviews.value.filter((review) => review.status === "待开始").length;
});

const inProcessingCount = computed(() => {
  return allReviews.value.filter((review) => review.status === "进行中").length;
});

const completedCount = computed(() => {
  return allReviews.value.filter((review) => review.status === "已完成").length;
});

// 方法
const fetchAllReviewData = async () => {
  loading.value = true;
  try {
    // 检查是否有有效的查询参数
    if (!queryParams.value.studentId && !queryParams.value.teacherId) {
      console.warn("🔄 [抗遗忘复习] 缺少查询参数，无法获取复习数据");
      console.warn("🔄 [抗遗忘复习] 当前props:", {
        teacherId: props.teacherId,
        studentId: props.studentId,
      });
      return;
    }

    // 只调用一次接口，获取所有复习数据
    const params = {
      ...queryParams.value,
      statuses: ["待开始", "进行中"],
      // 不设置时间范围限制，获取所有数据
      orderBy: "scheduled_time",
      orderDirection: "ASC",
    };

    console.log("🔄 [抗遗忘复习] 获取所有复习数据:", params);
    await curriculumStore.fetchReviewPlans(params);

    // 将数据存储到本地状态
    allReviews.value = curriculumStore.reviewPlans || [];

    console.log("🔄 [抗遗忘复习] 复习数据获取完成");
    console.log("🔄 [抗遗忘复习] 总数据量:", allReviews.value.length);
    console.log("🔄 [抗遗忘复习] 今日数量:", todayCount.value);
    console.log("🔄 [抗遗忘复习] 逾期数量:", overdueCount.value);
    console.log("🔄 [抗遗忘复习] 未来数量:", futureCount.value);
  } catch (error) {
    console.error("🔄 [抗遗忘复习] 获取复习数据失败:", error);
    allReviews.value = [];
  } finally {
    loading.value = false;
  }
};

const handleTabChange = (tabName) => {
  console.log("🔄 [抗遗忘复习] 标签切换:", tabName);
  // 标签切换时不需要重新获取数据，因为所有数据已经在本地
};

const handleRefresh = () => {
  console.log("🔄 [抗遗忘复习] 刷新数据");
  // 刷新时重新获取所有数据
  fetchAllReviewData();
};

const formatGroupDate = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  const diffDays = Math.floor((targetDate - today) / (1000 * 60 * 60 * 24));

  if (diffDays === 1) {
    return "明天";
  } else if (diffDays === 2) {
    return "后天";
  } else if (diffDays <= 7) {
    const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    return weekdays[date.getDay()];
  } else {
    return date.toLocaleDateString("zh-CN", {
      month: "2-digit",
      day: "2-digit",
    });
  }
};

// 事件处理
const handleReviewStart = (review) => {
  emit("reviewStart", review);
};

const handleReviewComplete = (review) => {
  emit("reviewComplete", review);
};

const handleReviewView = (review) => {
  emit("reviewView", review);
};

// 处理上传点击
const handleUploadClick = (review) => {
  console.log('🔄 [抗遗忘复习] ReviewPanel 处理上传点击:', review)
  console.log('🔄 [抗遗忘复习] 设置对话框状态:', {
    visible: true,
    reviewScheduleId: review.id,
    mode: 'upload'
  })
  uploadDialog.value = {
    visible: true,
    reviewScheduleId: review.id,
    mode: 'upload'
  };
  console.log('🔄 [抗遗忘复习] 对话框状态已设置:', uploadDialog.value)
};

// 处理查看上传内容
const handleViewUpload = (review) => {
  uploadDialog.value = {
    visible: true,
    reviewScheduleId: review.id,
    mode: 'view'
  };
};

// 处理上传成功
const handleUploadSuccess = (result) => {
  console.log('上传成功:', result);
  // 刷新数据
  fetchAllReviewData();
};

// 处理上传对话框关闭
const handleUploadDialogClose = () => {
  uploadDialog.value = {
    visible: false,
    reviewScheduleId: '',
    mode: 'upload'
  };
};

// 初始化数据
const initializeData = async () => {
  console.log("🔄 [抗遗忘复习] 初始化数据");

  // 检查是否有有效的查询参数
  if (!queryParams.value.studentId && !queryParams.value.teacherId) {
    console.warn("🔄 [抗遗忘复习] 缺少查询参数，跳过初始化");
    return;
  }

  // 获取所有复习数据
  await fetchAllReviewData();
};

// 监听属性变化
watch(
  [() => props.teacherId, () => props.studentId],
  () => {
    // 当老师ID或学生ID变化时，重新初始化数据
    console.log("🔄 [抗遗忘复习] 参数变化，重新初始化数据:", {
      teacherId: props.teacherId,
      studentId: props.studentId,
      queryParams: queryParams.value,
    });

    if (queryParams.value.studentId || queryParams.value.teacherId) {
      initializeData();
    }
  },
  { immediate: true }
);

onMounted(() => {
  console.log("🔄 [抗遗忘复习] 组件挂载，开始初始化");
  initializeData();
});
</script>

<style lang="scss" scoped>
.review-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: 0; /* 关键：允许flex容器收缩 */
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .panel-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;

    .title-icon {
      margin-right: 6px;
      font-size: 18px;
    }
  }
}

.review-tabs {
  flex: 1;
  min-height: 0; /* 关键：允许tabs容器收缩 */
  display: flex;
  flex-direction: column;

  :deep(.el-tabs__header) {
    margin: 0;
    background: #f8f9fa;
    padding: 0 16px;
    flex-shrink: 0; /* 防止头部被压缩 */
  }

  :deep(.el-tabs__nav-wrap) {
    &::after {
      display: none;
    }
  }

  :deep(.el-tabs__item) {
    font-weight: 500;
    color: #6b7280;
    font-size: 14px;
    padding: 0 12px;
    height: 36px;
    line-height: 36px;

    &.is-active {
      color: #667eea;
    }
  }

  :deep(.el-tabs__active-bar) {
    background-color: #667eea;
  }

  :deep(.el-tabs__content) {
    flex: 1;
    min-height: 0; /* 关键：允许内容区域收缩 */
    padding: 0;
    overflow: hidden; /* 防止内容溢出 */
  }

  :deep(.el-tab-pane) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
}

.tab-badge {
  :deep(.el-badge__content) {
    transform: translateX(50%);
  }
}

.review-list {
  flex: 1;
  min-height: 0; /* 关键：允许flex子元素收缩 */
  padding: 12px 16px;
  overflow-y: auto;

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;

  .empty-icon {
    font-size: 36px;
    color: #d1d5db;
  }
}

.date-group {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.date-header {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  background: #f8fafc;
  border-radius: 6px;
  margin-bottom: 8px;
  border-left: 3px solid #667eea;

  .date-icon {
    color: #667eea;
    margin-right: 6px;
    font-size: 14px;
  }

  .date-text {
    font-weight: 600;
    color: #374151;
    font-size: 13px;
  }

  .count-text {
    margin-left: 6px;
    color: #6b7280;
    font-size: 12px;
  }

  &.overdue-header {
    background: #fef2f2;
    border-left-color: #ef4444;

    .date-icon {
      color: #ef4444;
    }

    .date-text {
      color: #dc2626;
    }

    .count-text {
      color: #ef4444;
    }
  }
}

.review-stats {
  display: flex;
  justify-content: space-around;
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e5e7eb;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;

  .stat-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
  }

  .stat-value {
    font-size: 18px;
    font-weight: 700;

    &.待开始 {
      color: #f59e0b;
    }

    &.进行中 {
      color: #3b82f6;
    }

    &.已完成 {
      color: #10b981;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .review-panel {
    height: 100%;
    max-height: 70vh; /* 增加最大高度 */
    min-height: 400px; /* 确保最小高度 */
  }

  .panel-header {
    padding: 10px 14px;
    flex-shrink: 0; /* 防止头部被压缩 */

    .panel-title {
      font-size: 15px;
    }
  }

  .review-tabs {
    flex: 1;
    min-height: 0; /* 关键：允许tabs容器收缩 */

    :deep(.el-tabs__header) {
      flex-shrink: 0; /* 防止标签头被压缩 */
      padding: 0 14px;
    }

    :deep(.el-tabs__content) {
      flex: 1;
      min-height: 0; /* 关键：允许内容区域收缩 */
      overflow: hidden; /* 防止内容溢出 */
    }

    :deep(.el-tab-pane) {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  .review-list {
    flex: 1;
    min-height: 0; /* 关键：允许列表收缩 */
    padding: 10px 14px;
    /* 移除固定的max-height，让它自适应 */
  }

  .review-stats {
    padding: 12px 16px;
    flex-shrink: 0; /* 防止统计区域被压缩 */
  }

  .date-group {
    margin-bottom: 12px; /* 减少移动端的间距 */
  }

  .date-header {
    padding: 4px 8px; /* 减少移动端的内边距 */
    margin-bottom: 6px;
  }
}
</style>
