<!--<template>-->
<!--  <el-dialog-->
<!--    v-model="visible"-->
<!--    title="选择学生"-->
<!--    width="800px"-->
<!--    :close-on-click-modal="false"-->
<!--    @close="handleCancel"-->
<!--  >-->
<!--    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px">-->
<!--      <el-form-item label="学生姓名" prop="name">-->
<!--        <el-input-->
<!--          v-model="queryParams.name"-->
<!--          placeholder="请输入学生姓名"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="联系电话" prop="phone">-->
<!--        <el-input-->
<!--          v-model="queryParams.phone"-->
<!--          placeholder="请输入联系电话"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item>-->
<!--        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>-->
<!--        <el-button icon="Refresh" @click="resetQuery">重置</el-button>-->
<!--      </el-form-item>-->
<!--    </el-form>-->

<!--    <el-table-->
<!--      v-loading="loading"-->
<!--      :data="studentList"-->
<!--      @selection-change="handleSelectionChange"-->
<!--      highlight-current-row-->
<!--      @current-change="handleCurrentChange"-->
<!--    >-->
<!--      <el-table-column type="selection" width="55" align="center" />-->
<!--      <el-table-column label="学生ID" align="center" prop="id" width="80" />-->
<!--      <el-table-column label="学生姓名" align="center" prop="name" width="120" />-->
<!--      <el-table-column label="联系电话" align="center" prop="phone" width="140" />-->
<!--      <el-table-column label="年级" align="center" prop="grade" width="80" />-->
<!--      <el-table-column label="家长姓名" align="center" prop="parentName" width="120" />-->
<!--      <el-table-column label="家长电话" align="center" prop="parentPhone" width="140" />-->
<!--      <el-table-column label="创建时间" align="center" prop="createTime" width="180">-->
<!--        <template #default="scope">-->
<!--          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--    </el-table>-->

<!--    <pagination-->
<!--      v-show="total > 0"-->
<!--      :total="total"-->
<!--      v-model:page="queryParams.pageNum"-->
<!--      v-model:limit="queryParams.pageSize"-->
<!--      @pagination="getList"-->
<!--    />-->

<!--    <template #footer>-->
<!--      <div class="dialog-footer">-->
<!--        <el-button @click="handleCancel">取 消</el-button>-->
<!--        <el-button type="primary" :disabled="!selectedStudent" @click="handleConfirm">确 定</el-button>-->
<!--      </div>-->
<!--    </template>-->
<!--  </el-dialog>-->
<!--</template>-->

<!--<script setup name="StudentSelectDialog">-->
<!--import { listStudent } from "@/api/management/student";-->

<!--// Props-->
<!--interface Props {-->
<!--  modelValue: boolean-->
<!--}-->

<!--const props = withDefaults(defineProps<Props>(), {-->
<!--  modelValue: false-->
<!--})-->

<!--// Emits-->
<!--const emit = defineEmits<{-->
<!--  'update:modelValue': [value: boolean]-->
<!--  'confirm': [student: any]-->
<!--}>()-->

<!--const { proxy } = getCurrentInstance();-->
<!--const { parseTime } = proxy.useDict();-->

<!--// 响应式数据-->
<!--const visible = ref(false)-->
<!--const loading = ref(false)-->
<!--const studentList = ref([])-->
<!--const total = ref(0)-->
<!--const selectedStudent = ref(null)-->

<!--const data = reactive({-->
<!--  queryParams: {-->
<!--    pageNum: 1,-->
<!--    pageSize: 10,-->
<!--    name: null,-->
<!--    phone: null-->
<!--  }-->
<!--})-->

<!--const { queryParams } = toRefs(data)-->

<!--// 监听弹窗显示状态-->
<!--watch(() => props.modelValue, (val) => {-->
<!--  visible.value = val-->
<!--  if (val) {-->
<!--    resetQuery()-->
<!--    getList()-->
<!--  }-->
<!--})-->

<!--watch(visible, (val) => {-->
<!--  emit('update:modelValue', val)-->
<!--})-->

<!--/** 查询学生列表 */-->
<!--function getList() {-->
<!--  loading.value = true-->
<!--  listStudent(queryParams.value).then(response => {-->
<!--    studentList.value = response.rows-->
<!--    total.value = response.total-->
<!--    loading.value = false-->
<!--  })-->
<!--}-->

<!--/** 搜索按钮操作 */-->
<!--function handleQuery() {-->
<!--  queryParams.value.pageNum = 1-->
<!--  getList()-->
<!--}-->

<!--/** 重置按钮操作 */-->
<!--function resetQuery() {-->
<!--  proxy.resetForm("queryRef")-->
<!--  selectedStudent.value = null-->
<!--  handleQuery()-->
<!--}-->

<!--/** 多选框选中数据 */-->
<!--function handleSelectionChange(selection) {-->
<!--  selectedStudent.value = selection.length > 0 ? selection[0] : null-->
<!--}-->

<!--/** 当前行变化 */-->
<!--function handleCurrentChange(currentRow) {-->
<!--  selectedStudent.value = currentRow-->
<!--}-->

<!--/** 处理取消 */-->
<!--function handleCancel() {-->
<!--  visible.value = false-->
<!--  selectedStudent.value = null-->
<!--}-->

<!--/** 处理确认 */-->
<!--function handleConfirm() {-->
<!--  if (!selectedStudent.value) {-->
<!--    proxy.$modal.msgError("请选择一个学生")-->
<!--    return-->
<!--  }-->
<!--  emit('confirm', selectedStudent.value)-->
<!--}-->
<!--</script>-->

<!--<style scoped>-->
<!--.dialog-footer {-->
<!--  text-align: right;-->
<!--}-->
<!--</style>-->