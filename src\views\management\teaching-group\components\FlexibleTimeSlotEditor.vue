<template>
  <div class="flexible-time-slot-editor">
    <!-- 时间段列表 -->
    <div class="time-slots-list">
      <div
        v-for="(slot, index) in timeSlots"
        :key="slot.id || index"
        class="time-slot-item"
        :class="{ 'editing': slot.editing }"
      >
        <div v-if="!slot.editing" class="slot-display">
          <div class="slot-info">
            <div class="slot-title">{{ slot.title || '未命名时段' }}</div>
            <div class="slot-time">
              {{ weekDayNames[slot.weekday] }} 
              {{ slot.startTime }}-{{ slot.endTime }}
            </div>
            <div class="slot-status">
              <el-tag :type="getStatusTagType(slot.status)">
                {{ getStatusText(slot.status) }}
              </el-tag>
            </div>
          </div>
          <div class="slot-actions">
            <el-button type="primary" link @click="editSlot(index)">
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button type="danger" link @click="removeSlot(index)">
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>

        <div v-else class="slot-editor">
          <el-form :model="slot" label-width="80px" size="small">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="标题">
                  <el-input v-model="slot.title" placeholder="如：第一节课" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="星期">
                  <el-select v-model="slot.weekday" style="width: 100%">
                    <el-option
                      v-for="(name, day) in weekDayNames"
                      :key="day"
                      :label="name"
                      :value="parseInt(day)"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="开始时间">
                  <el-time-picker
                    v-model="slot.startTime"
                    format="HH:mm"
                    value-format="HH:mm"
                    placeholder="选择开始时间"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结束时间">
                  <el-time-picker
                    v-model="slot.endTime"
                    format="HH:mm"
                    value-format="HH:mm"
                    placeholder="选择结束时间"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="状态">
                  <el-select v-model="slot.status" style="width: 100%">
                    <el-option label="可上课" value="available" />
                    <el-option label="不可上课" value="unavailable" />
                    <el-option label="已排课" value="scheduled" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="排序">
                  <el-input-number
                    v-model="slot.sortOrder"
                    :min="0"
                    :max="100"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="备注">
              <el-input
                v-model="slot.remark"
                type="textarea"
                :rows="2"
                placeholder="备注信息"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="success" size="small" @click="saveSlot(index)">
                保存
              </el-button>
              <el-button size="small" @click="cancelEdit(index)">
                取消
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 添加新时间段 -->
    <div class="add-slot-section">
      <el-button type="primary" @click="addNewSlot">
        <el-icon><Plus /></el-icon>
        添加时间段
      </el-button>
    </div>

    <!-- 快速模板 -->
    <div class="template-section">
      <el-divider content-position="left">快速模板</el-divider>
      <div class="template-buttons">
        <el-button size="small" @click="applyTemplate('standard')">
          标准课表 (8:00-17:00)
        </el-button>
        <el-button size="small" @click="applyTemplate('flexible')">
          灵活时段
        </el-button>
        <el-button size="small" @click="applyTemplate('weekend')">
          周末课表
        </el-button>
        <el-button size="small" type="danger" @click="clearAll">
          清空所有
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const timeSlots = ref([])

const weekDayNames = {
  1: '周一', 
  2: '周二',
  3: '周三',
  4: '周四',
  5: '周五',
  6: '周六',
  7: '周日',
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  timeSlots.value = newValue.map(slot => ({
    ...slot,
    editing: false
  }))
}, { immediate: true, deep: true })

watch(timeSlots, (newValue) => {
  const cleanSlots = newValue.map(slot => {
    const { editing, ...cleanSlot } = slot
    return cleanSlot
  })
  emit('update:modelValue', cleanSlots)
  emit('change', cleanSlots)
}, { deep: true })

// 方法
const getStatusTagType = (status) => {
  const typeMap = {
    available: 'success',
    unavailable: 'danger',
    scheduled: 'primary'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    available: '可上课',
    unavailable: '不可上课',
    scheduled: '已排课'
  }
  return textMap[status] || '未知'
}

const addNewSlot = () => {
  const newSlot = {
    id: `temp_${Date.now()}`,
    title: '',
    weekday: 1,
    startTime: '09:00',
    endTime: '10:30',
    status: 'available',
    remark: '',
    sortOrder: timeSlots.value.length + 1,
    editing: true
  }
  timeSlots.value.push(newSlot)
}

const editSlot = (index) => {
  if (props.readonly) return
  timeSlots.value[index].editing = true
}

const saveSlot = (index) => {
  const slot = timeSlots.value[index]
  
  // 验证时间
  if (!slot.startTime || !slot.endTime) {
    ElMessage.error('请选择开始时间和结束时间')
    return
  }
  
  if (slot.startTime >= slot.endTime) {
    ElMessage.error('开始时间必须早于结束时间')
    return
  }
  
  slot.editing = false
  ElMessage.success('保存成功')
}

const cancelEdit = (index) => {
  const slot = timeSlots.value[index]
  if (slot.id && slot.id.startsWith('temp_')) {
    // 新添加的时间段，直接删除
    timeSlots.value.splice(index, 1)
  } else {
    // 已存在的时间段，恢复原始数据
    slot.editing = false
    // 这里应该恢复原始数据，简化处理
  }
}

const removeSlot = async (index) => {
  try {
    await ElMessageBox.confirm('确定要删除这个时间段吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    timeSlots.value.splice(index, 1)
    ElMessage.success('删除成功')
  } catch (error) {
    // 用户取消删除
  }
}

const applyTemplate = (templateType) => {
  let template = []
  
  switch (templateType) {
    case 'standard':
      template = generateStandardTemplate()
      break
    case 'flexible':
      template = generateFlexibleTemplate()
      break
    case 'weekend':
      template = generateWeekendTemplate()
      break
  }
  
  timeSlots.value = template
  ElMessage.success('模板应用成功')
}

const generateStandardTemplate = () => {
  const template = []
  const timeSlots = [
    { start: '08:00', end: '09:30', title: '第一节课' },
    { start: '09:45', end: '11:15', title: '第二节课' },
    { start: '14:00', end: '15:30', title: '第三节课' },
    { start: '15:45', end: '17:15', title: '第四节课' }
  ]
  
  for (let weekday = 1; weekday <= 5; weekday++) {
    timeSlots.forEach((slot, index) => {
      template.push({
        id: `std_${weekday}_${index}`,
        title: slot.title,
        weekday,
        startTime: slot.start,
        endTime: slot.end,
        status: 'available',
        remark: '标准课表',
        sortOrder: index + 1,
        editing: false
      })
    })
  }
  
  return template
}

const generateFlexibleTemplate = () => {
  return [
    {
      id: 'flex_1',
      title: '上午时段',
      weekday: 1,
      startTime: '09:15',
      endTime: '10:45',
      status: 'available',
      remark: '灵活时间安排',
      sortOrder: 1,
      editing: false
    },
    {
      id: 'flex_2',
      title: '下午时段',
      weekday: 1,
      startTime: '14:30',
      endTime: '16:00',
      status: 'available',
      remark: '灵活时间安排',
      sortOrder: 2,
      editing: false
    }
  ]
}

const generateWeekendTemplate = () => {
  const template = []
  const timeSlots = [
    { start: '09:00', end: '10:30', title: '周末第一节' },
    { start: '10:45', end: '12:15', title: '周末第二节' },
    { start: '14:00', end: '15:30', title: '周末第三节' }
  ]
  
  for (let weekday = 0; weekday <= 6; weekday += 6) { // 周日和周六
    timeSlots.forEach((slot, index) => {
      template.push({
        id: `weekend_${weekday}_${index}`,
        title: slot.title,
        weekday,
        startTime: slot.start,
        endTime: slot.end,
        status: 'available',
        remark: '周末课表',
        sortOrder: index + 1,
        editing: false
      })
    })
  }
  
  return template
}

const clearAll = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有时间段吗？', '确认清空', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    timeSlots.value = []
    ElMessage.success('清空成功')
  } catch (error) {
    // 用户取消清空
  }
}
</script>

<style lang="scss" scoped>
.flexible-time-slot-editor {
  .time-slots-list {
    margin-bottom: 20px;

    .time-slot-item {
      border: 1px solid #ebeef5;
      border-radius: 4px;
      margin-bottom: 12px;
      transition: all 0.3s;

      &:hover {
        border-color: #c0c4cc;
      }

      &.editing {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
      }

      .slot-display {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;

        .slot-info {
          flex: 1;

          .slot-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .slot-time {
            font-size: 14px;
            color: #606266;
            margin-bottom: 8px;
          }

          .slot-status {
            .el-tag {
              font-size: 12px;
            }
          }
        }

        .slot-actions {
          display: flex;
          gap: 8px;
        }
      }

      .slot-editor {
        padding: 16px;
        background-color: #f8f9fa;
      }
    }
  }

  .add-slot-section {
    text-align: center;
    margin-bottom: 20px;
  }

  .template-section {
    .template-buttons {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .flexible-time-slot-editor {
    .time-slots-list {
      .time-slot-item {
        .slot-display {
          flex-direction: column;
          align-items: stretch;
          gap: 12px;

          .slot-actions {
            justify-content: center;
          }
        }
      }
    }

    .template-section {
      .template-buttons {
        flex-direction: column;

        .el-button {
          width: 100%;
        }
      }
    }
  }
}
</style>
