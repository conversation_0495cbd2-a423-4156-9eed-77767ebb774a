<template>
  <el-dialog
    v-model="visible"
    title="添加复习课"
    width="600px"
    :before-close="handleClose"
    class="course-schedule-dialog"
    top="5vh"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="90px"
      class="course-schedule-form"
    >
      <!-- 教师和学生同一行 -->
      <el-form-item label-width="20">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="选择教师" prop="teacherId" label-width="80px">
              <el-select
                v-model="form.teacherId"
                placeholder="请选择教师"
                filterable
                remote
                :remote-method="searchTeachers"
                :loading="teachersLoading"
                class="teacher-select"
                clearable
                :disabled="teacherIdFromProps && !canChangeTeacher"
                @change="onTeacherChange"
              >
                <el-option
                  v-for="teacher in teachers"
                  :key="teacher.id"
                  :label="`${teacher.name}`"
                  :value="teacher.id"
                >
                  <div class="teacher-option">
                    <span class="teacher-name">{{ teacher.name }}</span>
                    <!-- <span class="teacher-phone">({{ teacher.phone }})</span> -->
                  </div>
                </el-option>
              </el-select>
              <!-- <div v-if="teacherIdFromProps && !canChangeTeacher" class="teacher-locked-tip">
                <el-text type="info" size="small">教师已锁定，不可修改</el-text>
              </div> -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择学生" prop="studentId" label-width="80px">
              <el-select
                v-model="form.studentId"
                placeholder="请先选择教师"
                filterable
                remote
                :remote-method="searchStudents"
                :loading="studentsLoading"
                class="student-select"
                clearable
                :disabled="!form.teacherId"
              >
                <el-option
                  v-for="student in students"
                  :key="student.id"
                  :label="`${student.name}（${student.phone}）`"
                  :value="student.id"
                >
                  <div class="student-option">
                    <span class="student-name">{{ student.name }}</span>
                    <span class="student-phone">({{ student.phone }})</span>
                  </div>
                </el-option>
              </el-select>
              <div v-if="!form.teacherId" class="student-disabled-tip">
                <el-text type="info" size="small">请先选择教师后再选择学生</el-text>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 课程类型和课程性质同一行 -->
      <el-form-item label-width="20">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="课程类型" prop="type" label-width="80px">
              <el-input
                value="复习课"
                disabled
                placeholder="复习课"
                class="course-type-input"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="课程性质" prop="courseType" label-width="80px">
              <el-select
                v-model="form.courseType"
                placeholder="请选择课程性质"
                class="course-nature-select"
              >
                <el-option label="正式课" value="正式课" />
                <el-option label="试听课" value="试听课" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 学科和规格同一行 -->
      <el-form-item label-width="20">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="选择学科" prop="subject" label-width="80px">
              <el-select
                v-model="form.subject"
                placeholder="请选择学科"
                class="subject-select"
              >
                <el-option
                  v-for="subject in subjectOptions"
                  :key="subject.value"
                  :label="subject.label"
                  :value="subject.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择规格" prop="specification" label-width="80px">
              <el-select
                v-model="form.specification"
                placeholder="请选择规格"
                class="specification-select"
              >
                <el-option
                  v-for="spec in specificationOptions"
                  :key="spec.value"
                  :label="spec.label"
                  :value="spec.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 上课日期 -->
      <el-form-item label="上课日期" prop="date">
        <el-date-picker
          v-model="form.date"
          type="date"
          placeholder="选择日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
          class="date-picker"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 开始时间和结束时间同一行 -->
      <el-form-item label-width="20">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime" label-width="80px">
              <el-time-select
                v-model="form.startTime"
                start="06:00"
                end="24:00"
                step="00:05"
                placeholder="选择开始时间"
                style="width: 100%"
                :max-time="getMaxStartTime()"
                @change="onStartTimeChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime" label-width="80px">
              <el-time-select
                v-model="form.endTime"
                start="06:00"
                end="24:00"
                step="00:05"
                placeholder="选择结束时间"
                style="width: 100%"
                :min-time="getMinEndTime()"
                @change="onEndTimeChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 课程时长 -->
      <el-form-item label="课程时长">
        <el-input
          :value="form.duration > 0 ? form.duration + '分钟' : ''"
          disabled
          placeholder="自动计算"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 使用系统 -->
      <el-form-item v-if="false" label="使用系统" prop="useSystem">
        <el-radio-group v-model="form.useSystem">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 备注 -->
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          确定添加
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { useCurriculumStore } from "@/stores/curriculum";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  teacherId: {
    type: [String, Number],
    default: null,
  },
});

const emit = defineEmits(["update:modelValue", "success"]);

const curriculumStore = useCurriculumStore();

console.log('=== 复习课弹窗组件创建 ===');
console.log('初始 props.modelValue:', props.modelValue);
console.log('初始 props.teacherId:', props.teacherId);

// 响应式数据
const formRef = ref(null);
const submitting = ref(false);
const studentsLoading = ref(false);
const teachersLoading = ref(false);

// 表单数据
const form = ref({
  teacherId: "",
  studentId: "",
  type: "复习课", // 课程类型，固定为复习课
  courseType: "正式课", // 课程性质，默认为正式课
  useSystem: true, // 使用系统，默认为是
  subject: "英语",
  specification: "单词课",
  date: "",
  startTime: "",
  endTime: "",
  duration: 0, // 改为自动计算
  remark: "",
});

// 教师和学生列表
const teachers = ref([]);
const students = ref([]);

// 是否从props传入教师ID（外部指定）
const teacherIdFromProps = computed(() => props.teacherId);
const canChangeTeacher = computed(() => !teacherIdFromProps.value);

// 学科选项
const subjectOptions = [
  { label: "英语", value: "英语" },
  { label: "语文", value: "语文" },
  { label: "数学", value: "数学" },
  { label: "物理", value: "物理" },
  { label: "化学", value: "化学" },
];

// 所有课型选项
const allSpecificationOptions = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" },
  { label: "通用课（非英语）", value: "通用课（非英语）" }
];

// 英语学科的课型选项
const englishSpecificationOptions = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" }
];

// 其他学科的课型选项
const otherSubjectSpecificationOptions = [
  { label: "通用课（非英语）", value: "通用课（非英语）" }
];

// 当前可用的课型选项
const specificationOptions = ref(allSpecificationOptions);

// 时长选项
const durationOptions = [
  { label: "60分钟", value: 60 },
  { label: "90分钟", value: 90 },
  { label: "120分钟", value: 120 },
];

// 自定义验证函数：检查课程时长
const validateDuration = (rule, value, callback) => {
  // updateDuration 会在 startTime 或 endTime 的 @change 事件中被调用，
  // 因此在这里 form.value.duration 应该是最新的。
  if (form.value.duration > 25) {
    callback(new Error("复习课时长不能超过25分钟"));
  } else {
    callback();
  }
};

// 表单验证规则
const rules = {
  teacherId: [{ required: true, message: "请选择教师", trigger: "change" }],
  studentId: [{ required: true, message: "请选择学生", trigger: "change" }],
  type: [{ required: true, message: "课程类型不能为空", trigger: "change" }],
  courseType: [{ required: true, message: "请选择课程性质", trigger: "change" }],
  useSystem: [{ required: true, message: "请选择是否使用系统", trigger: "change" }],
  subject: [{ required: true, message: "请选择课程科目", trigger: "change" }],
  specification: [{ required: true, message: "请选择课程规格", trigger: "change" }],
  date: [{ required: true, message: "请选择上课日期", trigger: "change" }],
  startTime: [
    { required: true, message: "请选择开始时间", trigger: "change" },
    { validator: validateDuration, trigger: "change" }
  ],
  endTime: [
    { required: true, message: "请选择结束时间", trigger: "change" },
    { validator: validateDuration, trigger: "change" }
  ],
};

// 计算属性
const visible = computed({
  get: () => {
    console.log('ReviewScheduleDialog visible get:', props.modelValue);
    return props.modelValue;
  },
  set: (value) => {
    console.log('ReviewScheduleDialog visible set:', value);
    emit("update:modelValue", value);
  },
});

// 禁用过去两周的日期
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 30 * 24 * 60 * 60 * 1000;
};

// 更新时长（根据开始时间和结束时间计算）
const updateDuration = () => {
  if (form.value.startTime && form.value.endTime) {
    const [startHours, startMinutes] = form.value.startTime.split(':').map(Number);
    const [endHours, endMinutes] = form.value.endTime.split(':').map(Number);

    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;

    if (endTotalMinutes > startTotalMinutes) {
      form.value.duration = endTotalMinutes - startTotalMinutes;
    } else {
      form.value.duration = 0;
    }
  } else {
    form.value.duration = 0;
  }
};

// 获取开始时间的最大值（不能晚于结束时间）
const getMaxStartTime = () => {
  if (form.value.endTime) {
    return form.value.endTime;
  }
  return '24:00';
};

// 获取结束时间的最小值（不能早于开始时间）
const getMinEndTime = () => {
  if (form.value.startTime) {
    return form.value.startTime;
  }
  return '06:00';
};

// 将时间字符串转换为分钟数
const timeToMinutes = (timeStr) => {
  if (!timeStr) return 0;
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
};

// 将分钟数转换为时间字符串
const minutesToTime = (minutes) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
};



// 开始时间变化处理
const onStartTimeChange = (newStartTime) => {
  if (!newStartTime) {
    updateDuration();
    return;
  }

  // 如果有结束时间，检查是否需要调整
  if (form.value.endTime) {
    const startMinutes = timeToMinutes(newStartTime);
    const endMinutes = timeToMinutes(form.value.endTime);

    // 如果开始时间晚于或等于结束时间，设置默认时长为25分钟（复习课）
    if (startMinutes >= endMinutes) {
      const defaultDuration = 25; // 复习课固定25分钟
      const newEndMinutes = startMinutes + defaultDuration;
      form.value.endTime = minutesToTime(newEndMinutes);
    }
  } else {
    // 如果没有结束时间，设置默认时长为25分钟（复习课）
    const startMinutes = timeToMinutes(newStartTime);
    const defaultDuration = 25; // 复习课固定25分钟
    const newEndMinutes = startMinutes + defaultDuration;
    form.value.endTime = minutesToTime(newEndMinutes);
  }

  updateDuration();
};

// 结束时间变化处理
const onEndTimeChange = (newEndTime) => {
  if (!newEndTime) {
    updateDuration();
    return;
  }

  // 如果有开始时间，检查是否需要调整
  if (form.value.startTime) {
    const startMinutes = timeToMinutes(form.value.startTime);
    const endMinutes = timeToMinutes(newEndTime);

    // 如果结束时间早于或等于开始时间，设置默认时长为25分钟（复习课）
    if (endMinutes <= startMinutes) {
      const defaultDuration = 25; // 复习课固定25分钟
      const newStartMinutes = Math.max(endMinutes - defaultDuration, timeToMinutes('06:00'));
      form.value.startTime = minutesToTime(newStartMinutes);
    }
  } else {
    // 如果没有开始时间，设置默认时长为25分钟（复习课）
    const endMinutes = timeToMinutes(newEndTime);
    const defaultDuration = 25; // 复习课固定25分钟
    const newStartMinutes = Math.max(endMinutes - defaultDuration, timeToMinutes('06:00'));
    form.value.startTime = minutesToTime(newStartMinutes);
  }

  updateDuration();
};

// 搜索教师
const searchTeachers = async (query) => {
  teachersLoading.value = true;
  try {
    await curriculumStore.fetchTeachers({ keyword: query || '' });
    teachers.value = curriculumStore.teachers;

    // 如果没有传入teacherId且教师列表只有一个教师，自动选中这个教师
    if (!teacherIdFromProps.value && teachers.value && teachers.value.length === 1) {
      const singleTeacher = teachers.value[0];
      form.value.teacherId = singleTeacher.id;
      await onTeacherChange(singleTeacher.id);
    }
  } finally {
    teachersLoading.value = false;
  }
};

// 搜索学生
const searchStudents = async (query) => {
  // 如果没有选择教师，不请求学生列表
  if (!form.value.teacherId) {
    // 只有在用户主动搜索时才显示警告，自动调用时不显示
    if (query) {
      ElMessage.warning('请先选择教师');
    }
    return;
  }

  // 当有教师ID时，无论是否有query都可以请求学生列表
  studentsLoading.value = true;
  try {
    // 传递教师ID和关键词获取学生列表
    await curriculumStore.fetchStudents({
      keyword: query || '',
      teacherId: form.value.teacherId
    });
    students.value = curriculumStore.students;
  } finally {
    studentsLoading.value = false;
  }
};

// 教师变更时的处理
const onTeacherChange = async (teacherId) => {
  if (!teacherId) {
    // 清空学生选择和学生列表
    form.value.studentId = '';
    students.value = [];
    return;
  }

  try {
    // 清空当前学生选择，重新获取该教师的学生列表
    form.value.studentId = '';
    students.value = [];

    // 自动获取该教师的学生列表（不需要关键词）
    studentsLoading.value = true;
    try {
      await curriculumStore.fetchStudents({
        keyword: '',
        teacherId: teacherId
      });
      students.value = curriculumStore.students;
      console.log('获取到教师学生列表:', students.value);
    } catch (error) {
      console.error('获取教师学生列表失败:', error);
    //   ElMessage.error('获取教师学生列表失败');
    } finally {
      studentsLoading.value = false;
    }
  } catch (error) {
    console.error('教师变更处理失败:', error);
    ElMessage.error('教师变更处理失败');
  }
};

// 加载教师列表并验证传入的teacherId是否存在
const loadAndValidateTeacher = async (teacherId) => {
  teachersLoading.value = true;

  try {
    // 先加载所有教师列表
    await curriculumStore.fetchTeachers({ keyword: '' });
    teachers.value = curriculumStore.teachers;

    console.log('验证教师ID:', teacherId, '教师列表:', teachers.value);

    // 验证传入的teacherId是否在列表中存在（处理类型转换）
    const targetTeacher = teachers.value?.find(teacher => {
      const teacherIdStr = String(teacher.id);
      const targetIdStr = String(teacherId);
      console.log('比较教师ID:', teacherIdStr, '目标ID:', targetIdStr);
      return teacherIdStr === targetIdStr;
    });

    console.log('找到的教师:', targetTeacher);

    if (targetTeacher) {
      // 设置教师ID（确保类型与选项value一致）并获取学生列表
      form.value.teacherId = targetTeacher.id; // 使用找到的教师的ID，确保类型一致
      await onTeacherChange(targetTeacher.id);
    } else {
      // 显示错误提示
      console.error('教师验证失败:', {
        teacherId,
        teachersList: teachers.value,
        teacherIds: teachers.value?.map(t => ({ id: t.id, type: typeof t.id }))
      });
      ElMessage.error({
        message: `无法操作ID为${teacherId}的老师`,
        duration: 3000,
        onClose: () => {
          handleClose();
        }
      });
    }
  } catch (error) {
    console.error("加载教师列表失败:", error);
    ElMessage.error({
      message: "加载教师列表失败",
      duration: 3000,
      onClose: () => {
        handleClose();
      }
    });
  } finally {
    teachersLoading.value = false;
  }
};



// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();

    submitting.value = true;

    // 获取选中学生的年级信息
    const selectedStudent = students.value.find(student => student.id === form.value.studentId);
    const studentGrade = selectedStudent?.grade || '';

    // 构造课程数据
    const courseData = {
      studentId: form.value.studentId,
      teacherId: form.value.teacherId,
      subject: form.value.subject,
      specification: form.value.specification,
      type: form.value.type, // 使用表单中的课程类型
      courseType: form.value.courseType, // 使用选择的课程性质
      useSystem: form.value.useSystem, // 新增使用系统字段
      totalLessons: 1, // 单节课程
      dateRange: [form.value.date, form.value.date],
      weeklySchedules: [{
        dayOfWeek: new Date(form.value.date).getDay(),
        startTime: form.value.startTime,
        endTime: form.value.endTime,
        duration: form.value.duration,
      }],
      duration: form.value.duration,
      remark: form.value.remark,
      grade: studentGrade, // 添加学生年级信息
    };

    const success = await curriculumStore.createSchedule(courseData);

    if (success) {
      ElMessage.success("复习课添加成功");
      emit("success");
      handleClose();
    }
  } catch (error) {
    console.error(`添加${form.value.type}失败:`, error);
  } finally {
    submitting.value = false;
  }
};

// 关闭对话框
const handleClose = () => {
  visible.value = false;
  // 重置表单
  form.value = {
    teacherId: teacherIdFromProps.value || "", // 如果从props传入则保留，否则为空
    studentId: "", // 学生选择总是重置为空，等教师选择后再加载
    type: "复习课", // 课程类型，固定为复习课
    courseType: "正式课", // 默认为正式课
    useSystem: true, // 默认为是
    subject: "英语",
    specification: "单词课",
    date: "",
    startTime: "",
    endTime: "",
    duration: 0,
    remark: "",
  };
  formRef.value?.clearValidate();
};

// 监听对话框显示状态
watch(visible, (newValue) => {
  if (newValue) {
    console.log('=== 复习课弹窗打开 ===');
    console.log('teacherIdFromProps:', teacherIdFromProps.value);
    console.log('props.teacherId:', props.teacherId);

    // 对话框打开时，根据props决定加载逻辑
    if (teacherIdFromProps.value) {
      console.log('有传入teacherId，调用loadAndValidateTeacher');
      // 如果传入了teacherId，需要先加载教师列表然后验证
      loadAndValidateTeacher(teacherIdFromProps.value);
    } else {
      console.log('没有传入teacherId，调用searchTeachers');
      // 没有传入teacherId时，正常加载教师列表
      searchTeachers("");
    }

    // 不在这里加载学生列表，等教师选择后再加载
    // 如果传入了studentId，在教师加载完成后会自动处理
  }
});

// 监听学科变化，更新课型选项
watch(
  () => form.value.subject,
  (newSubject) => {
    // 清空当前选择的课型
    form.value.specification = '';

    if (!newSubject) {
      // 没有选择学科，显示所有课型
      specificationOptions.value = allSpecificationOptions;
    } else if (newSubject === '英语') {
      // 英语学科，显示英语相关课型
      specificationOptions.value = englishSpecificationOptions;
    } else {
      // 其他学科，只显示通用课
      specificationOptions.value = otherSubjectSpecificationOptions;
    }
  }
);

// 监听开始时间和结束时间变化（仅用于计算时长，具体的时间调整在change事件中处理）
watch(() => [form.value.startTime, form.value.endTime], updateDuration);

// 监听props中的teacherId变化
watch(() => props.teacherId, (newTeacherId) => {
  if (newTeacherId && visible.value) {
    // 只有在对话框可见时才进行验证，避免在初始化时重复执行
    loadAndValidateTeacher(newTeacherId);
  }
}, { immediate: false }); // 改为false，避免在组件初始化时执行

// 组件创建时立即检查是否需要初始化
if (props.modelValue) {
  console.log('=== 组件创建时对话框已打开，立即初始化 ===');
  console.log('teacherIdFromProps:', props.teacherId);

  if (props.teacherId) {
    console.log('有传入teacherId，调用loadAndValidateTeacher');
    loadAndValidateTeacher(props.teacherId);
  } else {
    console.log('没有传入teacherId，调用searchTeachers');
    searchTeachers("");
  }
}


</script>

<style lang="scss" scoped>
.course-schedule-dialog {
  .course-schedule-form {
    .teacher-option,
    .student-option {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .teacher-name {
        font-weight: 500;
      }

      .teacher-phone,
      .student-phone {
        color: #6b7280;
        font-size: 12px;
      }
    }

    .teacher-locked-tip {
      margin-top: 4px;
      font-size: 12px;
    }

    .student-disabled-tip {
      margin-top: 4px;
      font-size: 12px;
      color: #909399;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  .course-type-input {
    :deep(.el-input__inner) {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: 500;
    }
  }
}
</style>
