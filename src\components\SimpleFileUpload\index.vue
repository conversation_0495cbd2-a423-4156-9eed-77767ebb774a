<template>
  <div class="simple-file-upload">
    <!-- 文件列表显示 -->
    <div v-if="fileList.length > 0" class="file-list">
      <div 
        v-for="(file, index) in fileList" 
        :key="index" 
        class="file-item"
      >
        <div class="file-info">
          <el-icon class="file-icon">
            <Document v-if="isDocument(file.name)" />
            <VideoPlay v-else />
          </el-icon>
          <span class="file-name">{{ file.name }}</span>
        </div>
        <div class="file-actions">
          <el-link 
            v-if="file.url" 
            :href="file.url" 
            target="_blank" 
            type="primary"
            :underline="false"
          >
            查看
          </el-link>
          <el-button 
            type="danger" 
            size="small" 
            text 
            @click="removeFile(index)"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 上传按钮 -->
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="headers"
      :accept="acceptTypes"
      :before-upload="handleBeforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :show-file-list="false"
      :auto-upload="true"
      class="upload-button"
    >
      <el-button 
        type="primary" 
        :disabled="fileList.length >= limit"
        size="small"
      >
        <el-icon><Upload /></el-icon>
        {{ fileList.length >= limit ? '已达上限' : '选择文件' }}
      </el-button>
    </el-upload>

    <!-- 提示信息 -->
    <div v-if="showTip" class="upload-tip">
      <div>{{ tipText }}</div>
      <div v-if="fileSize">单个文件大小不超过 <b>{{ fileSize }}MB</b></div>
      <div v-if="fileTypes && fileTypes.length">
        支持格式：<b>{{ fileTypes.join(', ') }}</b>
      </div>
      <div>最多上传 <b>{{ limit }}</b> 个文件</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Upload, Document, VideoPlay } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getToken } from '@/utils/auth'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  // 上传地址
  uploadUrl: {
    type: String,
    required: true
  },
  // 文件数量限制
  limit: {
    type: Number,
    default: 5
  },
  // 文件大小限制(MB)
  fileSize: {
    type: Number,
    default: 200
  },
  // 支持的文件类型
  fileTypes: {
    type: Array,
    default: () => []
  },
  // 提示文本
  tipText: {
    type: String,
    default: '请选择文件上传'
  },
  // 是否显示提示
  showTip: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const uploadRef = ref()
const fileList = ref([])

// 计算属性
const headers = computed(() => ({
  Authorization: `Bearer ${getToken()}`
}))

const acceptTypes = computed(() => {
  if (!props.fileTypes || props.fileTypes.length === 0) return ''
  return props.fileTypes.map(type => `.${type}`).join(',')
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  if (newVal && Array.isArray(newVal)) {
    fileList.value = newVal.map((url, index) => ({
      name: getFileNameFromUrl(url) || `文件${index + 1}`,
      url: url
    }))
  } else {
    fileList.value = []
  }
}, { immediate: true })

// 方法
const isDocument = (fileName) => {
  if (!fileName) return true
  const ext = fileName.split('.').pop()?.toLowerCase()
  return ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'].includes(ext)
}

const getFileNameFromUrl = (url) => {
  if (!url) return null
  const parts = url.split('/')
  return parts[parts.length - 1]
}

const handleBeforeUpload = (file) => {
  // 检查数量限制
  if (fileList.value.length >= props.limit) {
    ElMessage.error(`最多只能上传 ${props.limit} 个文件`)
    return false
  }

  // 检查文件类型
  if (props.fileTypes && props.fileTypes.length > 0) {
    const fileExtension = file.name.split('.').pop().toLowerCase()
    if (!props.fileTypes.includes(fileExtension)) {
      ElMessage.error(`不支持的文件格式，请上传 ${props.fileTypes.join(', ')} 格式的文件`)
      return false
    }
  }

  // 检查文件大小
  if (props.fileSize && file.size > props.fileSize * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过 ${props.fileSize}MB`)
    return false
  }

  return true
}

const handleSuccess = (response, file) => {
  console.log('Upload success response:', response)
  
  // 处理不同的响应格式
  let fileUrl = null
  let fileName = file.name
  
  if (response.success && response.data && response.data.fileUrl) {
    fileUrl = response.data.fileUrl
    fileName = response.data.fileName || file.name
  } else if (typeof response === 'string') {
    // 如果响应是字符串格式
    try {
      const parsed = JSON.parse(response)
      if (parsed.success && parsed.data && parsed.data.fileUrl) {
        fileUrl = parsed.data.fileUrl
        fileName = parsed.data.fileName || file.name
      }
    } catch (e) {
      console.error('Failed to parse response:', e)
    }
  }
  
  if (fileUrl) {
    // 添加到文件列表
    const newFile = {
      name: fileName,
      url: fileUrl
    }
    fileList.value.push(newFile)
    
    // 更新 v-model
    const urls = fileList.value.map(item => item.url)
    emit('update:modelValue', urls)
    emit('change', urls)
    
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error('文件上传失败：响应格式错误')
  }
}

const handleError = (error, file) => {
  console.error('文件上传失败:', error)
  ElMessage.error('文件上传失败，请重试')
}

const removeFile = (index) => {
  fileList.value.splice(index, 1)
  
  // 更新 v-model
  const urls = fileList.value.map(item => item.url)
  emit('update:modelValue', urls)
  emit('change', urls)
}

// 清空文件列表
const clearFiles = () => {
  fileList.value = []
  emit('update:modelValue', [])
  emit('change', [])
}

// 暴露方法
defineExpose({
  clearFiles
})
</script>

<style scoped>
.simple-file-upload {
  width: 100%;
}

.file-list {
  margin-bottom: 12px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #409eff;
}

.file-name {
  font-size: 14px;
  color: #303133;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.upload-button {
  margin-bottom: 8px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.upload-tip b {
  color: #f56c6c;
  font-weight: 600;
}
</style>
