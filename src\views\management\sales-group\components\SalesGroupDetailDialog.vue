<template>
  <el-dialog
    v-model="dialogVisible"
    title="销售组详情"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="detail-container">
      <div v-if="groupDetail" class="group-detail">
        <!-- 基本信息 -->
        <el-card shadow="never" class="info-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="销售组名称">
              {{ groupDetail.name }}
            </el-descriptions-item>
            <el-descriptions-item label="组长">
              {{ groupDetail.leaderName || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="成员数量">
              {{ groupDetail.memberCount || 0 }}人
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="groupDetail.status === 'active' ? 'success' : 'danger'" size="small">
                {{ groupDetail.status === 'active' ? '正常' : '停用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">
              {{ groupDetail.createTime }}
            </el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">
              {{ groupDetail.description || '暂无描述' }}
            </el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">
              {{ groupDetail.remark || '暂无备注' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 统计信息 -->
        <el-card v-if="false" shadow="never" class="stats-card">
          <template #header>
            <div class="card-header">
              <span>统计信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ groupDetail.totalStudents || 0 }}</div>
                <div class="stat-label">负责学生总数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ groupDetail.activeStudents || 0 }}</div>
                <div class="stat-label">活跃学生数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ groupDetail.pendingApplications || 0 }}</div>
                <div class="stat-label">待处理申请</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ groupDetail.completedCourses || 0 }}</div>
                <div class="stat-label">已完成课程</div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 成员列表 -->
        <el-card shadow="never" class="members-card">
          <template #header>
            <div class="card-header">
              <span>成员列表</span>
              <el-button type="primary" size="small" @click="handleRefreshMembers">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <el-table
            v-loading="membersLoading"
            :data="memberList"
            stripe
            border
            max-height="300"
          >
            <el-table-column prop="salesName" label="姓名" width="120" />
            <el-table-column prop="salesPhone" label="手机号" width="130" />
            <el-table-column label="角色" width="100">
              <template #default="{ row }">
                <el-tag :type="row.role === 'leader' ? 'danger' : 'primary'" size="small">
                  {{ row.role === 'leader' ? '组长' : '成员' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="studentCount" label="负责学生数" width="120" align="center" />
            <el-table-column prop="joinTime" label="加入时间" width="180" />
            <el-table-column label="状态" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                  {{ row.status === 'active' ? '正常' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { 
  getSalesGroupDetailApi,
  getSalesGroupMembersApi
} from '@/api/management/salesGroup'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  groupId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const loading = ref(false)
const membersLoading = ref(false)
const groupDetail = ref(null)
const memberList = ref([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听props变化
watch(() => props.groupId, (newGroupId) => {
  if (newGroupId && props.modelValue) {
    loadGroupDetail()
    loadMembers()
  }
}, { immediate: true })

watch(() => props.modelValue, (newValue) => {
  if (newValue && props.groupId) {
    loadGroupDetail()
    loadMembers()
  }
})

// 加载销售组详情
const loadGroupDetail = async () => {
  if (!props.groupId) return
  
  loading.value = true
  try {
    const response = await getSalesGroupDetailApi(props.groupId)
    groupDetail.value = response.data
  } catch (error) {
    ElMessage.error('获取销售组详情失败')
  } finally {
    loading.value = false
  }
}

// 加载成员列表
const loadMembers = async () => {
  if (!props.groupId) return
  
  membersLoading.value = true
  try {
    const response = await getSalesGroupMembersApi(props.groupId)
    memberList.value = response.data || []
  } catch (error) {
    ElMessage.error('获取成员列表失败')
  } finally {
    membersLoading.value = false
  }
}

// 刷新成员列表
const handleRefreshMembers = () => {
  loadMembers()
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  groupDetail.value = null
  memberList.value = []
}
</script>

<style scoped>
.detail-container {
  padding: 10px 0;
}

.info-card,
.stats-card,
.members-card {
  margin-bottom: 20px;
}

.members-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  text-align: center;
  padding: 20px 0;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}
</style>
