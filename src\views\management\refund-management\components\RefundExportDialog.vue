<template>
  <el-dialog v-model="visible" title="导出退款记录" width="520px" :close-on-click-modal="false">
    <el-form label-width="100px">
      <el-form-item label="导出范围">
        <el-radio-group v-model="exportScope">
          <el-radio label="current">当前筛选结果</el-radio>
          <el-radio label="all">最大范围（上限10000）</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" :loading="downloading" @click="onExport">导 出</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { exportRefundRecords } from '@/api/management/refund-records'

const props = defineProps<{ modelValue: boolean; params?: Record<string, any> }>()
const emit = defineEmits<{ (e: 'update:modelValue', v: boolean): void }>()

const visible = ref<boolean>(props.modelValue)
watch(
  () => props.modelValue,
  (v) => (visible.value = v)
)
watch(visible, (v) => emit('update:modelValue', v))

const exportScope = ref<'current' | 'all'>('current')
const downloading = ref(false)

const onExport = async () => {
  try {
    downloading.value = true
    const payload: any = { ...(props.params || {}) }
    if (exportScope.value === 'all') {
      payload.maxExportCount = 10000
      payload.pageNum = 1
      payload.pageSize = 10000
    }
    const blob: any = await exportRefundRecords(payload)
    if (blob && blob.size) {
      const url = window.URL.createObjectURL(new Blob([blob]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `退款记录_${Date.now()}.xlsx`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      ElMessage.success('导出发起成功')
      visible.value = false
    } else {
      ElMessage.error('导出失败')
    }
  } catch (e) {
    console.error(e)
    ElMessage.error('导出失败')
  } finally {
    downloading.value = false
  }
}
</script>

