<template>
  <el-drawer
    v-model="visible"
    title="复习篮子"
    size="400px"
    :with-header="false"
    direction="rtl"
    class="review-basket-drawer"
  >
    <div class="review-basket-container" v-loading="disabled">
      <!-- 抽屉头部 -->
      <div class="review-basket-header">
        <div class="header-title">
          <el-icon class="header-icon"><MessageBox /></el-icon>
          复习篮子
        </div>
        <div class="header-subtitle">温故知新，让知识更牢固</div>
      </div>

      <!-- 复习内容区域 -->
      <div class="review-basket-content">
        <!-- 日期列表 -->
        <div
          v-for="(date, index) in reviewCourses"
          :key="index"
          class="review-date-section"
        >
          <div class="date-header">
            <div class="date-info">
              <span class="date-text">{{ date.scheduledTime }}</span>
              <!-- <el-tag size="small" :type="date.reviewType" effect="light" class="review-tag">
                {{ date.reviewType }}
              </el-tag> -->
            </div>
          </div>
          <!-- 课程列表 -->
          <div class="course-list">
            <div
              v-for="(detail, detailIndex) in date.detailContents"
              :key="detailIndex"
              class="course-item"
            >
              <div class="course-info">
                <div class="course-name">{{ detail.name }}</div>
                <div class="course-details">
                  <span class="word-count">{{ detail.wordCount }}个单词</span>
                  <span class="separator">·</span>
                  <span :class="['review-status-class',{'review-ing':detail.status === '进行中','review-wait':detail.status === '待开始'}]">{{ detail.status }}</span>
                  <span class="separator">·</span>
                  <span class="difficulty">{{
                    detail.reviewType
                  }}</span>
                </div>
              </div>
              <el-button
                type="primary"
                size="small"
                class="review-button"
                @click="startReview(detail.id)"
              >
               {{ detail.status === '待开始'?'开始':'继续' }}复习
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { MessageBox } from "@element-plus/icons-vue";
import { ReviewCourse, fetchReviewCourseInfoApi,startReviewCourseInfoApi } from "../../../api/course";
import { ElMessage } from "element-plus";

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  studentId: {
    type: String,
    required: true,
  },
  courseId: {
    type: String,
    required: true,
  },
});

const reviewCourses = ref<ReviewCourse[]>([]);
const disabled = ref<boolean>(false);

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "close"): void;
  (e: "startReviewCourse", nodeInfo: any): void;
}>();

onMounted(() => {
  // 初始化数据
  initData();
});

const initData = () => {
  fetchReviewCourseInfoApi(props.studentId).then((res) => {
    if (res.code == 200) {
      reviewCourses.value = res.data.reviews;
    }
  });
};

// 开始复习
const startReview = (reviewId: any) => {
  disabled.value = true;
  startReviewCourseInfoApi(props.courseId, reviewId).then((res) => {
    if (res.code == 200) {
      ElMessage.success("开始学习成功");
      // sessionStorage.setItem(COURSE_sessionStorage_INFO_KEY + courseId.value,JSON.stringify(res.data));
      emit("startReviewCourse", res.data);
      handleClose();
    }
  }).finally(() => {
    disabled.value = false;
  });
};

// 关闭抽屉
const handleClose = () => {
  emit("update:visible", false);
  emit("close");
};

// 监听visible属性变化
const visible = computed({
  get: () => props.visible,
  set: (value) => {
    emit("update:visible", value);
  },
});
</script>

<style lang="scss" scoped>
.review-basket-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff8e1;
}

.review-basket-header {
  padding: 24px;
  background: linear-gradient(135deg, #ffe0b2, #ffecb3);
  border-bottom: 2px solid #ffd54f;

  .header-title {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    color: #ff9800;
    margin-bottom: 8px;

    .header-icon {
      margin-right: 8px;
      font-size: 24px;
    }
  }

  .header-subtitle {
    color: #f57c00;
    font-size: 14px;
    opacity: 0.8;
  }
}

.review-basket-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.review-date-section {
  margin-bottom: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .date-header {
    padding: 16px;
    background: linear-gradient(135deg, #fff8e1, #ffecb3);
    border-bottom: 1px solid #ffd54f;

    .date-info {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .date-text {
        font-size: 16px;
        font-weight: bold;
        color: #ff9800;
      }

      .review-tag {
        margin-left: 8px;
      }
    }
  }
}

.course-list {
  padding: 12px;
}

.course-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px dashed #ffe0b2;
  transition: all 0.3s;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #fff3e0;
  }

  .course-info {
    flex: 1;

    .course-name {
      font-size: 16px;
      color: #333;
      margin-bottom: 4px;
    }

    .course-details {
      font-size: 12px;
      color: #666;

      .separator {
        margin: 0 8px;
        color: #ffb74d;
      }

      .review-status-class {
        color: #fff;
        border-radius: 5px;
      }

      .review-ing{
        color:rgb(0, 81, 255);
      }

      .review-wait{
        color:red;
      }

      .word-count {
        color: #ff9800;
      }

      .difficulty {
        color: #9c27b0;
      }
    }
  }

  .review-button {
    background: linear-gradient(135deg, #ff9800, #ffc107);
    border: none;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
    }
  }
}

.review-basket-footer {
  padding: 20px;
  background: white;
  border-top: 2px solid #ffd54f;
  display: flex;
  justify-content: space-around;

  .stats-item {
    text-align: center;

    .stats-value {
      font-size: 24px;
      font-weight: bold;
      color: #ff9800;
      margin-bottom: 4px;
    }

    .stats-label {
      font-size: 12px;
      color: #666;
    }
  }
}

// 自定义滚动条样式
.review-basket-content::-webkit-scrollbar {
  width: 6px;
}

.review-basket-content::-webkit-scrollbar-track {
  background: #fff3e0;
  border-radius: 3px;
}

.review-basket-content::-webkit-scrollbar-thumb {
  background: #ffb74d;
  border-radius: 3px;

  &:hover {
    background: #ff9800;
  }
}
</style>
