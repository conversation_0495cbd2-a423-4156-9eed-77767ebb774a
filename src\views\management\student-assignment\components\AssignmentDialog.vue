<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`学生分配 - ${students.length}名学生`"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="assignment-container">
      <!-- 学生列表 -->
      <div class="student-section">
        <h4>待分配学生</h4>
        <el-table
          :data="students"
          max-height="200"
          stripe
          border
        >
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="phone" label="手机号" width="130" />
          <el-table-column prop="grade" label="年级" width="80" />
          <el-table-column prop="school" label="学校" min-width="150" show-overflow-tooltip />
        </el-table>
      </div>

      <!-- 分配选项 -->
      <div class="assignment-section">
        <h4>分配设置</h4>
        <el-form :model="assignmentForm" :rules="assignmentRules" ref="formRef" label-width="120px">
          <el-form-item label="分配方式" prop="assignmentType">
            <el-radio-group v-model="assignmentForm.assignmentType" @change="handleAssignmentTypeChange">
              <el-radio value="manual">手动指定销售</el-radio>
              <el-radio value="group">指定销售组</el-radio>
              <el-radio value="auto">智能自动分配</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 手动指定销售 -->
          <el-form-item 
            v-if="assignmentForm.assignmentType === 'manual'" 
            label="指定销售" 
            prop="salesId"
          >
            <el-select
              v-model="assignmentForm.salesId"
              placeholder="请选择销售人员"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="sales in salesOptions"
                :key="sales.id"
                :label="`${sales.name} (${sales.studentCount || 0}名学生)`"
                :value="sales.id"
              />
            </el-select>
          </el-form-item>

          <!-- 指定销售组 -->
          <el-form-item 
            v-if="assignmentForm.assignmentType === 'group'" 
            label="指定销售组" 
            prop="salesGroupId"
          >
            <el-select
              v-model="assignmentForm.salesGroupId"
              placeholder="请选择销售组"
              style="width: 100%"
            >
              <el-option
                v-for="group in salesGroupOptions"
                :key="group.id"
                :label="`${group.name} (${group.memberCount || 0}名成员)`"
                :value="group.id"
              />
            </el-select>
          </el-form-item>

          <!-- 智能分配选项 -->
          <div v-if="assignmentForm.assignmentType === 'auto'">
            <el-form-item label="分配策略">
              <el-checkbox-group v-model="assignmentForm.autoOptions">
                <el-checkbox value="balance">负载均衡</el-checkbox>
                <el-checkbox value="region">地区优先</el-checkbox>
                <el-checkbox value="experience">经验匹配</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>

          <el-form-item label="分配备注">
            <el-input
              v-model="assignmentForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入分配备注（可选）"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          确认分配
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  assignStudentsApi,
  batchAssignStudentsApi,
  autoAssignStudentsApi,
  getSalesOptionsApi,
  getSalesGroupOptionsApi
} from '@/api/management/studentAssignment'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  students: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const salesOptions = ref([])
const salesGroupOptions = ref([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const assignmentForm = reactive({
  assignmentType: 'manual',
  salesId: '',
  salesGroupId: '',
  autoOptions: ['balance'],
  remark: ''
})

// 表单验证规则
const assignmentRules = {
  assignmentType: [
    { required: true, message: '请选择分配方式', trigger: 'change' }
  ],
  salesId: [
    { required: true, message: '请选择销售人员', trigger: 'change' }
  ],
  salesGroupId: [
    { required: true, message: '请选择销售组', trigger: 'change' }
  ]
}

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    loadOptions()
    resetForm()
  }
})

// 页面初始化
onMounted(() => {
  loadOptions()
})

// 加载选项数据
const loadOptions = async () => {
  try {
    const [salesResponse, groupResponse] = await Promise.all([
      getSalesOptionsApi(),
      getSalesGroupOptionsApi()
    ])
    salesOptions.value = salesResponse.data || []
    salesGroupOptions.value = groupResponse.data || []
  } catch (error) {
    console.error('获取选项数据失败:', error)
  }
}

// 分配方式变化
const handleAssignmentTypeChange = () => {
  // 清空相关字段
  assignmentForm.salesId = ''
  assignmentForm.salesGroupId = ''
  formRef.value?.clearValidate()
}

// 重置表单
const resetForm = () => {
  Object.assign(assignmentForm, {
    assignmentType: 'manual',
    salesId: '',
    salesGroupId: '',
    autoOptions: ['balance'],
    remark: ''
  })
  formRef.value?.clearValidate()
}

// 提交分配
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    const studentIds = props.students.map(student => student.id)
    
    let apiCall
    let requestData = {
      studentIds,
      remark: assignmentForm.remark
    }

    switch (assignmentForm.assignmentType) {
      case 'manual':
        requestData.salesId = assignmentForm.salesId
        apiCall = assignStudentsApi
        break
      case 'group':
        requestData.salesGroupId = assignmentForm.salesGroupId
        apiCall = batchAssignStudentsApi
        break
      case 'auto':
        requestData.options = assignmentForm.autoOptions
        apiCall = autoAssignStudentsApi
        break
    }

    await apiCall(requestData)
    ElMessage.success('分配成功')
    emit('success')
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('分配失败')
    }
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}
</script>

<style scoped>
.assignment-container {
  padding: 10px 0;
}

.student-section,
.assignment-section {
  margin-bottom: 20px;
}

.student-section h4,
.assignment-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.dialog-footer {
  text-align: right;
}
</style>
