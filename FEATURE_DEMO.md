# 产品配置列表订单核销功能演示

## 功能演示流程

### 1. 产品列表页面

在产品配置列表页面，每个上架状态的产品都会显示"订单核销"按钮：

```
产品列表操作列：
[编辑] [产品下单] [订单核销] [下架]
```

- 只有上架状态的产品才显示"订单核销"按钮
- 需要相应权限才能看到按钮

### 2. 点击订单核销按钮

点击某个产品的"订单核销"按钮后，会打开学生选择对话框。

### 3. 学生选择对话框

对话框分为两个部分：

#### 3.1 产品信息展示区域
```
产品信息
├── 产品名称：英语单词课程包
├── 学科信息：英语 单词课
├── 适用年级：[一年级] [二年级] [三年级]
└── 客户类型：[通用]
```

#### 3.2 学生选择区域
```
选择学生
搜索栏：[学生姓名搜索] [手机号搜索] [搜索] [重置]

学生列表：
┌─────────┬─────────┬─────────┬─────────┬─────────┬─────────┐
│ 学生姓名 │ 手机号   │ 年级     │ 家长信息 │ 适用状态 │ 操作     │
├─────────┼─────────┼─────────┼─────────┼─────────┼─────────┤
│ 张小明   │ 1388888 │ [一年级] │ 张父     │ [适用]   │ [选择]   │
│         │         │         │ 1377777 │         │         │
├─────────┼─────────┼─────────┼─────────┼─────────┼─────────┤
│ 李小红   │ 1366666 │ [四年级] │ 李母     │ [年级不  │ [选择]   │
│         │         │         │ 1355555 │  适用]   │ (禁用)   │
├─────────┼─────────┼─────────┼─────────┼─────────┼─────────┤
│ 王小刚   │ 1344444 │ [二年级] │ 未设置   │ [家长信  │ [选择]   │
│         │         │         │ 未设置   │  息不完  │ (禁用)   │
│         │         │         │         │  整]     │         │
└─────────┴─────────┴─────────┴─────────┴─────────┴─────────┘

分页：[总计 25 条] [10条/页] [上一页] [1] [2] [3] [下一页]
```

### 4. 学生适用性验证

系统会自动验证每个学生是否适用于选中的产品：

#### 4.1 适用的学生
- 年级标签显示为绿色
- 适用状态显示"适用"
- 选择按钮可用

#### 4.2 年级不适用的学生
- 年级标签显示为红色
- 适用状态显示"年级不适用"
- 选择按钮被禁用

#### 4.3 家长信息不完整的学生
- 适用状态显示"家长信息不完整"
- 选择按钮被禁用

#### 4.4 客户类型不匹配的学生
- 适用状态显示相应的客户类型限制信息
- 选择按钮被禁用

### 5. 选择学生

点击适用学生的"选择"按钮后：
1. 学生选择对话框关闭
2. 订单核销对话框打开
3. 学生信息自动填入
4. 产品自动选中

### 6. 订单核销对话框

```
订单核销
├── 学生信息：张小明 1388888 [一年级]
├── 核销平台：[抖店 ▼]
├── 订单号：[输入框]
├── 产品选择：
│   └── 已选择：英语单词课程包 (自动选中)
├── 订单截图：[上传区域]
└── 备注：[文本框]

[取消] [确认核销]
```

### 7. 完成核销

填写完整信息后点击"确认核销"，系统会：
1. 验证信息完整性
2. 上传订单截图
3. 提交核销申请
4. 显示成功提示

## 关键特性

### 1. 智能验证
- 自动检查学生年级是否匹配产品适用年级
- 自动检查家长信息是否完整
- 自动检查客户类型是否匹配

### 2. 友好提示
- 不适用的学生会显示具体的不适用原因
- 不适用的学生选择按钮被禁用
- 使用不同颜色的标签区分适用状态

### 3. 高效操作
- 从产品直接进入核销，产品自动选中
- 支持学生搜索，快速定位目标学生
- 分页显示，处理大量学生数据

### 4. 一致性
- 与销售学生管理的订单核销逻辑完全一致
- 使用相同的验证规则和用户界面
- 保持统一的用户体验

## 使用场景

### 场景1：新产品推广
1. 产品经理发布新产品
2. 销售人员从产品列表进入核销
3. 快速找到适合的学生进行核销

### 场景2：特定产品核销
1. 客户指定要购买某个产品
2. 销售人员直接从该产品进入核销
3. 系统自动验证客户是否适用

### 场景3：批量产品核销
1. 销售人员需要为多个产品进行核销
2. 可以逐个从产品列表进入核销
3. 每次都会自动选中对应产品

## 优势

1. **提高效率**：从产品直接进入核销，减少操作步骤
2. **减少错误**：自动验证学生适用性，避免无效核销
3. **用户友好**：清晰的状态提示和禁用控制
4. **功能完整**：支持搜索、分页、验证等完整功能
