import request from '@/utils/request'

// 查询核销记录列表
export function listWriterOff(query) {
  return request({
    url: '/order/writeroff/list',
    method: 'get',
    params: query
  })
}

// 查询核销记录详细
export function getWriterOff(id) {
  return request({
    url: '/order/writeroff/' + id,
    method: 'get'
  })
}

// 新增核销记录
export function addWriterOff(data) {
  return request({
    url: '/order/writeroff',
    method: 'post',
    data: data
  })
}

// 修改核销记录
export function updateWriterOff(data) {
  return request({
    url: '/order/writeroff',
    method: 'put',
    data: data
  })
}

// 删除核销记录
export function delWriterOff(id) {
  return request({
    url: '/order/writeroff/' + id,
    method: 'delete'
  })
}

// ==================== 核销申请相关接口 ====================

// 提交核销申请
export function submitWriteOffApplication(data) {
  return request({
    url: '/order-writeoff/write-off',
    method: 'post',
    data: data
  })
}

// 获取核销详情
export function getWriteOffDetail(id) {
  return request({
    url: `/order-writeoff/detail/${id}`,
    method: 'get'
  })
}

// 核销审核
export function approveWriteOff(id, data) {
  return request({
    url: `/order-writeoff/approve/${id}`,
    method: 'put',
    data: data
  })
}

// 核销订单
export function writeOffOrderApi(data) {
  return request({
    url: '/order-writeoff/write-off',
    method: 'post',
    data: data
  })
}

// 获取核销详情
export function getWriteOffDetailApi(orderNo) {
  return request({
    url: '/order-writeoff/detail/' + orderNo,
    method: 'get'
  })
}