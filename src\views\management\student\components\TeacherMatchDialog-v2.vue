<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`为学生 ${studentInfo?.name || ''} 匹配老师`"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="match-dialog-content">
      <!-- 使用重构后的匹配老师组件 -->
      <TeacherMatchV2
        :student-info="studentInfo"
        :is-dialog-mode="true"
        @success="handleMatchSuccess"
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import TeacherMatchV2 from '@/views/management/teacher-match/index.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  studentInfo: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const handleMatchSuccess = (result) => {
  emit('success', result)
  handleClose()
}
</script>

<style lang="scss" scoped>
.match-dialog-content {
  height: 600px;
  overflow: hidden;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
