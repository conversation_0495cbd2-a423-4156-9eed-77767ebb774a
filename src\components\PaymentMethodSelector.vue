<template>
  <div class="payment-method-selector">
    <!-- 支付方式选择 -->
<!--    <el-card class="payment-methods" shadow="never">-->
<!--      <template #header>-->
<!--        <span class="card-title">选择支付方式</span>-->
<!--      </template>-->
<!--      <el-radio-group v-model="selectedPayMethod" class="payment-method-group" @change="handleMethodChange">-->
<!--        <el-radio value="微信支付" class="payment-method-item">-->
<!--          <div class="method-content">-->
<!--            <el-icon class="method-icon wechat"><ChatDotRound /></el-icon>-->
<!--            <span class="method-name">微信支付</span>-->
<!--          </div>-->
<!--        </el-radio>-->
<!--        <el-radio value="支付宝" class="payment-method-item">-->
<!--          <div class="method-content">-->
<!--            <el-icon class="method-icon alipay"><Wallet /></el-icon>-->
<!--            <span class="method-name">支付宝</span>-->
<!--          </div>-->
<!--        </el-radio>-->
<!--        <el-radio value="银行卡" class="payment-method-item">-->
<!--          <div class="method-content">-->
<!--            <el-icon class="method-icon bank"><CreditCard /></el-icon>-->
<!--            <span class="method-name">银行卡</span>-->
<!--          </div>-->
<!--        </el-radio>-->
<!--      </el-radio-group>-->
<!--    </el-card>-->

    <!-- 支付操作按钮 -->
<!--    <div class="payment-actions">-->
<!--      <el-button type="primary" @click="generatePayment" :loading="loading" :disabled="!selectedPayMethod">-->
<!--        {{ qrCodeUrl || paymentLink ? '重新生成' : '生成支付' }}-->
<!--      </el-button>-->
<!--      <el-button v-if="qrCodeUrl || paymentLink" type="success" @click="confirmPayment">-->
<!--        确认支付完成-->
<!--      </el-button>-->
<!--    </div>-->

    <!-- 支付二维码 -->
    <el-card v-if="qrCodeUrl" class="qr-code-section" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">扫码支付</span>
          <el-button type="text" size="small" @click="refreshQRCode">
            <el-icon><Refresh /></el-icon>
            刷新二维码
          </el-button>
        </div>
      </template>
      <div class="qr-code-container">
        <div class="qr-code">
          <img :src="qrCodeUrl" alt="支付二维码" />
        </div>
        <div class="qr-code-tips">
          <p>请使用{{ selectedPayMethod }}扫描二维码完成支付</p>
          <p class="amount-tip">支付金额：<span class="amount">¥{{ formatAmount(amount) }}</span></p>
        </div>
      </div>
    </el-card>

    <!-- 支付链接 -->
    <el-card v-if="paymentLink" class="payment-link-section" shadow="never">
      <template #header>
        <span class="card-title">支付链接</span>
      </template>
      <div class="link-container">
        <el-input
          v-model="paymentLink"
          readonly
          class="payment-link-input"
          placeholder="支付链接"
        >
          <template #append>
            <el-button @click="copyLink">
              <el-icon><DocumentCopy /></el-icon>
              复制
            </el-button>
          </template>
        </el-input>
        <div class="link-actions">
          <el-button @click="copyLink">
            <el-icon><DocumentCopy /></el-icon>
            复制链接
          </el-button>
          <el-button @click="sendWechat" v-loading="sendWechatLoading">
            <el-icon><Link /></el-icon>
            发送微信消息
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ChatDotRound,
  Wallet,
  CreditCard,
  Refresh,
  DocumentCopy,
  Link
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  // 支付金额（分）
  amount: {
    type: Number,
    default: 0
  },
  // 交易信息
  transaction: {
    type: Object,
    default: null
  },
  // 是否显示确认支付按钮
  showConfirmButton: {
    type: Boolean,
    default: true
  },
  // 默认支付方式
  defaultPayMethod: {
    type: String,
    default: '微信支付'
  }
})

// Emits
const emit = defineEmits(['generate-payment', 'confirm-payment', 'copy-link', 'send-wechat', 'method-change'])

// 响应式数据
const selectedPayMethod = ref(props.defaultPayMethod)
const qrCodeUrl = ref('')
const paymentLink = ref('')
const loading = ref(false)
const sendWechatLoading = ref(false)

// 计算属性
const formatAmount = computed(() => {
  return (amount) => {
    if (typeof amount !== 'number') return '0.00'
    return (amount / 100).toFixed(2)
  }
})

// 监听交易变化，重置状态
watch(() => props.transaction, () => {
  if (props.transaction) {
    selectedPayMethod.value = props.defaultPayMethod
    qrCodeUrl.value = ''
    paymentLink.value = ''
  }
})

// 方法
const handleMethodChange = (value) => {
  emit('method-change', value)
}

const generatePayment = () => {
  if (!selectedPayMethod.value) {
    ElMessage.warning('请选择支付方式')
    return
  }
  
  loading.value = true
  try {
    emit('generate-payment', {
      payMethod: selectedPayMethod.value,
      transaction: props.transaction,
      amount: props.amount
    })

  } finally {
    loading.value = false
  }
}

const refreshQRCode = () => {
  generatePayment()
}

const generatePaymentResult = async (data) => {
  qrCodeUrl.value = data.qrCodeUrl
  paymentLink.value = data.paymentLink
  ElMessage.success('支付信息生成成功')
}

const confirmPayment = async () => {
  if (!props.showConfirmButton) return
  
  try {
    await ElMessageBox.confirm(
      '请确认用户已完成支付，确认后将更新订单状态。',
      '确认支付',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    emit('confirm-payment', {
      transaction: props.transaction,
      payMethod: selectedPayMethod.value,
      amount: props.amount
    })
  } catch (error) {
    // 用户取消确认
  }
}

const copyLink = async () => {
  if (!paymentLink.value) {
    ElMessage.warning('暂无支付链接')
    return
  }
  
  try {
    await navigator.clipboard.writeText(paymentLink.value)
    ElMessage.success('支付链接已复制到剪贴板')
    emit('copy-link', paymentLink.value)
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const sendWechat = () => {
  sendWechatLoading.value = true
  try {
    if (!paymentLink.value) {
      ElMessage.warning('暂无支付链接')
      return
    }

    emit('send-wechat', {
      paymentLink: paymentLink.value,
      payMethod: selectedPayMethod.value,
      amount: props.amount
    })
  } finally {
    sendWechatLoading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  generatePayment,
  generatePaymentResult,
  confirmPayment,
  resetPayment: () => {
    qrCodeUrl.value = ''
    paymentLink.value = ''
    selectedPayMethod.value = props.defaultPayMethod
  },
  setPaymentInfo: (info) => {
    if (info.qrCodeUrl) qrCodeUrl.value = info.qrCodeUrl
    if (info.paymentLink) paymentLink.value = info.paymentLink
  }
})
</script>

<style scoped>
.payment-method-selector {
  .payment-methods,
  .qr-code-section,
  .payment-link-section {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.payment-method-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.payment-method-item {
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  transition: all 0.3s;

  &:hover {
    border-color: #409EFF;
    background-color: #f0f9ff;
  }

  &.is-checked {
    border-color: #409EFF;
    background-color: #f0f9ff;
  }
}

.method-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.method-icon {
  font-size: 24px;

  &.wechat {
    color: #07c160;
  }

  &.alipay {
    color: #1677ff;
  }

  &.bank {
    color: #722ed1;
  }
}

.method-name {
  font-size: 16px;
  font-weight: 500;
}

.payment-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
}

.qr-code-container {
  display: flex;
  gap: 24px;
  align-items: center;
}

.qr-code {
  flex-shrink: 0;

  img {
    width: 200px;
    height: 200px;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
  }
}

.qr-code-tips {
  flex: 1;

  p {
    margin: 0 0 12px 0;
    color: #666;
    line-height: 1.6;
  }

  .amount-tip {
    font-size: 16px;

    .amount {
      font-size: 20px;
      font-weight: bold;
      color: #67C23A;
    }
  }
}

.link-container {
  .payment-link-input {
    margin-bottom: 16px;
  }

  .link-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
  }
}
</style>
