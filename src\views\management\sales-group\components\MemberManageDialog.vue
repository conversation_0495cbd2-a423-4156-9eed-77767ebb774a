<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`成员管理 - ${group?.name || ''}`"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="member-manage-container">
      <!-- 操作栏 -->
      <div class="action-bar">
        <el-button type="primary" @click="handleAddMember">
          <el-icon><Plus /></el-icon>
          添加成员
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>

      <!-- 当前成员列表 -->
      <div class="member-section">
        <h4>当前成员 ({{ memberList.length }}人)</h4>
        <el-table
          v-loading="loading"
          :data="memberList"
          stripe
          border
          max-height="400"
        >
          <el-table-column prop="salesName" label="姓名" width="120" />
          <el-table-column prop="salesPhone" label="手机号" width="130" />
          <el-table-column label="角色" width="100">
            <template #default="{ row }">
              <el-tag :type="row.roleType === 'leader' ? 'danger' : 'primary'" size="small">
                {{ row.roleType === 'leader' ? '组长' : '成员' }}
              </el-tag>
            </template>
          </el-table-column>
          <!-- 暂时移除学生数列，后续可以添加 -->
          <!-- <el-table-column prop="studentCount" label="负责学生数" width="120" align="center" /> -->
          <el-table-column prop="joinTime" label="加入时间" width="180" />
          <el-table-column label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                {{ row.status === 'active' ? '正常' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="row.roleType !== 'leader'"
                type="primary"
                link
                @click="handleSetAsLeader(row)"
              >
                设为组长
              </el-button>
              <el-button
                type="danger"
                link
                @click="handleRemoveMember(row)"
                :disabled="row.roleType === 'leader'"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 添加成员对话框 -->
    <el-dialog
      v-model="showAddMemberDialog"
      title="添加成员"
      width="600px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="add-member-container">
        <!-- 搜索栏 -->
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索销售人员（姓名/手机号）"
            clearable
            @input="handleSearchAvailable"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 可添加成员列表 -->
        <div class="available-members">
          <h5>可添加成员</h5>
          <el-table
            v-loading="availableLoading"
            :data="availableMemberList"
            @selection-change="handleAvailableSelectionChange"
            max-height="300"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="userName" label="姓名" width="120" />
            <el-table-column prop="nickName" label="昵称" width="120" />
            <el-table-column prop="phonenumber" label="手机号" width="130" />
            <el-table-column prop="status" label="状态" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                  {{ row.status === 'active' ? '正常' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddMemberDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            :loading="addingMembers"
            :disabled="selectedAvailableMembers.length === 0"
            @click="handleConfirmAddMembers"
          >
            添加选中成员 ({{ selectedAvailableMembers.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Search } from '@element-plus/icons-vue'
import { 
  getSalesGroupMembersApi,
  addSalesGroupMembersApi,
  removeSalesGroupMemberApi,
  setGroupLeaderApi,
  getAvailableSalesApi
} from '@/api/management/salesGroup'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  group: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const loading = ref(false)
const memberList = ref([])
const showAddMemberDialog = ref(false)
const availableLoading = ref(false)
const availableMemberList = ref([])
const selectedAvailableMembers = ref([])
const addingMembers = ref(false)
const searchKeyword = ref('')

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听props变化
watch(() => props.group, (newGroup) => {
  if (newGroup?.id) {
    loadMembers()
  }
}, { immediate: true })

// 加载成员列表
const loadMembers = async () => {
  if (!props.group?.id) return
  
  loading.value = true
  try {
    const response = await getSalesGroupMembersApi(props.group.id)
    memberList.value = response.data || []
  } catch (error) {
    ElMessage.error('获取成员列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新
const handleRefresh = () => {
  loadMembers()
}

// 添加成员
const handleAddMember = async () => {
  showAddMemberDialog.value = true
  await loadAvailableMembers()
}

// 加载可添加成员
const loadAvailableMembers = async () => {
  availableLoading.value = true
  try {
    const response = await getAvailableSalesApi({
      groupId: props.group.id,
      keyword: searchKeyword.value
    })
    availableMemberList.value = response.data || []
  } catch (error) {
    ElMessage.error('获取可添加成员失败')
  } finally {
    availableLoading.value = false
  }
}

// 搜索可添加成员
const handleSearchAvailable = () => {
  loadAvailableMembers()
}

// 选择可添加成员
const handleAvailableSelectionChange = (selection) => {
  selectedAvailableMembers.value = selection
}

// 确认添加成员
const handleConfirmAddMembers = async () => {
  if (selectedAvailableMembers.value.length === 0) {
    ElMessage.warning('请选择要添加的成员')
    return
  }

  addingMembers.value = true
  try {
    const memberIds = selectedAvailableMembers.value.map(member => member.userId)
    // 修复：传递正确的数据格式
    await addSalesGroupMembersApi({
      groupId: props.group.id,
      salesIds: memberIds
    })
    ElMessage.success('添加成员成功')
    showAddMemberDialog.value = false
    selectedAvailableMembers.value = []
    loadMembers()
    emit('success')
  } catch (error) {
    ElMessage.error('添加成员失败')
    console.error('添加成员失败:', error)
  } finally {
    addingMembers.value = false
  }
}

// 设为组长
const handleSetAsLeader = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要将"${row.salesName}"设为组长吗？`,
      '设置组长',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 修复：传递正确的数据格式
    await setGroupLeaderApi({
      groupId: props.group.id,
      leaderId: row.salesId  // 注意：后端期望的是leaderId字段
    })
    ElMessage.success('设置组长成功')
    loadMembers()
    emit('success')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('设置组长失败')
    }
  }
}

// 移除成员
const handleRemoveMember = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要将"${row.salesName}"从销售组中移除吗？`,
      '移除成员',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 修复：传递正确的数据格式
    await removeSalesGroupMemberApi({
      groupId: props.group.id,
      salesId: row.salesId
    })
    ElMessage.success('移除成员成功')
    loadMembers()
    emit('success')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除成员失败')
    }
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  showAddMemberDialog.value = false
  selectedAvailableMembers.value = []
  searchKeyword.value = ''
}
</script>

<style scoped>
.member-manage-container {
  padding: 10px 0;
}

.action-bar {
  margin-bottom: 20px;
}

.member-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.add-member-container {
  padding: 10px 0;
}

.search-bar {
  margin-bottom: 20px;
}

.available-members h5 {
  margin: 0 0 10px 0;
  color: #606266;
}

.dialog-footer {
  text-align: right;
}
</style>
