<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`上课时间安排 - ${teacher?.name || ''}`"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="teacher" class="schedule-dialog-container">
      <!-- 操作栏 -->
      <div class="action-bar">
        <div class="left-actions">
          <el-button type="primary" @click="handleEdit">
            <el-icon><Edit /></el-icon>
            编辑时间表
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>

        <div class="right-actions">
          <div class="teacher-info-section">
            <span class="teacher-info">{{ teacher.phone }}</span>
            <div v-if="lastUpdateInfo" class="update-time-info">
              <el-text size="small" type="info">
                最后更新：{{ formatUpdateTime(lastUpdateInfo.lastUpdateTime) }}
                <span v-if="lastUpdateInfo.daysSinceLastUpdate !== null">
                  （{{ lastUpdateInfo.daysSinceLastUpdate }}天前）
                </span>
              </el-text>
            </div>
          </div>
        </div>
      </div>

      <!-- 周视图日历时间选择器 -->
      <div class="calendar-editor">
        <WeeklyCalendarTimeSelector
          v-model="timeSlots"
          :readonly="!isEditing"
          @change="handleTimeSlotsChange"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="isEditing"
          type="success"
          :loading="saving"
          @click="handleSave"
        >
          保存修改
        </el-button>
        <el-button
          v-if="isEditing"
          @click="handleCancelEdit"
        >
          取消编辑
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Refresh } from '@element-plus/icons-vue'
import { useTeachingGroupStore } from '@/stores/teachingGroup'
import WeeklyCalendarTimeSelector from './WeeklyCalendarTimeSelector.vue'
import { checkTeacherTimeSlotUpdateTimeApi } from '@/api/management/teachingGroup'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  teacher: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 使用store
const teachingGroupStore = useTeachingGroupStore()

// 响应式数据
const timeSlots = ref([])
const originalTimeSlots = ref([])
const isEditing = ref(false)
const saving = ref(false)
const lastUpdateInfo = ref(null)



// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const availableCount = computed(() => {
  return timeSlots.value.filter(slot => slot.status === 'available').length
})

const scheduledCount = computed(() => {
  return timeSlots.value.filter(slot => slot.status === 'scheduled').length
})

const utilizationRate = computed(() => {
  const total = availableCount.value + scheduledCount.value
  if (total === 0) return 0
  return Math.round((scheduledCount.value / total) * 100)
})

// 监听器
watch(() => props.teacher, (newTeacher) => {
  if (newTeacher && props.modelValue) {
    fetchTimeSlots()
  }
}, { immediate: true })

watch(() => props.modelValue, (visible) => {
  if (visible && props.teacher) {
    fetchTimeSlots()
  }
})

// 方法
const fetchTimeSlots = async () => {
  if (!props.teacher?.id) return

  try {
    // 调用API获取教师时间表
    const response = await teachingGroupStore.fetchTeacherTimeSlots(props.teacher.id)

    timeSlots.value = response || []
    originalTimeSlots.value = JSON.parse(JSON.stringify(response || []))

    // 获取更新时间信息
    await fetchUpdateTimeInfo()
  } catch (error) {
    console.error('获取教师时间表失败:', error)
    ElMessage.error('获取教师时间表失败')
    timeSlots.value = []
    originalTimeSlots.value = []
  }
}

// 获取时间表更新时间信息
const fetchUpdateTimeInfo = async () => {
  if (!props.teacher?.id) return

  try {
    const response = await checkTeacherTimeSlotUpdateTimeApi(props.teacher.id)
    if (response.code === 200) {
      lastUpdateInfo.value = response.data
    }
  } catch (error) {
    console.error('获取时间表更新时间失败:', error)
    // 不显示错误信息，静默失败
  }
}





const handleEdit = () => {
  isEditing.value = true
}

const handleCancelEdit = () => {
  isEditing.value = false
  timeSlots.value = JSON.parse(JSON.stringify(originalTimeSlots.value))
}



const handleTimeSlotsChange = (newTimeSlots) => {
  // 灵活时间段变化时的处理
  console.log('时间段变化:', newTimeSlots)
}

const handleReset = () => {
  if (isEditing.value) {
    handleCancelEdit()
  } else {
    fetchTimeSlots()
  }
}

const handleSave = async () => {
  try {
    saving.value = true

    // 保存时间段数据
    const success = await teachingGroupStore.updateTeacherTimeSlots({
      teacherId: props.teacher.id,
      timeSlots: timeSlots.value
    })

    if (success) {
      originalTimeSlots.value = JSON.parse(JSON.stringify(timeSlots.value))
      isEditing.value = false
      ElMessage.success('保存成功')
      emit('success')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 格式化更新时间
const formatUpdateTime = (updateTime) => {
  if (!updateTime) return '未知'

  try {
    const date = new Date(updateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '格式错误'
  }
}

const handleClose = () => {
  if (isEditing.value) {
    handleCancelEdit()
  }
  dialogVisible.value = false
}

// 生命周期
onMounted(() => {
  if (props.modelValue && props.teacher) {
    fetchTimeSlots()
  }
})
</script>

<style lang="scss" scoped>
.schedule-dialog-container {
  .action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;

    .left-actions {
      display: flex;
      gap: 12px;
    }

    .center-actions {
      flex: 1;
      display: flex;
      justify-content: center;
    }

    .teacher-info-section {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;

      .teacher-info {
        font-size: 14px;
        color: #909399;
      }

      .update-time-info {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .flexible-editor {
    margin-bottom: 20px;
  }

  .schedule-table {
    margin-bottom: 20px;

    .time-header {
      display: grid;
      grid-template-columns: 80px repeat(7, 1fr);
      gap: 1px;
      margin-bottom: 1px;

      .header-cell {
        background-color: #f5f7fa;
        padding: 12px 8px;
        text-align: center;
        font-size: 14px;
        font-weight: 600;
        color: #606266;
        border: 1px solid #ebeef5;
      }
    }

    .time-row {
      display: grid;
      grid-template-columns: 80px repeat(7, 1fr);
      gap: 1px;
      margin-bottom: 1px;

      .time-cell {
        background-color: #fafafa;
        padding: 8px;
        text-align: center;
        font-size: 12px;
        color: #606266;
        border: 1px solid #ebeef5;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .schedule-cell {
        border: 1px solid #ebeef5;
        min-height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: default;

        .cell-content {
          font-size: 14px;
          font-weight: 600;
        }

        &.status-unavailable {
          background-color: #f56c6c;
          color: white;
        }

        &.status-available {
          background-color: #67c23a;
          color: white;
        }

        &.status-scheduled {
          background-color: #409eff;
          color: white;
        }

        &.editable {
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }

  .footer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 4px;

    .legend {
      display: flex;
      gap: 20px;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        color: #606266;

        .legend-color {
          width: 16px;
          height: 16px;
          border-radius: 2px;

          &.unavailable {
            background-color: #f56c6c;
          }

          &.available {
            background-color: #67c23a;
          }

          &.scheduled {
            background-color: #409eff;
          }
        }
      }
    }

    .statistics {
      display: flex;
      gap: 20px;

      .stat-item {
        font-size: 12px;

        .stat-label {
          color: #909399;
        }

        .stat-value {
          color: #409eff;
          font-weight: 600;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }

  .schedule-dialog-container {
    .action-bar {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
    }

    .schedule-table {
      .time-header,
      .time-row {
        grid-template-columns: 60px repeat(7, 1fr);
      }

      .time-cell {
        font-size: 10px;
        padding: 4px;
      }

      .schedule-cell {
        min-height: 24px;

        .cell-content {
          font-size: 12px;
        }
      }
    }

    .footer-info {
      flex-direction: column;
      gap: 12px;

      .legend {
        flex-wrap: wrap;
        gap: 12px;
      }

      .statistics {
        flex-wrap: wrap;
        gap: 12px;
      }
    }
  }
}
</style>
