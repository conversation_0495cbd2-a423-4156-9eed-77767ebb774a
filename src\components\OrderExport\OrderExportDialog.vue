<template>
  <el-dialog
    title="导出订单数据"
    :visible.sync="visible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="exportForm"
      :model="exportForm"
      :rules="exportRules"
      label-width="120px"
      size="small"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="订单号">
            <el-input v-model="exportForm.orderNo" placeholder="请输入订单号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单状态">
            <el-select v-model="exportForm.orderStatus" placeholder="请选择订单状态" clearable>
              <el-option label="未付款" value="未付款" />
              <el-option label="已付款" value="已付款" />
              <el-option label="已全额支付" value="已全额支付" />
              <el-option label="已部分支付" value="已部分支付" />
              <el-option label="已退款" value="已退款" />
              <el-option label="已取消" value="已取消" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学生姓名">
            <el-input v-model="exportForm.studentName" placeholder="请输入学生姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学生手机号">
            <el-input v-model="exportForm.studentPhone" placeholder="请输入学生手机号" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="销售员姓名">
            <el-input v-model="exportForm.salerName" placeholder="请输入销售员姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单来源">
            <el-select v-model="exportForm.source" placeholder="请选择订单来源" clearable>
              <el-option label="线上" value="线上" />
              <el-option label="线下" value="线下" />
              <el-option label="电话" value="电话" />
              <el-option label="推荐" value="推荐" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学科">
            <el-select v-model="exportForm.subject" placeholder="请选择学科" clearable>
              <el-option label="数学" value="数学" />
              <el-option label="英语" value="英语" />
              <el-option label="语文" value="语文" />
              <el-option label="物理" value="物理" />
              <el-option label="化学" value="化学" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="课型">
            <el-select v-model="exportForm.courseType" placeholder="请选择课型" clearable>
              <el-option label="一对一" value="一对一" />
              <el-option label="小班课" value="小班课" />
              <el-option label="大班课" value="大班课" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="createTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="handleCreateTimeChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="支付时间">
            <el-date-picker
              v-model="payTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="handlePayTimeChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最小金额(元)">
            <el-input-number
              v-model="minAmount"
              :min="0"
              :precision="2"
              placeholder="最小订单金额"
              style="width: 100%"
              @change="handleMinAmountChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大金额(元)">
            <el-input-number
              v-model="maxAmount"
              :min="0"
              :precision="2"
              placeholder="最大订单金额"
              style="width: 100%"
              @change="handleMaxAmountChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最大导出数量" prop="maxExportCount">
            <el-input-number
              v-model="exportForm.maxExportCount"
              :min="1"
              :max="10000"
              placeholder="最大导出数量"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="包含交易记录">
            <el-switch v-model="exportForm.includeTransactions" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" :loading="exporting" @click="handleExport">
        {{ exporting ? '导出中...' : '确认导出' }}
      </el-button>
    </div>

    <!-- 导出进度对话框 -->
    <ExportProgress
      :visible.sync="progressVisible"
      :status="progressStatus"
      :progress="progressValue"
      :message="progressMessage"
      :fileName="progressFileName"
      :exportCount="progressExportCount"
      :fileSize="progressFileSize"
      :exportTime="progressExportTime"
      :errorMessage="progressErrorMessage"
      @cancel="handleProgressCancel"
      @retry="handleProgressRetry"
      @close="handleProgressClose"
    />
  </el-dialog>
</template>

<script>
import { exportOrdersApi } from '@/api/management/order'
import { managerExportOrdersApi } from '@/api/management/order-manager'
import { exportExcel, formatExportParams, validateExportParams, generateExportFileName } from '@/utils/download'
import ExportProgress from './ExportProgress.vue'

export default {
  name: 'OrderExportDialog',
  components: {
    ExportProgress
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isManager: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      exportForm: {
        orderNo: '',
        orderStatus: '',
        studentName: '',
        studentPhone: '',
        salerName: '',
        source: '',
        subject: '',
        courseType: '',
        createTimeStart: '',
        createTimeEnd: '',
        lastPayTimeStart: '',
        lastPayTimeEnd: '',
        minTotalAmt: null,
        maxTotalAmt: null,
        maxExportCount: 1000,
        includeTransactions: false,
        exportFormat: 'excel'
      },
      exportRules: {
        maxExportCount: [
          { required: true, message: '请输入最大导出数量', trigger: 'blur' },
          { type: 'number', min: 1, max: 10000, message: '导出数量必须在1-10000之间', trigger: 'blur' }
        ]
      },
      createTimeRange: [],
      payTimeRange: [],
      minAmount: null,
      maxAmount: null,
      exporting: false,
      // 进度相关
      progressVisible: false,
      progressStatus: 'processing',
      progressValue: 0,
      progressMessage: '',
      progressFileName: '',
      progressExportCount: 0,
      progressFileSize: 0,
      progressExportTime: null,
      progressErrorMessage: ''
    }
  },
  methods: {
    handleCreateTimeChange(value) {
      if (value && value.length === 2) {
        this.exportForm.createTimeStart = value[0]
        this.exportForm.createTimeEnd = value[1]
      } else {
        this.exportForm.createTimeStart = ''
        this.exportForm.createTimeEnd = ''
      }
    },
    handlePayTimeChange(value) {
      if (value && value.length === 2) {
        this.exportForm.lastPayTimeStart = value[0]
        this.exportForm.lastPayTimeEnd = value[1]
      } else {
        this.exportForm.lastPayTimeStart = ''
        this.exportForm.lastPayTimeEnd = ''
      }
    },
    handleMinAmountChange(value) {
      this.exportForm.minTotalAmt = value ? Math.round(value * 100) : null
    },
    handleMaxAmountChange(value) {
      this.exportForm.maxTotalAmt = value ? Math.round(value * 100) : null
    },
    handleExport() {
      this.$refs.exportForm.validate((valid) => {
        if (valid) {
          this.doExport()
        }
      })
    },
    async doExport() {
      try {
        this.exporting = true
        this.progressVisible = true

        // 格式化导出参数
        const params = formatExportParams(this.exportForm)

        // 验证导出参数
        const validation = validateExportParams(params)
        if (!validation.valid) {
          this.$message.error(validation.message)
          this.progressVisible = false
          return
        }

        // 选择导出API
        const exportApi = this.isManager ? managerExportOrdersApi : exportOrdersApi

        // 生成文件名
        const fileName = generateExportFileName('订单数据', params)

        // 执行导出
        await exportExcel(
          exportApi,
          params,
          fileName,
          (fileName, fileSize) => {
            // 成功回调在进度回调中处理
          },
          (error) => {
            // 错误回调在进度回调中处理
          },
          (progressInfo) => {
            // 进度回调
            this.updateProgress(progressInfo)
          }
        )
      } catch (error) {
        this.updateProgress({
          status: 'failed',
          errorMessage: error.message || '未知错误'
        })
      } finally {
        this.exporting = false
      }
    },
    updateProgress(progressInfo) {
      this.progressStatus = progressInfo.status
      this.progressValue = progressInfo.progress || 0
      this.progressMessage = progressInfo.message || ''
      this.progressFileName = progressInfo.fileName || ''
      this.progressExportCount = progressInfo.exportCount || 0
      this.progressFileSize = progressInfo.fileSize || 0
      this.progressExportTime = progressInfo.exportTime || null
      this.progressErrorMessage = progressInfo.errorMessage || ''
    },
    handleProgressCancel() {
      this.progressVisible = false
      this.exporting = false
      this.$message.info('导出已取消')
    },
    handleProgressRetry() {
      this.progressVisible = false
      this.doExport()
    },
    handleProgressClose() {
      this.progressVisible = false
      if (this.progressStatus === 'completed') {
        this.handleClose()
      }
    },
    handleClose() {
      this.$emit('update:visible', false)
      this.resetForm()
    },
    resetForm() {
      this.$refs.exportForm.resetFields()
      this.createTimeRange = []
      this.payTimeRange = []
      this.minAmount = null
      this.maxAmount = null
      this.exporting = false
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
