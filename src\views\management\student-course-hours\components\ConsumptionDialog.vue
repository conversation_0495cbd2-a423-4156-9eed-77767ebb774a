<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1400px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="学生手机" prop="studentPhone">
        <el-input
          v-model="queryParams.studentPhone"
          placeholder="请输入学生手机号"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="学科" prop="subject">
        <el-select v-model="queryParams.subject" placeholder="请选择学科" clearable style="width: 120px" @change="handleSubjectChange">
          <el-option label="英语" value="英语" />
          <el-option label="语文" value="语文" />
          <el-option label="数学" value="数学" />
          <el-option label="物理" value="物理" />
          <el-option label="化学" value="化学" />
        </el-select>
      </el-form-item>
      <el-form-item label="课型" prop="specification">
        <el-select v-model="queryParams.specification" placeholder="请选择课型" clearable style="width: 120px">
          <el-option
            v-for="option in availableSpecifications"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="老师姓名" prop="teacherName">
        <el-input
          v-model="queryParams.teacherName"
          placeholder="请输入老师姓名"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="教学组" prop="teachingGroupName">
        <el-input
          v-model="queryParams.teachingGroupName"
          placeholder="请输入教学组名称"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="销售人员" prop="salesName">
        <el-input
          v-model="queryParams.salesName"
          placeholder="请输入销售人员姓名"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="销售组" prop="salesGroupName">
        <el-input
          v-model="queryParams.salesGroupName"
          placeholder="请输入销售组名称"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="课时类型" prop="courseHoursType">
        <el-select
          v-model="queryParams.courseHoursType"
          placeholder="请选择课时类型"
          clearable
          style="width: 120px"
        >
          <el-option label="首课" value="首课" />
          <el-option label="续费" value="续费" />
        </el-select>
      </el-form-item>
      <el-form-item label="课消时间">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="consumptionList" border max-height="400">
      <el-table-column label="学生姓名" prop="studentName" width="120" />
      <el-table-column label="学生手机" prop="studentPhone" width="130" />
      <el-table-column label="学科" prop="subject" width="80" />
      <el-table-column label="课型" prop="specification" width="80" />
      <el-table-column label="课消课时" prop="consumedHours" width="100" align="right">
        <template #default="scope">
          <span class="text-danger">{{ scope.row.consumedHours }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课消时间" prop="consumptionTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.consumptionTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="老师姓名" prop="teacherName" width="120" />
      <el-table-column label="教学组" prop="teachingGroupName" width="120" />
      <el-table-column label="销售人员" prop="salesName" width="120" />
      <el-table-column label="销售组" prop="salesGroupName" width="120" />
      <el-table-column label="课时类型" width="100" align="center">
        <template #default="scope">
          <el-tag 
            :type="scope.row.courseHoursType === '正式' ? 'success' : 'warning'" 
            size="small"
          >
            {{ scope.row.courseHoursType || '未知' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="批次号" prop="batchNo" width="150" />
      <el-table-column label="备注" prop="remark" min-width="150" show-overflow-tooltip />
      <el-table-column label="状态" prop="status" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
            {{ scope.row.status === 'active' ? '有效' : '已取消' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      style="margin-top: 20px"
    />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { parseTime } from '@/utils/ruoyi'
import { listConsumptionRecords } from '@/api/management/studentCourseHours'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  studentInfo: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const dialogTitle = computed(() => {
  return props.studentInfo
    ? `课消记录查询 - ${props.studentInfo.studentName} (${props.studentInfo.subject} - ${props.studentInfo.specification})`
    : '课消记录查询'
})

const loading = ref(false)
const consumptionList = ref([])
const total = ref(0)
const dateRange = ref([])

// 课型选项配置
const allSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" },
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

const englishSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" }
]

const otherSubjectSpecifications = [
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

// 可用的课型选项
const availableSpecifications = ref(allSpecifications)

// 学科变化处理
const handleSubjectChange = (subject) => {
  // 清空当前选择的课型
  queryParams.value.specification = ''

  if (!subject) {
    // 没有选择学科，显示所有课型
    availableSpecifications.value = allSpecifications
  } else if (subject === '英语') {
    // 英语学科，显示英语相关课型
    availableSpecifications.value = englishSpecifications
  } else {
    // 其他学科，只显示通用课
    availableSpecifications.value = otherSubjectSpecifications
  }
}

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  studentName: '',
  studentPhone: '',
  subject: '',
  specification: '',
  teacherName: '',
  teachingGroupName: '',
  salesName: '',
  salesGroupName: '',
  courseHoursType: '',
  startTime: '',
  endTime: '',
  status: ''
})

// 查询表单引用
const queryRef = ref()

// 监听时间范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    queryParams.value.startTime = newVal[0]
    queryParams.value.endTime = newVal[1]
  } else {
    queryParams.value.startTime = ''
    queryParams.value.endTime = ''
  }
})

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    getList()
  }
})

// 监听对话框打开和学生信息变化
watch(() => [props.visible, props.studentInfo], ([visible, studentInfo]) => {
  if (visible) {
    if (studentInfo) {
      // 如果传入了学生信息，自动设置查询条件
      queryParams.value.studentId = studentInfo.studentId
      queryParams.value.subject = studentInfo.subject
      queryParams.value.specification = studentInfo.specification
    } else {
      // 清空学生相关的查询条件
      queryParams.value.studentId = null
      queryParams.value.subject = ''
      queryParams.value.specification = ''
    }
    getList()
  }
})

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const response = await listConsumptionRecords(queryParams.value)
    consumptionList.value = response.rows
    total.value = response.total
  } catch (error) {
    console.error('获取课消记录失败:', error)
    ElMessage.error('获取课消记录失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}

// 重置
const resetQuery = () => {
  queryRef.value?.resetFields()
  dateRange.value = []
  queryParams.value.startTime = ''
  queryParams.value.endTime = ''
  handleQuery()
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}
</style>
