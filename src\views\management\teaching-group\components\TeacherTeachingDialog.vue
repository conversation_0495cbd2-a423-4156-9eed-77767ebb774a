<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`带教情况 - ${teacher?.name || ''}`"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="teacher" class="teaching-dialog-container">
      <!-- 概览卡片 -->
      <div class="overview-cards">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon students">
              <el-icon><User /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">{{ teachingInfo.currentStudents || 0 }}</div>
              <div class="card-label">在教学生</div>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon unconsumed">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">{{ teachingInfo.unconsumedHours || 0 }}</div>
              <div class="card-label">未消课时</div>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon consumed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">{{ teachingInfo.consumedHours || 0 }}</div>
              <div class="card-label">已消课时</div>
            </div>
          </div>
        </el-card>

        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon total">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">{{ teachingInfo.totalHours || 0 }}</div>
              <div class="card-label">总课时</div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 课时进度 -->
      <el-card class="progress-card">
        <template #header>
          <span>课时进度</span>
        </template>
        <div class="progress-content">
          <div class="progress-info">
            <span>完成进度：{{ progressPercentage }}%</span>
            <span>剩余课时：{{ teachingInfo.unconsumedHours || 0 }}小时</span>
          </div>
          <el-progress
            :percentage="progressPercentage"
            :color="progressColor"
            :stroke-width="12"
            text-inside
          />
        </div>
      </el-card>

      <!-- 学生列表 -->
      <el-card class="students-card">
        <template #header>
          <div class="card-header">
            <span>学生列表</span>
            <el-button type="primary" size="small" @click="handleAddStudent">
              <el-icon><Plus /></el-icon>
              添加学生
            </el-button>
          </div>
        </template>
        <el-table
          :data="studentList"
          stripe
          style="width: 100%"
          max-height="300px"
        >
          <el-table-column prop="name" label="学生姓名" width="120" />
          <el-table-column prop="grade" label="年级" width="100" />
          <el-table-column prop="subject" label="学科" width="100" />
          <el-table-column prop="totalHours" label="总课时" width="100" align="center" />
          <el-table-column prop="consumedHours" label="已消课时" width="100" align="center" />
          <el-table-column prop="remainingHours" label="剩余课时" width="100" align="center">
            <template #default="{ row }">
              <span :class="{ 'low-hours': row.remainingHours <= 5 }">
                {{ row.remainingHours }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="lastClassTime" label="最近上课" width="120">
            <template #default="{ row }">
              <span>{{ formatDate(row.lastClassTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStudentStatusTagType(row.status)" size="small">
                {{ getStudentStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleViewStudent(row)">
                查看
              </el-button>
              <el-button type="warning" link @click="handleEditStudent(row)">
                编辑
              </el-button>
              <el-button type="danger" link @click="handleRemoveStudent(row)">
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 教学统计 -->
      <el-card class="statistics-card">
        <template #header>
          <span>教学统计</span>
        </template>
        <div class="statistics-grid">
          <div class="stat-item">
            <div class="stat-label">平均课时/学生</div>
            <div class="stat-value">{{ averageHoursPerStudent }}小时</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">本月已上课时</div>
            <div class="stat-value">{{ monthlyHours }}小时</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">本周已上课时</div>
            <div class="stat-value">{{ weeklyHours }}小时</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">教学状态</div>
            <div class="stat-value">
              <el-tag :type="getTeachingStatusTagType()" size="small">
                {{ getTeachingStatusText() }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Clock, Check, DataAnalysis, Plus, Download } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/date'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  teacher: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'add-student', 'edit-student', 'view-student'])

// 响应式数据
const teachingInfo = ref({
  currentStudents: 0,
  unconsumedHours: 0,
  consumedHours: 0,
  totalHours: 0
})

const studentList = ref([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const progressPercentage = computed(() => {
  const total = teachingInfo.value.totalHours || 0
  const consumed = teachingInfo.value.consumedHours || 0
  if (total === 0) return 0
  return Math.round((consumed / total) * 100)
})

const progressColor = computed(() => {
  const percentage = progressPercentage.value
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
})

const averageHoursPerStudent = computed(() => {
  const students = teachingInfo.value.currentStudents || 0
  const total = teachingInfo.value.totalHours || 0
  if (students === 0) return 0
  return Math.round((total / students) * 10) / 10
})

const monthlyHours = computed(() => {
  // 模拟本月已上课时
  return Math.floor(Math.random() * 50) + 20
})

const weeklyHours = computed(() => {
  // 模拟本周已上课时
  return Math.floor(Math.random() * 15) + 5
})

// 监听器
watch(() => props.teacher, (newTeacher) => {
  if (newTeacher && props.modelValue) {
    fetchTeachingInfo()
  }
}, { immediate: true })

watch(() => props.modelValue, (visible) => {
  if (visible && props.teacher) {
    fetchTeachingInfo()
  }
})

// 方法
const fetchTeachingInfo = async () => {
  if (!props.teacher?.id) return

  try {
    // 调用API获取教师带教信息
    const response = await teachingGroupStore.fetchTeachingInfo(props.teacher.id)
    if (response) {
      teachingInfo.value = response.teachingInfo
      studentList.value = response.studentList || []
    }
  } catch (error) {
    console.error('获取带教信息失败:', error)
    ElMessage.error('获取带教信息失败')
  }
}



const getStudentStatusText = (status) => {
  const statusMap = {
    active: '正常',
    low: '课时不足',
    finished: '已完成',
    paused: '暂停'
  }
  return statusMap[status] || '未知'
}

const getStudentStatusTagType = (status) => {
  const typeMap = {
    active: 'success',
    low: 'warning',
    finished: 'info',
    paused: 'danger'
  }
  return typeMap[status] || 'info'
}

const getTeachingStatusText = () => {
  const students = teachingInfo.value.currentStudents || 0
  const unconsumed = teachingInfo.value.unconsumedHours || 0
  
  if (students === 0) return '空闲'
  if (unconsumed === 0) return '课时用完'
  if (students <= 3) return '正常'
  if (students <= 6) return '繁忙'
  return '超负荷'
}

const getTeachingStatusTagType = () => {
  const status = getTeachingStatusText()
  const typeMap = {
    '空闲': 'info',
    '课时用完': 'warning',
    '正常': 'success',
    '繁忙': 'warning',
    '超负荷': 'danger'
  }
  return typeMap[status] || 'info'
}

const handleAddStudent = () => {
  emit('add-student', props.teacher)
}

const handleViewStudent = (student) => {
  emit('view-student', student)
}

const handleEditStudent = (student) => {
  emit('edit-student', student)
}

const handleRemoveStudent = (student) => {
  ElMessage.info('移除学生功能待实现')
}

const handleExport = () => {
  ElMessage.info('导出报告功能待实现')
}

const handleClose = () => {
  dialogVisible.value = false
}

// 生命周期
onMounted(() => {
  if (props.modelValue && props.teacher) {
    fetchTeachingInfo()
  }
})
</script>

<style lang="scss" scoped>
.teaching-dialog-container {
  .overview-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-bottom: 20px;

    .overview-card {
      .card-content {
        display: flex;
        align-items: center;
        gap: 12px;

        .card-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: white;

          &.students {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }

          &.unconsumed {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }

          &.consumed {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }

          &.total {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
        }

        .card-info {
          .card-value {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .card-label {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }

  .progress-card {
    margin-bottom: 20px;

    .progress-content {
      .progress-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        font-size: 14px;
        color: #606266;
      }
    }
  }

  .students-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .low-hours {
      color: #e6a23c;
      font-weight: 600;
    }
  }

  .statistics-card {
    .statistics-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;

      .stat-item {
        text-align: center;

        .stat-label {
          font-size: 12px;
          color: #909399;
          margin-bottom: 8px;
        }

        .stat-value {
          font-size: 18px;
          font-weight: 600;
          color: #409eff;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
  max-height: 70vh;
  overflow-y: auto;
}

// 响应式设计
@media (max-width: 1200px) {
  .teaching-dialog-container {
    .overview-cards {
      grid-template-columns: repeat(2, 1fr);
    }

    .statistics-card {
      .statistics-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }

  .teaching-dialog-container {
    .overview-cards {
      grid-template-columns: 1fr;
    }

    .statistics-card {
      .statistics-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>
