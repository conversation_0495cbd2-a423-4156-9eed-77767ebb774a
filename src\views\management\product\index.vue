<template>
  <div class="product-management-container">
    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline label-width="80px">
        <el-form-item label="产品编号">
          <el-input
              v-model="searchForm.productNo"
              placeholder="请输入产品编号"
              clearable
              style="width: 200px"
              @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="产品名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入产品名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="学科" prop="subject">
          <el-select
            v-model="searchForm.subject"
            placeholder="请选择学科"
            clearable
            style="width: 120px"
            @change="handleSubjectChange"
          >
            <el-option label="英语" value="英语" />
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
          </el-select>
        </el-form-item>
        <el-form-item label="课型" prop="courseType">
          <el-select
            v-model="searchForm.courseType"
            placeholder="请选择课型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="option in availableSpecifications"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="上架" value="上架" />
            <el-option label="下架" value="下架" />
          </el-select>
        </el-form-item>
        <el-form-item label="客户类型">
          <el-select
            v-model="searchForm.customerType"
            placeholder="请选择客户类型"
            clearable
            style="width: 150px"
          >
            <el-option label="通用" value="通用" />
            <el-option label="仅老客户可用" value="仅老客户可用" />
            <el-option label="仅新客户可用" value="仅新客户可用" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="handleAdd" v-hasPermi="['management:products:add']">
            <el-icon><Plus /></el-icon>
            新增产品
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 产品列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="productList"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="产品编号" prop="productNo" min-width="200" />
        <el-table-column label="产品名称" prop="name" min-width="200" />
        <el-table-column label="学科/课型" prop="subject" width="180" align="center">
          <template #default="{ row }">
            {{ row.subject }} / {{ row.courseType }}
          </template>
        </el-table-column>
        <el-table-column label="适用年级" prop="applicableGrades" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.applicableGrades && row.applicableGrades.length">
              {{ row.applicableGrades.join(', ') }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="课时" width="180" align="center" >
          <template #default="{ row }">
            <div>
              正课课时：{{ row.quantity }}
            </div>
            <div>
              赠送课时：{{ row.bonusHoursQuantity }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="售价" width="150" align="center">
          <template #default="{ row }">
            <div style="text-decoration: line-through; color: #999;">
              原价：¥{{ (row.originalPrice / 100).toFixed(2) }}
            </div>
            <div style="color: red;">
              售价：¥{{ (row.sellingPrice / 100).toFixed(2) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === '上架' ? 'success' : 'danger'" size="small">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="有效状态" prop="validStatus" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.validStatus === '有效' ? 'success' : 'warning'" size="small">
              {{ row.validStatus || '有效' }}
            </el-tag>
            <div v-if="row.validStatus === '无效' && row.invalidReason" style="font-size: 12px; color: #f56c6c; margin-top: 2px;">
              {{ row.invalidReason }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="客户类型" prop="customerType" width="120" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.customerType === '通用' ? 'primary' : row.customerType === '仅老客户可用' ? 'success' : 'warning'"
              size="small"
            >
              {{ row.customerType || '通用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320" fixed="right">
          <template #default="{ row }">
            <el-button v-if="row.status === '下架' && row.publishVersion === 0"
              type="primary"
              link
              @click="handleUpdate(row)"
              v-hasPermi="['management:products:edit']"
            >
              编辑
            </el-button>
            <el-button
                type="primary"
                link
                @click="handleOrder(row)"
                v-if="row.status === '上架'"
                v-hasPermi="['order:pay']"
            >
              产品下单
            </el-button>
            <el-button
                type="success"
                link
                @click="handleWriteoff(row)"
                v-if="row.status === '上架'"
                v-hasPermi="['order:writeoff:apply']"
            >
              订单核销
            </el-button>
            <el-button
              v-if="row.status === '下架'"
              type="success"
              link
              @click="handleEnable(row)"
              v-hasPermi="['management:products:enable']"
            >
              上架
            </el-button>
            <el-button
              v-if="row.status === '上架'"
              type="warning"
              link
              @click="handleDisable(row)"
              v-hasPermi="['management:products:disable']"
            >
              下架
            </el-button>
            <el-button
              v-if="row.status === '下架'"
              type="danger"
              link
              @click="handleDelete(row)"
              v-hasPermi="['management:products:remove']"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.pageNum"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑产品对话框 -->
    <CreateEditProductDialog
      v-model="dialogVisible"
      :product="currentProduct"
      @success="handleDialogSuccess"
    />

    <!-- 学生选择对话框 -->
    <el-dialog
      v-model="studentSelectVisible"
      title="选择学生进行订单核销"
      width="1000px"
      @close="handleStudentSelectCancel"
    >
      <div class="student-select-content">
        <el-card shadow="never" style="margin-bottom: 20px">
          <template #header>
            <span class="course-info-title">产品信息</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="产品名称">
              {{ currentProductForWriteOff?.name }}
            </el-descriptions-item>
            <el-descriptions-item label="学科信息">
              {{ currentProductForWriteOff?.subject }}
              {{ currentProductForWriteOff?.courseType }}
            </el-descriptions-item>
            <el-descriptions-item label="适用年级">
              <el-tag size="small" style="margin-right: 10px" v-for="grade in currentProductForWriteOff?.applicableGrades" :key="grade">{{ grade }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="客户类型">
              <el-tag :type="getCustomerTypeTagType(currentProductForWriteOff?.customerType)">
                {{ currentProductForWriteOff?.customerType }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card shadow="never">
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>选择学生</span>
              <div>
                <el-input
                  v-model="studentSearchForm.name"
                  placeholder="搜索学生姓名"
                  style="width: 200px; margin-right: 10px"
                  @keyup.enter="searchStudents"
                  clearable
                />
                <el-input
                  v-model="studentSearchForm.phone"
                  placeholder="搜索手机号"
                  style="width: 200px; margin-right: 10px"
                  @keyup.enter="searchStudents"
                  clearable
                />
                <el-button type="primary" @click="searchStudents">搜索</el-button>
                <el-button @click="resetStudentSearch">重置</el-button>
              </div>
            </div>
          </template>

          <el-table
            v-loading="studentLoading"
            :data="studentList"
            style="width: 100%"
            max-height="400"
          >
            <el-table-column label="学生姓名" prop="name" width="120" />
            <el-table-column label="手机号" prop="phone" width="140" />
            <el-table-column label="年级" width="100">
              <template #default="{ row }">
                <el-tag
                  size="small"
                  :type="isStudentApplicable(row) ? 'success' : 'danger'"
                >
                  {{ getGradeText(row.grade) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="家长信息" width="200">
              <template #default="{ row }">
                <div>
                  <span>{{ row.parentName || '未设置' }}</span>
                  <br>
                  <span style="color: #999; font-size: 12px">{{ row.parentPhone || '未设置' }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="适用状态" width="150">
              <template #default="{ row }">
                <div v-if="!isStudentApplicable(row)" class="incompatible-reasons">
                  <el-tag
                    v-for="reason in getStudentIncompatibleReasons(row)"
                    :key="reason"
                    type="danger"
                    size="small"
                    style="margin-right: 5px; margin-bottom: 2px"
                  >
                    {{ reason }}
                  </el-tag>
                </div>
                <el-tag v-else type="success" size="small">适用</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  :disabled="!isStudentApplicable(row)"
                  @click="handleStudentSelect(row)"
                >
                  选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div style="margin-top: 20px; text-align: center">
            <el-pagination
              v-model:current-page="studentPagination.pageNum"
              v-model:page-size="studentPagination.pageSize"
              :total="studentPagination.total"
              :page-sizes="[10, 20, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleStudentSizeChange"
              @current-change="handleStudentCurrentChange"
            />
          </div>
        </el-card>
      </div>
    </el-dialog>

    <!-- 订单核销弹窗 -->
    <OrderWriteroffDialog
      v-model="writeoffDialogVisible"
      :student-info="selectedStudent"
      :product-info="currentProductForWriteOff"
      @success="handleWriteoffSuccess"
    />
  </div>
</template>

<script setup lang="ts" name="ProductManagement">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date.js'
import {
  getProductListApi,
  getProductDetailApi,
  deleteProductApi,
  enableProductApi,
  disableProductApi
} from '@/api/management/product'
import CreateEditProductDialog from './components/CreateEditProductDialog.vue'
import OrderWriteroffDialog from '@/views/management/order-writeroff/components/OrderWriteroffDialog.vue'
import { getStudentListApi } from '@/api/management/studentManagement.js'
import { getGradeText, GRADE_OPTIONS } from '@/utils/gradeUtils.js'
import {useRouter} from "vue-router";

// 响应式数据
const loading = ref(false)
const productList = ref([])
const dialogVisible = ref(false)
const currentProduct = ref(null)
const router = useRouter();

// 订单核销相关
const studentSelectVisible = ref(false)
const writeoffDialogVisible = ref(false)
const currentProductForWriteOff = ref(null)
const selectedStudent = ref(null)
const studentLoading = ref(false)
const studentList = ref([])

// 学生搜索表单
const studentSearchForm = reactive({
  name: '',
  phone: ''
})

// 学生分页
const studentPagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 课型选项配置
const allSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" },
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

const englishSpecifications = [
  { label: "单词课", value: "单词课" },
  { label: "音标拼读课", value: "音标拼读课" },
  { label: "语法课", value: "语法课" },
  { label: "题型课", value: "题型课" },
  { label: "听说课", value: "听说课" }
]

const otherSubjectSpecifications = [
  { label: "通用课（非英语）", value: "通用课（非英语）" }
]

// 可用的课型选项
const availableSpecifications = ref(allSpecifications)

// 搜索表单
const searchForm = reactive({
  productNo: '',
  name: '',
  courseType: '',
  subject: '',
  status: '',
  customerType: ''
})

// 分页数据
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 学科变化处理
const handleSubjectChange = (subject: string) => {
  searchForm.courseType = ''
  if (!subject) {
    // 没有选择学科，显示所有课型
    availableSpecifications.value = allSpecifications
  } else if (subject === '英语') {
    // 英语学科，显示英语相关课型
    availableSpecifications.value = englishSpecifications
  } else {
    // 其他学科，只显示通用课
    availableSpecifications.value = otherSubjectSpecifications
  }
}

// 方法
const fetchData = async () => {
  try {
    loading.value = true
    const queryParams = {
      ...searchForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    }
    const response = await getProductListApi(queryParams)
    if (response.data) {
      productList.value = response.data.records || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取产品列表失败:', error)
    ElMessage.error('获取产品列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.pageNum = 1
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    courseType: '',
    subject: '',
    status: ''
  })
  handleSearch()
}

const handleAdd = () => {
  currentProduct.value = null
  dialogVisible.value = true
}

const handleUpdate = async (row) => {
  try {
    const response = await getProductDetailApi(row.id)
    if (response.data) {
      currentProduct.value = response.data
      dialogVisible.value = true
    }
  } catch (error) {
    ElMessage.error('获取产品详情失败')
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('是否确认删除该产品？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await deleteProductApi(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleEnable = async (row) => {
  try {
    await ElMessageBox.confirm('上架后售价不可更改，确认要上架该产品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    await enableProductApi(row.id)
    ElMessage.success('上架成功')
    await fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('上架失败')
    }
  }
}

const handleOrder = async (row) => {
  await router.push({ path: '/product/order', query: { productId: row.id } })
}

// 处理订单核销
const handleWriteoff = (row) => {
  currentProductForWriteOff.value = row
  studentSelectVisible.value = true
  getStudentList()
}

const handleDisable = async (row) => {
  try {
    await ElMessageBox.confirm('确认要下架该产品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    await disableProductApi(row.id)
    ElMessage.success('下架成功')
    await fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('下架失败')
    }
  }
}

const handleSelectionChange = (selection) => {
  // 处理选择变化
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.pageNum = page
  fetchData()
}

const handleDialogSuccess = () => {
  fetchData()
}

// 获取学生列表
const getStudentList = async () => {
  studentLoading.value = true
  try {
    const params = {
      pageNum: studentPagination.pageNum,
      pageSize: studentPagination.pageSize,
      name: studentSearchForm.name,
      phone: studentSearchForm.phone
    }

    const response = await getStudentListApi(params)
    studentList.value = response.data?.records || []
    studentPagination.total = response.data?.total || 0
  } catch (error) {
    ElMessage.error('获取学生列表失败')
  } finally {
    studentLoading.value = false
  }
}

// 搜索学生
const searchStudents = () => {
  studentPagination.pageNum = 1
  getStudentList()
}

// 重置学生搜索
const resetStudentSearch = () => {
  Object.assign(studentSearchForm, {
    name: '',
    phone: ''
  })
  searchStudents()
}

// 学生分页大小变化
const handleStudentSizeChange = (size) => {
  studentPagination.pageSize = size
  studentPagination.pageNum = 1
  getStudentList()
}

// 学生分页页码变化
const handleStudentCurrentChange = (page) => {
  studentPagination.pageNum = page
  getStudentList()
}

// 选择学生
const handleStudentSelect = (student) => {
  selectedStudent.value = student
  studentSelectVisible.value = false
  writeoffDialogVisible.value = true
}

// 取消学生选择
const handleStudentSelectCancel = () => {
  studentSelectVisible.value = false
  selectedStudent.value = null
  currentProductForWriteOff.value = null
}

// 订单核销成功回调
const handleWriteoffSuccess = () => {
  ElMessage.success('订单核销成功')
  // 可以在这里刷新相关数据
}

// 检查学生是否适用于产品
const isStudentApplicable = (student) => {
  if (!currentProductForWriteOff.value || !student) {
    return false
  }

  const reasons = getStudentIncompatibleReasons(student)
  return reasons.length === 0
}

// 获取学生不适用原因
const getStudentIncompatibleReasons = (student) => {
  if (!currentProductForWriteOff.value || !student) {
    return []
  }

  const product = currentProductForWriteOff.value
  const reasons = []

  // 检查年级适用性
  if (!isProductApplicableForStudent(product, student)) {
    reasons.push('年级不适用')
  }

  // 检查家长信息完整性
  if (!isParentInfoComplete(student)) {
    reasons.push('家长信息不完整')
  }

  return reasons
}

// 检查产品是否适用于学生
const isProductApplicableForStudent = (product, student) => {
  if (!product || !student || !product.applicableGrades || !student.grade) {
    return false
  }

  return product.applicableGrades.includes(getGradeText(student.grade))
}

// 检查家长信息是否完整
const isParentInfoComplete = (student) => {
  if (!student) {
    return false
  }
  // 检查家长姓名和家长手机号是否都不为空
  return !!(student.parentName && student.parentName.trim() && student.parentPhone && student.parentPhone.trim())
}

// 获取客户类型标签类型
const getCustomerTypeTagType = (customerType) => {
  const typeMap = {
    '通用': 'primary',
    '仅新客可用': 'warning',
    '仅老客可用': 'success'
  }
  return typeMap[customerType] || 'info'
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.product-management-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;

  .el-form {
    .el-form-item {
      margin-bottom: 16px;
    }
  }
}

.table-card {
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

.student-select-content {
  .course-info-title {
    font-weight: 500;
    color: #303133;
  }

  .incompatible-reasons {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
}
</style>
