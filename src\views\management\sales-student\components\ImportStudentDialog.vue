<template>
  <el-dialog
    v-model="visible"
    title="导入学生"
    width="600px"
    :before-close="handleClose"
  >
    <div class="import-content">
      <!-- 导入说明 -->
      <el-alert
        title="导入说明"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="import-tips">
            <p>1. 请先下载导入模板，按照模板格式填写学生信息</p>
            <p>2. 支持的文件格式：.xlsx、.xls</p>
            <p>3. 单次最多导入1000条记录</p>
            <p>4. 手机号码不能重复，重复的记录将被跳过</p>
          </div>
        </template>
      </el-alert>

      <!-- 文件上传 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :on-change="handleFileChange"
          :before-upload="beforeUpload"
          accept=".xlsx,.xls"
          :file-list="fileList"
          :limit="1"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 xlsx/xls 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 导入选项 -->
      <el-form :model="importForm" label-width="120px" style="margin-top: 20px">
        <el-form-item v-if="false" label="默认销售组">
          <el-select 
            v-model="importForm.defaultSalesGroupId" 
            placeholder="请选择默认销售组（可选）" 
            style="width: 100%"
            @change="handleSalesGroupChange"
            clearable
          >
            <el-option
              v-for="group in salesGroupOptions"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="!isSalesRole" label="默认销售人员">
          <el-select
            v-model="importForm.defaultSalesId"
            placeholder="请选择默认销售人员（可选）"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="sales in salesOptions"
              :key="sales.id"
              :label="`${sales.name} (${sales.phone})`"
              :value="sales.id"
            />
          </el-select>
        </el-form-item>

        <!-- 重复处理说明 -->
        <el-form-item label="重复处理">
          <el-text type="info">系统将自动跳过已存在的手机号用户，不重复创建</el-text>
        </el-form-item>
      </el-form>

      <!-- 导入结果 -->
      <div v-if="importResult" class="import-result">
        <el-divider content-position="left">导入结果</el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="总记录数">
            {{ importResult.totalCount }}
          </el-descriptions-item>
          <el-descriptions-item label="成功导入">
            <span style="color: #67c23a">{{ importResult.successCount }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="失败记录">
            <span style="color: #f56c6c">{{ importResult.failCount }}</span>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 错误详情 -->
        <div v-if="importResult.errorMessages && importResult.errorMessages.length > 0" class="error-details">
          <el-divider content-position="left">错误详情</el-divider>
          <div class="error-list">
            <div v-for="(error, index) in importResult.errorMessages" :key="index" class="error-item">
              <el-text type="danger">{{ error }}</el-text>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button @click="handleDownloadTemplate">下载模板</el-button>
        <el-button
          type="primary"
          @click="handleUpload"
          :loading="uploading"
          :disabled="!selectedFile || uploading"
        >
          开始导入
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import useUserStore from '@/store/modules/user'
import {
  importStudentsApi as importSalesStudent,
  downloadStudentTemplateApi as downloadTemplate,
  getSalesOptionsApi,
  getSalesGroupOptionsApi
} from '@/api/management/salesStudent'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'refresh'])

// 用户store
const userStore = useUserStore()

// 判断用户是否为销售角色
const isSalesRole = computed(() => {
  const roles = userStore.roles || []
  return roles.some(role =>
    role === 'sales' ||
    role === 'sales_group_leader' ||
    role === 'sales_director' ||
    role === '销售' ||
    role === '销售组长' ||
    role === '销售总监'
  )
})

const visible = ref(false)
const uploading = ref(false)
const uploadRef = ref()
const fileList = ref([])
const selectedFile = ref(null)
const importResult = ref(null)
const salesGroupOptions = ref([])
const salesOptions = ref([])

// 导入表单
const importForm = reactive({
  defaultSalesGroupId: '',
  defaultSalesId: ''
})

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    initDialog()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 初始化对话框
const initDialog = () => {
  fileList.value = []
  selectedFile.value = null
  importResult.value = null
  uploading.value = false
  Object.assign(importForm, {
    defaultSalesGroupId: '',
    defaultSalesId: ''
  })
  // 清空上传组件的文件列表
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  getSalesGroupOptionList()
  // 只有非销售角色才需要获取销售人员选项
  if (!isSalesRole.value) {
    getSalesOptionList()
  }
  console.log('对话框初始化完成，fileList长度:', fileList.value.length)
}

// 获取销售组选项
const getSalesGroupOptionList = async () => {
    const response = await getSalesGroupOptionsApi()
    salesGroupOptions.value = response.data || []
    console.log('销售组选项:', response.data)
}

// 获取销售人员选项
const getSalesOptionList = async (groupId = '') => {
    const response = await getSalesOptionsApi({ groupId })
    salesOptions.value = response.data || []
    console.log('销售人员选项:', response.data)
}

// 销售组变化处理
const handleSalesGroupChange = (groupId) => {
  importForm.defaultSalesId = ''
  // 只有非销售角色才需要获取销售人员选项
  if (!isSalesRole.value) {
    getSalesOptionList(groupId)
  }
}

// 文件变化处理
const handleFileChange = (file, files) => {
  console.log('文件变化:', file, files)
  fileList.value = files
  selectedFile.value = file.raw
}

// 上传前检查
const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}

// 开始上传
const handleUpload = async () => {
  console.log('开始上传，fileList长度:', fileList.value.length)
  if (!selectedFile.value) {
    ElMessage.warning('请选择要上传的文件')
    return
  }

  uploading.value = true
  try {
    const uploadData = {
      // 销售角色用户不传递salesId，让后端自动分配给当前用户
      salesId: isSalesRole.value ? undefined : (importForm.defaultSalesId || undefined)
    }

    const response = await importSalesStudent(selectedFile.value, uploadData)

    if (response.code === 200) {
      ElMessage.success('导入成功')
      importResult.value = response.data
      emit('refresh')
    } else {
      ElMessage.error(response.msg || '导入失败')
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败: ' + (error.message || '未知错误'))
  } finally {
    uploading.value = false
  }
}

// 下载模板
const handleDownloadTemplate = async () => {
  try {
    const response = await downloadTemplate()
    // 创建下载链接 - 修复：直接使用response而不是response.data
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '学生导入模板.xlsx'
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('模板下载失败')
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  fileList.value = []
  selectedFile.value = null
  importResult.value = null
  uploading.value = false
  // 清空上传组件的文件列表
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  console.log('对话框关闭，fileList长度:', fileList.value.length)
}
</script>

<style scoped>
.import-content {
  max-height: 600px;
  overflow-y: auto;
}

.import-tips p {
  margin: 5px 0;
  font-size: 14px;
}

.upload-section {
  margin: 20px 0;
}

.import-result {
  margin-top: 20px;
}

.error-details {
  margin-top: 15px;
}

.error-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #fef0f0;
}

.error-item {
  margin-bottom: 8px;
  padding: 4px 0;
  border-bottom: 1px solid #fde2e2;
}

.error-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.dialog-footer {
  text-align: right;
}
</style>
