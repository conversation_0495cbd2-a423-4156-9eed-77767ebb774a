<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加教师到教学组"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="add-teacher-container">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索教师姓名或手机号"
          clearable
          style="width: 300px"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <div class="search-stats">
          <span>可分配教师：{{ filteredTeachers.length }}人</span>
        </div>
      </div>

      <!-- 教师列表 -->
      <div class="teacher-list">
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="filteredTeachers"
          @selection-change="handleSelectionChange"
          stripe
          style="width: 100%"
          max-height="400px"
        >
          <el-table-column type="selection" width="55" :reserve-selection="true" />
          <el-table-column prop="name" label="姓名" width="100" />
          <el-table-column prop="phone" label="手机号码" width="120" />
          <el-table-column prop="email" label="邮箱" width="180" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.email || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="education" label="学历" width="100">
            <template #default="{ row }">
              <span>{{ row.education || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="subjects" label="教授学科" min-width="150" show-overflow-tooltip>
            <template #default="{ row }">
              <div v-if="row.subjects && row.subjects.length > 0" class="subjects-container">
                <el-tag
                  v-for="subject in row.subjects"
                  :key="subject"
                  size="small"
                  class="subject-tag"
                >
                  {{ subject }}
                </el-tag>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="teachingYears" label="教龄" width="80">
            <template #default="{ row }">
              <span>{{ row.teachingYears ? `${row.teachingYears}年` : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                {{ row.status === 'active' ? '在职' : '离职' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 选中统计 -->
      <div v-if="selectedTeachers.length > 0" class="selection-summary">
        <el-alert
          :title="`已选择 ${selectedTeachers.length} 位教师`"
          type="info"
          :closable="false"
        >
          <template #default>
            <div class="selected-teachers">
              <el-tag
                v-for="teacher in selectedTeachers"
                :key="teacher.id"
                closable
                @close="handleRemoveSelection(teacher)"
              >
                {{ teacher.name }}
              </el-tag>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredTeachers.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无可分配的教师" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :disabled="selectedTeachers.length === 0"
          :loading="submitting"
          @click="handleSubmit"
        >
          添加教师 ({{ selectedTeachers.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { useTeachingGroupStore } from '@/stores/teachingGroup'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  groupId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 使用store
const teachingGroupStore = useTeachingGroupStore()

// 响应式数据
const tableRef = ref()
const searchKeyword = ref('')
const selectedTeachers = ref([])
const submitting = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const availableTeachers = computed(() => teachingGroupStore.availableTeachers)
const loading = computed(() => teachingGroupStore.loading)

const filteredTeachers = computed(() => {
  if (!searchKeyword.value) {
    return availableTeachers.value
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return availableTeachers.value.filter(teacher => 
    teacher.name.toLowerCase().includes(keyword) ||
    teacher.phone.includes(keyword) ||
    (teacher.email && teacher.email.toLowerCase().includes(keyword))
  )
})

// 监听器
watch(() => props.modelValue, (visible) => {
  if (visible) {
    fetchAvailableTeachers()
    resetSelection()
  }
})

// 方法
const fetchAvailableTeachers = async () => {
  await teachingGroupStore.fetchAvailableTeachers()
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleSelectionChange = (selection) => {
  selectedTeachers.value = selection
}

const handleRemoveSelection = (teacher) => {
  const index = selectedTeachers.value.findIndex(t => t.id === teacher.id)
  if (index > -1) {
    selectedTeachers.value.splice(index, 1)
    // 更新表格选中状态
    tableRef.value?.toggleRowSelection(teacher, false)
  }
}

const resetSelection = () => {
  selectedTeachers.value = []
  searchKeyword.value = ''
  tableRef.value?.clearSelection()
}

const handleClose = () => {
  dialogVisible.value = false
  resetSelection()
}

const handleSubmit = async () => {
  if (selectedTeachers.value.length === 0) {
    ElMessage.warning('请选择要添加的教师')
    return
  }

  try {
    submitting.value = true

    const teacherIds = selectedTeachers.value.map(teacher => teacher.id)
    const success = await teachingGroupStore.assignTeachers(props.groupId, teacherIds)

    if (success) {
      ElMessage.success(`成功添加 ${selectedTeachers.value.length} 名教师`)
      emit('success')
      // 重新获取可分配教师列表，移除已添加的教师
      await fetchAvailableTeachers()
      // 清空选择
      resetSelection()
      // 不关闭对话框，允许继续添加
    }
  } catch (error) {
    console.error('添加教师失败:', error)
  } finally {
    submitting.value = false
  }
}

// 生命周期
onMounted(() => {
  if (props.modelValue) {
    fetchAvailableTeachers()
  }
})
</script>

<style lang="scss" scoped>
.add-teacher-container {
  .search-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .search-stats {
      font-size: 14px;
      color: #606266;
    }
  }

  .teacher-list {
    margin-bottom: 16px;

    .subjects-container {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .subject-tag {
        margin: 0;
      }
    }
  }

  .selection-summary {
    margin-bottom: 16px;

    .selected-teachers {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 8px;
    }
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-table) {
  .el-table__header {
    th {
      background-color: #fafafa;
      color: #606266;
      font-weight: 600;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }

  .add-teacher-container {
    .search-bar {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .el-input {
        width: 100% !important;
      }
    }
  }
}
</style>
