<template>
  <el-dialog
    v-model="dialogVisible"
    title="智能自动分配"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="auto-assign-container">
      <el-alert
        title="智能分配说明"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>系统将根据以下策略自动分配未分配的学生：</p>
          <ul>
            <li>负载均衡：优先分配给学生数量较少的销售人员</li>
            <li>地区优先：优先分配给同地区的销售人员</li>
            <li>经验匹配：根据销售经验和学生特点进行匹配</li>
          </ul>
        </template>
      </el-alert>

      <el-form :model="assignForm" :rules="assignRules" ref="formRef" label-width="120px" style="margin-top: 20px;">
        <el-form-item label="分配范围" prop="scope">
          <el-radio-group v-model="assignForm.scope">
            <el-radio value="unassigned">仅未分配学生</el-radio>
            <el-radio value="all">全部学生（重新分配）</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="分配策略" prop="strategies">
          <el-checkbox-group v-model="assignForm.strategies">
            <el-checkbox value="balance">负载均衡</el-checkbox>
            <el-checkbox value="region">地区优先</el-checkbox>
            <el-checkbox value="experience">经验匹配</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="目标销售组">
          <el-select
            v-model="assignForm.targetGroupId"
            placeholder="全部销售组"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="group in salesGroupOptions"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="最大负载">
          <el-input-number
            v-model="assignForm.maxLoad"
            :min="1"
            :max="100"
            placeholder="每个销售最多分配学生数"
            style="width: 100%"
          />
          <div class="form-tip">设置每个销售人员最多可分配的学生数量</div>
        </el-form-item>

        <el-form-item label="分配备注">
          <el-input
            v-model="assignForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入分配备注（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <!-- 预览信息 -->
      <div v-if="previewData" class="preview-section">
        <h4>分配预览</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="待分配学生">{{ previewData.totalStudents }}名</el-descriptions-item>
          <el-descriptions-item label="目标销售人员">{{ previewData.targetSales }}名</el-descriptions-item>
          <el-descriptions-item label="平均分配">{{ previewData.avgAssignment }}名/人</el-descriptions-item>
          <el-descriptions-item label="预计用时">{{ previewData.estimatedTime }}秒</el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handlePreview" :loading="previewing">预览分配</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          开始分配
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  autoAssignStudentsApi,
  getSalesGroupOptionsApi,
  getWorkloadDistributionApi
} from '@/api/management/studentAssignment'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const previewing = ref(false)
const salesGroupOptions = ref([])
const previewData = ref(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const assignForm = reactive({
  scope: 'unassigned',
  strategies: ['balance'],
  targetGroupId: '',
  maxLoad: 20,
  remark: ''
})

// 表单验证规则
const assignRules = {
  scope: [
    { required: true, message: '请选择分配范围', trigger: 'change' }
  ],
  strategies: [
    { required: true, message: '请选择至少一个分配策略', trigger: 'change' }
  ]
}

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    loadOptions()
    resetForm()
  }
})

// 页面初始化
onMounted(() => {
  loadOptions()
})

// 加载选项数据
const loadOptions = async () => {
  try {
    const response = await getSalesGroupOptionsApi()
    salesGroupOptions.value = response.data || []
  } catch (error) {
    console.error('获取销售组选项失败:', error)
  }
}

// 预览分配
const handlePreview = async () => {
  try {
    await formRef.value.validate()
    previewing.value = true
    
    const response = await getWorkloadDistributionApi({
      ...assignForm,
      preview: true
    })
    
    previewData.value = response.data
    ElMessage.success('预览生成成功')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('预览失败')
    }
  } finally {
    previewing.value = false
  }
}

// 提交分配
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    await autoAssignStudentsApi(assignForm)
    ElMessage.success('智能分配完成')
    emit('success')
  } catch (error) {
    if (error !== false) {
      ElMessage.error('智能分配失败')
    }
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(assignForm, {
    scope: 'unassigned',
    strategies: ['balance'],
    targetGroupId: '',
    maxLoad: 20,
    remark: ''
  })
  previewData.value = null
  formRef.value?.clearValidate()
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}
</script>

<style scoped>
.auto-assign-container {
  padding: 10px 0;
}

.preview-section {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.preview-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.dialog-footer {
  text-align: right;
}
</style>
