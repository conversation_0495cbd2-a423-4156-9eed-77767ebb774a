<template>
  <div class="sales-group-management">
    <!-- 搜索表单 -->
    <el-card shadow="never" class="search-card">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="组名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入销售组名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="组长">
          <el-select
            v-model="searchForm.leaderId"
            placeholder="请选择组长"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="leader in leaderOptions"
              :key="leader.id"
              :label="leader.name"
              :value="leader.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="正常" value="0" />
            <el-option label="停用" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleCreate" v-hasPermi="['sales:group:add']">
            <el-icon><Plus /></el-icon>
            新建销售组
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格容器 -->
    <div class="table-container">

      <!-- 销售组列表 -->
      <el-card shadow="never" class="table-card">
        <div class="table-wrapper">
          <el-table
            v-loading="loading"
            :data="salesGroupList"
            :max-height="tableMaxHeight"
            @selection-change="handleSelectionChange"
            stripe
            border
          >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="销售组名称" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="leaderName" label="组长" width="120" />
        <el-table-column prop="memberCount" label="成员数量" width="100" align="center" />
        <el-table-column label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
              {{ row.status === 'active' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              @click="handleView(row)"
              v-hasPermi="['sales:group:query']"
            >
              查看
            </el-button>
            <el-button
              type="primary"
              link
              @click="handleEdit(row)"
              v-hasPermi="['sales:group:edit']"
            >
              编辑
            </el-button>
            <el-button
              type="primary"
              link
              @click="handleManageMembers(row)"
              v-hasPermi="['sales:group:member']"
            >
              成员管理
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(row)"
              v-hasPermi="['sales:group:remove']"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <!-- 固定分页 -->
    <div class="fixed-pagination">
      <el-pagination
        v-model:current-page="searchForm.pageNum"
        v-model:page-size="searchForm.pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSearch"
        @current-change="handleSearch"
      />
    </div>

    <!-- 创建/编辑销售组对话框 -->
    <SalesGroupDialog
      v-model="showDialog"
      :group="selectedGroup"
      @success="handleDialogSuccess"
    />

    <!-- 成员管理对话框 -->
    <MemberManageDialog
      v-model="showMemberDialog"
      :group="selectedGroup"
      @success="handleMemberDialogSuccess"
    />

    <!-- 销售组详情对话框 -->
    <SalesGroupDetailDialog
      v-model="showDetailDialog"
      :group-id="selectedGroupId"
    />
  </div>
</template>

<script setup name='sales-group'>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { 
  getSalesGroupListApi, 
  deleteSalesGroupApi,
  getLeaderOptionsApi
} from '@/api/management/salesGroup'
import { parseTime } from '@/utils/ruoyi'
import SalesGroupDialog from './components/SalesGroupDialog.vue'
import MemberManageDialog from './components/MemberManageDialog.vue'
import SalesGroupDetailDialog from './components/SalesGroupDetailDialog.vue'

// 响应式数据
const loading = ref(false)
const salesGroupList = ref([])
const total = ref(0)
const selectedRows = ref([])
const leaderOptions = ref([])

// 计算表格最大高度
const tableMaxHeight = ref(600)

// 对话框状态
const showDialog = ref(false)
const showMemberDialog = ref(false)
const showDetailDialog = ref(false)
const selectedGroup = ref(null)
const selectedGroupId = ref(null)

// 搜索表单
const searchForm = reactive({
  name: '',
  leaderId: '',
  status: '',
  pageNum: 1,
  pageSize: 20
})

// 计算表格高度
const calculateTableHeight = () => {
  const windowHeight = window.innerHeight
  const usedHeight = 40 + 120 + 80 + 40 // 大概的固定高度
  tableMaxHeight.value = Math.max(400, windowHeight - usedHeight)
}

// 页面初始化
onMounted(() => {
  handleSearch()
  loadLeaderOptions()
  calculateTableHeight()

  // 监听窗口大小变化
  window.addEventListener('resize', calculateTableHeight)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight)
})

// 搜索
const handleSearch = async () => {
  loading.value = true
  try {
    const response = await getSalesGroupListApi(searchForm)
    // 后端返回的是IPage格式，需要从data中获取
    const pageData = response.data || response
    salesGroupList.value = pageData.records || pageData.rows || []
    total.value = pageData.total || 0
  } catch (error) {
    ElMessage.error('获取销售组列表失败')
    console.error('获取销售组列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    leaderId: '',
    status: '',
    pageNum: 1,
    pageSize: 20
  })
  handleSearch()
}

// 加载组长选项
const loadLeaderOptions = async () => {
  try {
    console.log('主页面开始加载组长选项...')
    const response = await getLeaderOptionsApi()
    console.log('主页面组长选项API响应:', response)

    // 处理响应数据
    const data = response.data || response || []
    console.log('主页面处理后的组长选项数据:', data)

    // 确保数据格式正确 - 后端返回的字段是 name (实际是 salesName)
    leaderOptions.value = Array.isArray(data) ? data.map(item => ({
      id: item.id,
      name: item.name || '未知',  // 后端已经设置为 salesName
      phone: item.phone || ''
    })) : []

    console.log('主页面最终的组长选项:', leaderOptions.value)
  } catch (error) {
    console.error('获取组长选项失败:', error)
    leaderOptions.value = []
  }
}

// 创建销售组
const handleCreate = () => {
  selectedGroup.value = null
  showDialog.value = true
}

// 编辑销售组
const handleEdit = (row) => {
  selectedGroup.value = { ...row }
  showDialog.value = true
}

// 查看销售组详情
const handleView = (row) => {
  selectedGroupId.value = row.id
  showDetailDialog.value = true
}

// 成员管理
const handleManageMembers = (row) => {
  selectedGroup.value = { ...row }
  showMemberDialog.value = true
}

// 删除销售组
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除销售组"${row.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteSalesGroupApi(row.id)
    ElMessage.success('删除成功')
    handleSearch()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 对话框成功回调
const handleDialogSuccess = () => {
  showDialog.value = false
  handleSearch()
}

// 成员管理对话框成功回调
const handleMemberDialogSuccess = () => {
  showMemberDialog.value = false
  handleSearch()
}
</script>

<style scoped>
.sales-group-management {
  padding: 20px 20px 0 20px;
  background-color: #f5f5f5;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* 搜索条件样式 */
.search-card {
  margin-bottom: 12px;
  flex-shrink: 0;
}

.search-card .el-form {
  padding: 8px 0;
}

.search-card .el-form-item {
  margin-bottom: 12px;
}

/* 表格容器样式 */
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
  padding-bottom: 80px; /* 为固定分页留出空间 */
}

.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  overflow: hidden;
  min-height: 0;
}

.table-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 20px;
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

.table-wrapper .el-table {
  width: 100% !important;
}

.table-wrapper .el-table .el-table__cell {
  padding: 8px 12px;
}

/* 固定分页样式 */
.fixed-pagination {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 20px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: center;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  align-items: center;
}
</style>
