<template>
  <ul class="workbench-s">
    <li v-for="index in 5" :key="`c_${index}`" class="customer-item">
      <div class="icon"></div>
      <div class="customer-info">
        <div class="customer-bc"></div>
        <div class="customer-bc customer-state"></div>
      </div>
    </li>
  </ul>
</template>

<script lang="ts">
  export default {
    name: 'SkeletonScreen',
  };
</script>

<style scoped>
.workbench-s {
  display: flex;
  flex-direction: column;
  padding: 0 5px;
  overflow: auto;
}

.workbench-s > .customer-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-bottom: 1px solid rgb(220, 223, 230);
}

.workbench-s > .customer-item > .icon {
  width: 40px;
  height: 40px;
  margin-top: 5px;
  background-color: rgb(240, 242, 245);
  border-radius: 4px;
}

.workbench-s > .customer-item > .customer-info {
  display: flex;
  flex-flow: column wrap;
  flex-grow: 1;
  justify-content: space-evenly;
  margin-left: 12px;
  overflow: auto;
  font-size: 14px;
}

.customer-item > .customer-info > .customer-bc {
  width: 100px;
  height: 20px;
  background-color: rgb(240, 242, 245);
  border-radius: 4px;
}

.customer-item > .customer-info > .customer-state{
  width: 130px;
  margin-top: 5px;
}
</style>
