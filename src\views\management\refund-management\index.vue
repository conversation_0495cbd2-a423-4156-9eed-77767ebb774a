<template>
  <div class="refund-mgmt-container">
    <!-- 筛选区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="query" label-width="90px" inline>
        <el-form-item label="订单编号">
          <el-input v-model="query.orderNo" placeholder="请输入订单编号" clearable style="width: 220px" @keyup.enter="onSearch" />
        </el-form-item>
        <el-form-item label="退款单号">
          <el-input v-model="query.refundNo" placeholder="请输入退款单号" clearable style="width: 220px" @keyup.enter="onSearch" />
        </el-form-item>
        <el-form-item label="退款状态">
          <el-select v-model="query.refundStatus" placeholder="请选择退款状态" clearable style="width: 180px">
            <el-option label="待审批" value="待审批" />
            <el-option label="待退款" value="待退款" />
            <el-option label="审批不通过" value="审批不通过" />
            <el-option label="已退款" value="已退款" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请时间">
          <el-date-picker
            v-model="refundTimeRange"
            type="datetimerange"
            value-format="YYYY-MM-DD HH:mm:ss"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            range-separator="-"
            style="width: 360px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="onSearch">
            <el-icon><Search /></el-icon> 搜索
          </el-button>
          <el-button @click="onReset">
            <el-icon><Refresh /></el-icon> 重置
          </el-button>
<!--          <el-button type="success" @click="onOpenExport">-->
<!--            <el-icon><Download /></el-icon> 导出-->
<!--          </el-button>-->
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 列表区域 -->
    <el-card class="table-card" shadow="never">
      <el-table :data="list" v-loading="loading" stripe style="width: 100%">
        <el-table-column prop="productName" label="产品名称" width="200" show-overflow-tooltip />
        <el-table-column prop="refundNo" label="退款单号" width="220" show-overflow-tooltip />
        <el-table-column prop="orderNo" label="原订单编号" width="220" show-overflow-tooltip />
        <el-table-column prop="refundAmountYuan" label="退款金额(元)" width="140" align="center">
          <template #default="{ row }">
            <span class="amount">¥{{ (row.refundAmount / 100).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="refundStatus" label="退款状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="statusTagType(row.refundStatus)">{{ statusText(row.refundStatus) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="studentName" label="学生姓名" min-width="200" show-overflow-tooltip />
        <el-table-column prop="salerName" label="销售人员" min-width="200" show-overflow-tooltip />

        <el-table-column label="总课时" min-width="200" show-overflow-tooltip >
          <template #default="{ row }">
            {{ (Number(row.products?.quantity) + Number(row.products?.bonusHoursQuantity)) || ''}}
          </template>
        </el-table-column>
        <el-table-column prop="salerName" label="购买课时" min-width="200" show-overflow-tooltip >
          <template #default="{ row }">
            {{ row.products?.quantity }}
          </template>
        </el-table-column>
        <el-table-column prop="salerName" label="赠送课时" min-width="200" show-overflow-tooltip >
        <template #default="{ row }">
          {{ row.products?.bonusHoursQuantity }}
        </template>
        </el-table-column>
        <el-table-column prop="salerName" label="消耗课时" min-width="200" show-overflow-tooltip >
          <template #default="{ row }">
            {{ row.courseHoursRefundCalculationResult?.consumedHours }}
          </template>
        </el-table-column>
        <el-table-column prop="salerName" label="退款课时" min-width="200" show-overflow-tooltip >
          <template #default="{ row }">
            {{ row.courseHoursRefundCalculationResult?.refundableHours }}
          </template>
        </el-table-column>

        <el-table-column prop="refundReason" label="退款原因" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="申请时间" width="170">
          <template #default="{ row }">{{ formatDateTime(row.createTime) }}</template>
        </el-table-column>
        <el-table-column prop="refundTime" label="退款完成时间" width="170">
          <template #default="{ row }">{{ formatDateTime(row.refundTime) }}</template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="onDetail(row)">详情</el-button>
            <el-button
              v-if="canApprove(row)"
              link
              type="success"
              @click="onApprove(row, 'approved')"
              v-hasPermi="['refund:records:approve']"
            >审批通过</el-button>
            <el-button
              v-if="canApprove(row)"
              link
              type="danger"
              @click="onApprove(row, 'rejected')"
              v-hasPermi="['refund:records:approve']"
            >审批拒绝</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          :current-page="query.pageNum"
          :page-size="query.pageSize"
          :total="total"
          :page-sizes="[10,20,50,100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onPageChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <RefundDetailDialog v-model="detailVisible" :refund-id="currentId" />

    <!-- 审批对话框 -->
    <RefundApprovalDialog
      v-model="approvalVisible"
      :refund-id="currentId"
      :result="approvalResult"
      @success="refresh"
    />

    <!-- 导出对话框 -->
    <RefundExportDialog v-model="exportVisible" :params="buildExportParams()" />
  </div>
</template>

<script setup lang="ts" name="RefundManagement">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date.js'
import { getRefundRecordList, exportRefundRecords } from '@/api/management/refund-records'
import RefundDetailDialog from './components/RefundDetailDialog.vue'
import RefundApprovalDialog from './components/RefundApprovalDialog.vue'
import RefundExportDialog from './components/RefundExportDialog.vue'

interface RefundRecordResp {
  id: string
  orderId: string
  refundNo: string
  refundType?: string
  refundTypeDesc?: string
  refundAmountYuan?: string
  refundReason?: string
  refundStatus?: string
  refundStatusDesc?: string
  refundTime?: string | number | Date
  createTime?: string | number | Date
}

interface QueryReq {
  pageNum: number
  pageSize: number
  refundNo?: string
  orderNo?: string
  refundType?: string
  refundStatus?: string
  refundTimeStart?: string
  refundTimeEnd?: string
}

const loading = ref(false)
const list = ref<RefundRecordResp[]>([])
const total = ref(0)
const refundTimeRange = ref<[string, string] | []>([])

const query = reactive<QueryReq>({
  pageNum: 1,
  pageSize: 10,
  refundNo: '',
  orderNo: '',
  refundStatus: ''
})

const detailVisible = ref(false)
const approvalVisible = ref(false)
const exportVisible = ref(false)
const currentId = ref<string>('')
const approvalResult = ref<'approved' | 'rejected' | ''>('')

const fetchList = async () => {
  try {
    loading.value = true
    // 时间范围
    if (refundTimeRange.value && refundTimeRange.value.length === 2) {
      query.refundTimeStart = refundTimeRange.value[0]
      query.refundTimeEnd = refundTimeRange.value[1]
    } else {
      query.refundTimeStart = ''
      query.refundTimeEnd = ''
    }
    const res: any = await getRefundRecordList(query)
    if (res.code === 200) {
      list.value = res.rows || []
      total.value = res.total || 0
    } else {
      ElMessage.error(res.msg || '获取退款记录失败')
    }
  } catch (e) {
    console.error(e)
    ElMessage.error('获取退款记录失败')
  } finally {
    loading.value = false
  }
}

const onSearch = () => {
  query.pageNum = 1
  fetchList()
}

const onReset = () => {
  query.pageNum = 1
  query.pageSize = 10
  query.refundNo = ''
  query.orderNo = ''
  query.refundStatus = ''
  refundTimeRange.value = []
  fetchList()
}

const onOpenExport = () => {
  exportVisible.value = true
}

const onSizeChange = (size: number) => {
  query.pageSize = size
  query.pageNum = 1
  fetchList()
}

const onPageChange = (page: number) => {
  query.pageNum = page
  fetchList()
}

const onDetail = (row: RefundRecordResp) => {
  currentId.value = row.id
  detailVisible.value = true
}

const canApprove = (row: RefundRecordResp) => {
  return row.refundStatus === '待审批'
}

const onApprove = (row: RefundRecordResp, result: 'approved' | 'rejected') => {
  currentId.value = row.id
  approvalResult.value = result
  approvalVisible.value = true
}

const statusTagType = (status?: string) => {
  const map: Record<string, string> = {
    '待审批': 'warning',
    '审批不通过': 'danger',
    '待退款': 'primary',
    '已退款': 'success',
    processing: 'warning',
    success: 'success',
    failed: 'danger'
  }
  return (status && map[status]) || 'info'
}

const statusText = (status?: string) => status || '-'

const buildExportParams = () => {
  const p: Record<string, any> = { ...query }
  if (refundTimeRange.value && refundTimeRange.value.length === 2) {
    p.refundTimeStart = refundTimeRange.value[0]
    p.refundTimeEnd = refundTimeRange.value[1]
  }
  return p
}

const refresh = () => {
  fetchList()
}

onMounted(fetchList)
</script>

<style scoped>
.refund-mgmt-container { padding: 16px; }
.search-card { margin-bottom: 16px; }
.table-card { margin-top: 0; }
.pagination { margin-top: 16px; display: flex; justify-content: flex-end; }
.amount { color: #E6A23C; font-weight: 500; }
</style>

