import request from '@/utils/request'

// 查询请求参数接口
export interface CourseQueryRequest {
  // 查询条件
  subject?: string // 学科(英语)
  specification?: string // 课型(单词课、题型课)
  type?: string // 类型(学习课、复习课)
  courseType?: string // 性质(正式课、试听课)
  courseStatuses?: string[] // 课程状态
  teacherId?: string // 老师ID
  teacherName?: string // 老师姓名
  studentId?: string // 学生ID
  studentName?: string // 学生姓名
  grade?: number // 年级
  startDate?: string // 上课开始日期
  endDate?: string // 上课结束日期
  antiForgetCompleted?: boolean // 抗遗忘是否完成
  exceptionTypes?: string[] // 异常类型
  hasCourseConsumption?: boolean // 是否产生课消

  // 分页参数
  pageNum?: number
  pageSize?: number
  orderBy?: string
  orderDirection?: string
}

// 抗遗忘状态接口
export interface ReviewStatus {
  status: string // 完成状态
  color: string // 显示颜色
}

export interface AntiForgetStatus {
  d2Status: ReviewStatus
  d4Status: ReviewStatus
  d7Status: ReviewStatus
  d14Status: ReviewStatus
  d21Status: ReviewStatus
}

// 查询响应接口
export interface CourseQueryResponse {
  id: string // 课程ID
  courseDate: string // 日期
  subject: string // 学科
  specification: string // 课型
  type: string // 类型
  courseType: string // 性质
  courseStatus: string // 课程状态
  teacherId: string // 老师ID
  teacherName: string // 老师姓名
  studentId: string // 学生ID
  studentName: string // 学生姓名
  grade?: number // 年级
  scheduledStartTime: string // 排课上课时间
  scheduledEndTime: string // 排课下课时间
  scheduledDuration: number // 排课时长(分钟)
  actualStartTime?: string // 实际上课时间
  actualEndTime?: string // 实际下课时间
  actualDuration?: number // 实际时长(分钟)
  antiForgetStatus: AntiForgetStatus // 抗遗忘完成情况
  exceptionTypes: string[] // 异常类型
  courseConsumption: number // 课消(小时)
  useSystem?: boolean // 是否使用系统（用于消课功能）

  // 为了兼容调课组件，添加这些字段（在处理函数中映射）
  duration?: number // 时长(分钟) - 从 scheduledDuration 映射
  startTime?: string // 开始时间 - 从 scheduledStartTime 映射
  endTime?: string // 结束时间 - 从 scheduledEndTime 映射

  // 操作权限
  canStart: boolean // 可以上课
  canCancel: boolean // 可以停课
  canReschedule: boolean // 可以调课
  canDownloadMaterial: boolean // 可以下载资料
  canViewReport: boolean // 可以查看报告
  canEnter: boolean // 可以进入课堂
}

// 统计响应接口
export interface StatisticsResponse {
  totalStudents: number // 学生数
  totalCourses: number // 排课数
  completedCourses: number // 完课数
  cancelledCourses: number // 停课数
  rescheduledCourses: number // 调课数
  antiForgetProgress: string // 抗遗忘进度(已完成/总数)
  totalCourseConsumption: number // 课消数(小时)
}

// 查询选项接口
export interface QueryOptions {
  subjects: Array<{ label: string; value: string }>
  specifications: Array<{ label: string; value: string }>
  types: Array<{ label: string; value: string }>
  courseTypes: Array<{ label: string; value: string }>
  courseStatuses: Array<{ label: string; value: string }>
  exceptionTypes: Array<{ label: string; value: string }>
  teachers: Array<{ label: string; value: string }>
  students: Array<{ label: string; value: string }>
}

/**
 * 分页查询课程信息
 */
export function queryCourses(data: CourseQueryRequest) {
  return request({
    url: '/course-query/list',
    method: 'post',
    data
  })
}

/**
 * 获取课程统计信息
 */
export function getStatistics(data: CourseQueryRequest) {
  return request({
    url: '/course-query/statistics',
    method: 'post',
    data
  })
}

/**
 * 课程操作 - 上课
 */
export function startCourse(courseId: string) {
  return request({
    url: `/course-query/start/${courseId}`,
    method: 'post'
  })
}

/**
 * 课程操作 - 停课
 */
export function cancelCourse(courseId: string, reason?: string) {
  return request({
    url: `/course-query/cancel/${courseId}`,
    method: 'post',
    data: reason
  })
}

/**
 * 课程操作 - 调课
 */
export function rescheduleCourse(courseId: string, rescheduleData: any) {
  return request({
    url: `/course-query/reschedule/${courseId}`,
    method: 'post',
    data: rescheduleData
  })
}

/**
 * 课程操作 - 下载资料
 */
export function downloadMaterial(courseId: string, materialType: string = 'handout') {
  return request({
    url: `/course-query/download/${courseId}`,
    method: 'get',
    params: { materialType }
  })
}

/**
 * 课程操作 - 查看报告
 */
export function viewReport(courseId: string) {
  return request({
    url: `/course-query/report/${courseId}`,
    method: 'get'
  })
}

/**
 * 获取查询条件的选项数据
 */
export function getQueryOptions() {
  return request({
    url: '/course-query/options',
    method: 'get'
  })
}

/**
 * 导出课程数据
 */
export function exportCourses(data: CourseQueryRequest) {
  return request({
    url: '/course-query/export',
    method: 'post',
    data,
    responseType: 'blob',
    // 设置3分钟超时时间
    timeout: 180000
  })
}

export default {
  queryCourses,
  getStatistics,
  startCourse,
  cancelCourse,
  rescheduleCourse,
  downloadMaterial,
  viewReport,
  getQueryOptions,
  exportCourses
}
