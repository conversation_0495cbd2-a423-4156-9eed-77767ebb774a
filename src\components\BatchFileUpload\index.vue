<template>
  <div class="batch-file-upload">
    <!-- 已上传文件列表 -->
    <div v-if="uploadedFiles.length > 0" class="uploaded-files">
      <h4>已上传文件 ({{ uploadedFiles.length }}/{{ limit }})</h4>
      <div class="file-list">
        <div 
          v-for="(file, index) in uploadedFiles" 
          :key="index" 
          class="file-item uploaded"
        >
          <div class="file-info">
            <el-icon class="file-icon">
              <Document v-if="isDocument(file.name)" />
              <VideoPlay v-else />
            </el-icon>
            <span class="file-name">{{ file.name }}</span>
            <el-tag size="small" type="success">已上传</el-tag>
          </div>
          <div class="file-actions">
            <el-link 
              :href="file.url" 
              target="_blank" 
              type="primary"
              :underline="false"
              size="small"
            >
              查看
            </el-link>
            <el-button 
              type="danger" 
              size="small" 
              text 
              @click="removeUploadedFile(index)"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 待上传文件列表 -->
    <div v-if="pendingFiles.length > 0" class="pending-files">
      <h4>待上传文件 ({{ pendingFiles.length }})</h4>
      <div class="file-list">
        <div 
          v-for="(file, index) in pendingFiles" 
          :key="file.uid" 
          class="file-item pending"
        >
          <div class="file-info">
            <el-icon class="file-icon">
              <Document v-if="isDocument(file.name)" />
              <VideoPlay v-else />
            </el-icon>
            <span class="file-name">{{ file.name }}</span>
            <el-tag 
              size="small" 
              :type="getStatusTagType(file.status)"
            >
              {{ getStatusText(file.status) }}
            </el-tag>
          </div>
          <div class="file-actions">
            <el-progress 
              v-if="file.status === 'uploading'" 
              :percentage="file.progress" 
              :width="60"
              type="circle"
              :stroke-width="4"
            />
            <el-button 
              v-if="file.status === 'ready' || file.status === 'error'"
              type="danger" 
              size="small" 
              text 
              @click="removePendingFile(index)"
            >
              移除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件选择和上传控制 -->
    <div class="upload-controls">
      <el-upload
        ref="uploadRef"
        :auto-upload="false"
        :show-file-list="false"
        :accept="acceptTypes"
        :multiple="true"
        :on-change="handleFileSelect"
        :before-upload="() => false"
        class="file-selector"
      >
        <el-button 
          type="primary" 
          :disabled="uploadedFiles.length + pendingFiles.length >= limit"
        >
          <el-icon><Plus /></el-icon>
          选择文件
        </el-button>
      </el-upload>

      <el-space v-if="pendingFiles.length > 0">
        <el-button 
          type="success" 
          @click="uploadAllFiles"
          :loading="isUploading"
          :disabled="!hasReadyFiles"
        >
          <el-icon><Upload /></el-icon>
          上传所有文件 ({{ readyFilesCount }})
        </el-button>
        <el-button 
          type="warning" 
          @click="clearPendingFiles"
          :disabled="isUploading"
        >
          清空待上传
        </el-button>
      </el-space>
    </div>

    <!-- 提示信息 -->
    <div v-if="showTip" class="upload-tip">
      <div>{{ tipText }}</div>
      <div v-if="fileSize">单个文件大小不超过 <b>{{ fileSize }}MB</b></div>
      <div v-if="fileTypes && fileTypes.length">
        支持格式：<b>{{ fileTypes.join(', ') }}</b>
      </div>
      <div>最多上传 <b>{{ limit }}</b> 个文件</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Plus, Upload, Document, VideoPlay } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getToken } from '@/utils/auth'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  // 上传地址
  uploadUrl: {
    type: String,
    required: true
  },
  // 文件数量限制
  limit: {
    type: Number,
    default: 5
  },
  // 文件大小限制(MB)
  fileSize: {
    type: Number,
    default: 200
  },
  // 支持的文件类型
  fileTypes: {
    type: Array,
    default: () => []
  },
  // 提示文本
  tipText: {
    type: String,
    default: '请选择文件上传'
  },
  // 是否显示提示
  showTip: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'upload-progress'])

// 响应式数据
const uploadRef = ref()
const uploadedFiles = ref([]) // 已上传成功的文件
const pendingFiles = ref([])  // 待上传的文件
const isUploading = ref(false)

// 计算属性
const acceptTypes = computed(() => {
  if (!props.fileTypes || props.fileTypes.length === 0) return ''
  return props.fileTypes.map(type => `.${type}`).join(',')
})

const hasReadyFiles = computed(() => {
  return pendingFiles.value.some(file => file.status === 'ready')
})

const readyFilesCount = computed(() => {
  return pendingFiles.value.filter(file => file.status === 'ready').length
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal, oldVal) => {
  console.log('BatchFileUpload modelValue 变化:', newVal, '旧值:', oldVal);

  if (newVal && Array.isArray(newVal) && newVal.length > 0) {
    uploadedFiles.value = newVal.map((url, index) => ({
      name: getFileNameFromUrl(url) || `文件${index + 1}`,
      url: url
    }))
    console.log('更新后的 uploadedFiles:', uploadedFiles.value);
  } else {
    uploadedFiles.value = []
  }
}, { immediate: true, deep: true })

// 方法
const isDocument = (fileName) => {
  if (!fileName) return true
  const ext = fileName.split('.').pop()?.toLowerCase()
  return ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'].includes(ext)
}

const getFileNameFromUrl = (url) => {
  if (!url) return null
  const parts = url.split('/')
  return parts[parts.length - 1]
}

const getStatusTagType = (status) => {
  const statusMap = {
    'ready': 'info',
    'uploading': 'warning', 
    'success': 'success',
    'error': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'ready': '待上传',
    'uploading': '上传中',
    'success': '成功',
    'error': '失败'
  }
  return statusMap[status] || '未知'
}

const validateFile = (file) => {
  // 检查文件类型
  if (props.fileTypes && props.fileTypes.length > 0) {
    const fileExtension = file.name.split('.').pop().toLowerCase()
    if (!props.fileTypes.includes(fileExtension)) {
      return `不支持的文件格式，请上传 ${props.fileTypes.join(', ')} 格式的文件`
    }
  }

  // 检查文件大小
  if (props.fileSize && file.size > props.fileSize * 1024 * 1024) {
    return `文件大小不能超过 ${props.fileSize}MB`
  }

  return null
}

const handleFileSelect = (file, fileList) => {
  // 检查总数量限制
  const totalFiles = uploadedFiles.value.length + pendingFiles.value.length
  if (totalFiles >= props.limit) {
    ElMessage.error(`最多只能上传 ${props.limit} 个文件`)
    return
  }

  // 验证文件
  const error = validateFile(file)
  if (error) {
    ElMessage.error(error)
    return
  }

  // 添加到待上传列表
  const pendingFile = {
    uid: file.uid,
    name: file.name,
    size: file.size,
    raw: file.raw,
    status: 'ready',
    progress: 0
  }

  pendingFiles.value.push(pendingFile)
}

const uploadSingleFile = async (file) => {
  file.status = 'uploading'
  file.progress = 0

  try {
    const formData = new FormData()
    formData.append('file', file.raw)

    const xhr = new XMLHttpRequest()
    
    // 上传进度监听
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        file.progress = Math.round((event.loaded / event.total) * 100)
      }
    })

    // 完成监听
    const uploadPromise = new Promise((resolve, reject) => {
      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText)
            resolve(response)
          } catch (e) {
            reject(new Error('响应格式错误'))
          }
        } else {
          reject(new Error(`HTTP ${xhr.status}`))
        }
      })

      xhr.addEventListener('error', () => {
        reject(new Error('网络错误'))
      })
    })

    // 发送请求
    xhr.open('POST', props.uploadUrl)
    xhr.setRequestHeader('Authorization', `Bearer ${getToken()}`)
    xhr.send(formData)

    const response = await uploadPromise

    if (response.success && response.data && response.data.fileUrl) {
      file.status = 'success'
      file.progress = 100
      
      // 添加到已上传列表
      uploadedFiles.value.push({
        name: response.data.fileName || file.name,
        url: response.data.fileUrl
      })

      return response.data.fileUrl
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    file.status = 'error'
    file.progress = 0
    throw error
  }
}

const uploadAllFiles = async () => {
  const readyFiles = pendingFiles.value.filter(file => file.status === 'ready')
  if (readyFiles.length === 0) {
    ElMessage.warning('没有待上传的文件')
    return
  }

  isUploading.value = true
  let successCount = 0
  let errorCount = 0

  try {
    // 逐个上传文件
    for (const file of readyFiles) {
      try {
        await uploadSingleFile(file)
        successCount++
      } catch (error) {
        console.error(`文件 ${file.name} 上传失败:`, error)
        errorCount++
      }
    }

    // 移除成功上传的文件
    pendingFiles.value = pendingFiles.value.filter(file => file.status !== 'success')

    // 更新 v-model
    const urls = uploadedFiles.value.map(file => file.url)
    emit('update:modelValue', urls)
    emit('change', urls)

    // 显示结果
    if (successCount > 0 && errorCount === 0) {
      ElMessage.success(`成功上传 ${successCount} 个文件`)
    } else if (successCount > 0 && errorCount > 0) {
      ElMessage.warning(`成功上传 ${successCount} 个文件，${errorCount} 个文件上传失败`)
    } else {
      ElMessage.error('所有文件上传失败')
    }
  } finally {
    isUploading.value = false
  }
}

const removePendingFile = (index) => {
  pendingFiles.value.splice(index, 1)
}

const removeUploadedFile = (index) => {
  uploadedFiles.value.splice(index, 1)
  
  // 更新 v-model
  const urls = uploadedFiles.value.map(file => file.url)
  emit('update:modelValue', urls)
  emit('change', urls)
}

const clearPendingFiles = () => {
  pendingFiles.value = []
}

// 清空所有文件
const clearAllFiles = () => {
  uploadedFiles.value = []
  pendingFiles.value = []
  emit('update:modelValue', [])
  emit('change', [])
}

// 检查是否有待上传文件
const hasPendingFiles = () => {
  return pendingFiles.value.length > 0
}

// 获取待上传文件数量
const getPendingFilesCount = () => {
  return pendingFiles.value.length
}

// 强制刷新已上传文件列表
const refreshUploadedFiles = (files) => {
  console.log('强制刷新文件列表:', files);
  if (files && Array.isArray(files) && files.length > 0) {
    uploadedFiles.value = files.map((url, index) => ({
      name: getFileNameFromUrl(url) || `文件${index + 1}`,
      url: url
    }))
  } else {
    uploadedFiles.value = []
  }
}

// 暴露方法
defineExpose({
  clearAllFiles,
  clearPendingFiles,
  uploadAllFiles,
  hasPendingFiles,
  getPendingFilesCount,
  refreshUploadedFiles
})
</script>

<style scoped>
.batch-file-upload {
  width: 100%;
}

.uploaded-files,
.pending-files {
  margin-bottom: 16px;
}

.uploaded-files h4,
.pending-files h4 {
  margin: 0 0 12px 0;
  color: #409eff;
  font-size: 14px;
  font-weight: 600;
}

.file-list {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f5f7fa;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item.uploaded {
  background-color: #f0f9ff;
}

.file-item.pending {
  background-color: #fefefe;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.file-icon {
  font-size: 16px;
  color: #409eff;
}

.file-name {
  font-size: 14px;
  color: #303133;
  flex: 1;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.upload-tip b {
  color: #f56c6c;
  font-weight: 600;
}
</style>
