<template>
  <el-dialog
    v-model="visible"
    title="设置学生可上课时间"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="time-slot-editor-dialog">
      <div class="dialog-content">
        <!-- 参考排课页面的简单时间段编辑 -->
        <div class="weekly-schedules">
          <div class="schedule-header">
            <span class="header-title">可上课时间</span>
            <el-button
              type="primary"
              size="small"
              :icon="Plus"
              @click="addTimeSlot"
            >
              添加时间段
            </el-button>
          </div>

          <div
            v-for="(slot, index) in localTimeSlots"
            :key="index"
            class="schedule-item"
          >
            <el-row :gutter="8" align="middle" class="schedule-row">
              <el-col :span="6">
                <el-select
                  v-model="slot.weekday"
                  placeholder="星期"
                  size="small"
                >
                  <el-option
                    v-for="day in weekDays"
                    :key="day.value"
                    :label="day.label"
                    :value="day.value"
                  />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-time-picker
                  v-model="slot.startTime"
                  format="HH:mm"
                  value-format="HH:mm"
                  placeholder="开始时间"
                  size="small"
                />
              </el-col>
              <el-col :span="6">
                <el-time-picker
                  v-model="slot.endTime"
                  format="HH:mm"
                  value-format="HH:mm"
                  placeholder="结束时间"
                  size="small"
                />
              </el-col>
              <el-col :span="6" class="time-actions">
                <el-button
                  type="danger"
                  size="small"
                  :icon="Delete"
                  @click="removeTimeSlot(index)"
                  circle
                  v-if="localTimeSlots.length > 1"
                />
              </el-col>
            </el-row>
          </div>

          <div v-if="localTimeSlots.length === 0" class="empty-state">
            <el-empty description="暂无时间段设置">
              <el-button type="primary" @click="addTimeSlot">
                添加第一个时间段
              </el-button>
            </el-empty>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">
          确定 ({{ localTimeSlots.length }}个时间段)
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  timeSlots: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'confirm'])

// 响应式数据
const visible = ref(false)
const localTimeSlots = ref([])

// 星期选项
const weekDays = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 7 }
]

// 工具函数
// 标准化时间格式，将 "HH:mm:ss" 转换为 "HH:mm"
function normalizeTimeFormat(timeStr) {
  if (!timeStr || typeof timeStr !== 'string') {
    return timeStr
  }

  const parts = timeStr.split(':')
  if (parts.length >= 2) {
    // 只取小时和分钟部分，忽略秒
    const hours = parts[0].padStart(2, '0')
    const minutes = parts[1].padStart(2, '0')
    return `${hours}:${minutes}`
  }

  return timeStr
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  visible.value = newValue
  if (newValue) {
    // 转换时间段格式并标准化时间格式
    if (props.timeSlots.length > 0) {
      localTimeSlots.value = props.timeSlots.map(slot => ({
        id: slot.id || `slot_${Date.now()}_${Math.random()}`,
        weekday: slot.weekday,
        startTime: normalizeTimeFormat(slot.startTime),
        endTime: normalizeTimeFormat(slot.endTime)
      }))
    } else {
      // 如果没有时间段，添加一个默认的
      localTimeSlots.value = [{
        id: `slot_${Date.now()}`,
        weekday: 1, // 周一
        startTime: '09:00',
        endTime: '10:00'
      }]
    }
  }
})

watch(visible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 方法
function addTimeSlot() {
  localTimeSlots.value.push({
    id: `slot_${Date.now()}_${Math.random()}`,
    weekday: 1, // 默认周一
    startTime: '09:00',
    endTime: '10:00'
  })
}

function removeTimeSlot(index) {
  localTimeSlots.value.splice(index, 1)
}

function handleConfirm() {
  // 转换为API需要的格式
  const apiTimeSlots = localTimeSlots.value.map(slot => ({
    weekday: slot.weekday,
    startTime: slot.startTime,
    endTime: slot.endTime
  }))
  
  emit('confirm', apiTimeSlots)
  visible.value = false
}

function handleCancel() {
  visible.value = false
}

function handleClose() {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.time-slot-editor-dialog {
  .dialog-content {
    min-height: 300px;
  }
}

.weekly-schedules {
  .schedule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 6px;

    .header-title {
      font-weight: 600;
      color: #303133;
    }
  }

  .schedule-item {
    margin-bottom: 12px;
    padding: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    background-color: #fff;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
    }

    .schedule-row {
      .time-actions {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #909399;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
