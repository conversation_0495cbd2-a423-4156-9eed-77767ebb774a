<template>
  <el-dialog
    :model-value="modelValue"
    :title="isEdit ? '编辑销售人员' : '新建销售人员'"
    width="600px"
    @update:model-value="$emit('update:modelValue', $event)"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="nickName">
            <el-input v-model="form.nickName" placeholder="请输入姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phonenumber">
            <el-input v-model="form.phonenumber" placeholder="请输入手机号" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="sex">
            <el-select v-model="form.sex" placeholder="请选择性别" style="width: 100%">
              <el-option label="男" value="0" />
              <el-option label="女" value="1" />
              <el-option label="未知" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio label="active">正常</el-radio>
              <el-radio label="inactive">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="销售组" prop="groupId">
        <el-select v-model="form.groupId" placeholder="请选择销售组（可选）" style="width: 100%" clearable>
          <el-option
            v-for="group in groupOptions"
            :key="group.id"
            :label="group.name"
            :value="group.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { createSalesStaffApi, updateSalesStaffApi } from '@/api/management/salesStaff'
import { getSalesGroupListApi } from '@/api/management/salesGroup'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  staff: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const groupOptions = ref([])
const loadingOptions = ref(false)

// 角色ID常量
// 102: 销售（固定角色）

// 表单数据
const form = reactive({
  userId: '',
  nickName: '',
  phonenumber: '',
  email: '',
  sex: '0',
  status: 'active',
  // 移除 roleIds 字段，由后端自动分配销售角色
  groupId: '',
  remark: ''
})

// 表单验证规则
const rules = {
  nickName: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  phonenumber: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 计算属性
const isEdit = computed(() => !!props.staff)

// 监听对话框打开
watch(() => props.modelValue, (val) => {
  if (val) {
    loadGroupOptions()
    if (props.staff) {
      // 编辑模式，填充表单数据
      Object.assign(form, {
        userId: props.staff.userId || props.staff.id, // 兼容不同的ID字段
        nickName: props.staff.salesName || props.staff.nickName, // 优先使用salesName
        phonenumber: props.staff.phone || props.staff.phonenumber, // 优先使用phone
        email: props.staff.email,
        sex: props.staff.sex || '0',
        status: props.staff.status || 'active',
        // 移除 roleIds 字段，角色由后端管理
        groupId: props.staff.groupId,
        remark: props.staff.remark
      })
    } else {
      // 新建模式，重置表单
      resetForm()
    }
  }
})

// 加载销售组选项
const loadGroupOptions = async () => {
  // 防止重复调用
  if (loadingOptions.value) {
    return
  }

  try {
    loadingOptions.value = true
    const response = await getSalesGroupListApi({ pageNum: 1, pageSize: 1000 })
    // 兼容不同的响应格式
    const pageData = response.data || response
    groupOptions.value = pageData.records || pageData.rows || []
  } catch (error) {
    console.error('获取销售组选项失败:', error)
    // 设置空数组，避免死循环
    groupOptions.value = []
    // 不显示错误消息，避免干扰用户体验
  } finally {
    loadingOptions.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    userId: '',
    nickName: '',
    phonenumber: '',
    email: '',
    sex: '0',
    status: 'active',
    roleIds: 102, // 默认为销售角色
    groupId: '',
    remark: ''
  })
  formRef.value?.clearValidate()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    // 转换字段名以匹配后端DTO
    const submitData = {
      salesName: form.nickName,  // 前端nickName -> 后端salesName
      phone: form.phonenumber,   // 前端phonenumber -> 后端phone
      email: form.email,
      sex: form.sex,             // 添加性别字段
      groupId: form.groupId
    }

    if (isEdit.value) {
      // 更新时需要ID字段，使用UpdateReq格式
      // 注意：后端期望的id是sales_id（即userId），不是sale_profile的主键
      submitData.id = form.userId  // 这里的userId实际上是sales_id
      submitData.status = form.status || 'active'
      // 移除角色相关处理，角色由后端管理

      console.log('更新销售人员数据:', submitData) // 调试日志
      await updateSalesStaffApi(submitData)
      ElMessage.success('更新成功')
    } else {
      // 创建时使用CreateReq格式
      submitData.password = form.password || '654321'
      // 移除角色相关处理，由后端自动分配销售角色
      await createSalesStaffApi(submitData)
      ElMessage.success('创建成功')
    }

    emit('success')
  } catch (error) {
    if (error !== false) { // 表单验证失败时error为false
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error('提交失败:', error)
    }
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
  resetForm()
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
