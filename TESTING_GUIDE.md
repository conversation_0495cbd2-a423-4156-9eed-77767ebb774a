# 产品配置列表订单核销功能测试指南

## 测试环境准备

### 1. 权限配置
确保测试用户具有以下权限：
- `order:writeoff:apply` - 订单核销申请权限
- `management:products:view` - 产品查看权限

### 2. 测试数据准备
需要准备以下测试数据：

#### 产品数据
- 至少一个状态为"上架"的产品
- 产品应该有明确的适用年级设置
- 产品应该有客户类型设置（通用/仅新客可用/仅老客可用）

#### 学生数据
准备不同类型的学生数据：
1. **完整信息的学生**：有姓名、手机号、年级、家长姓名、家长手机号
2. **家长信息不完整的学生**：缺少家长姓名或家长手机号
3. **年级不匹配的学生**：年级不在产品适用年级范围内
4. **不同客户类型的学生**：有购买历史和无购买历史的学生

## 测试步骤

### 1. 基础功能测试

#### 1.1 访问产品配置列表
1. 登录系统
2. 导航到产品配置列表页面
3. 验证页面正常加载，显示产品列表

#### 1.2 验证订单核销按钮显示
1. 检查上架状态的产品是否显示"订单核销"按钮
2. 检查下架状态的产品是否不显示"订单核销"按钮
3. 验证按钮权限控制是否正常

### 2. 学生选择功能测试

#### 2.1 打开学生选择对话框
1. 点击某个产品的"订单核销"按钮
2. 验证学生选择对话框是否正常打开
3. 检查产品信息是否正确显示

#### 2.2 学生搜索功能
1. 在学生姓名搜索框中输入学生姓名
2. 点击搜索按钮，验证搜索结果
3. 在手机号搜索框中输入手机号
4. 验证搜索结果是否正确
5. 测试重置功能是否正常

#### 2.3 学生适用性验证
1. 查看学生列表中的适用状态显示
2. 验证以下场景：
   - 年级匹配且家长信息完整的学生：应显示绿色"适用"标签，选择按钮可用
   - 年级不匹配的学生：应显示红色年级标签和"年级不适用"提示，选择按钮禁用
   - 家长信息不完整的学生：应显示"家长信息不完整"提示，选择按钮禁用

#### 2.4 分页功能
1. 如果学生数量超过10个，验证分页是否正常工作
2. 测试页面大小切换功能
3. 测试页码跳转功能

### 3. 订单核销流程测试

#### 3.1 选择学生
1. 选择一个适用的学生
2. 点击"选择"按钮
3. 验证学生选择对话框是否关闭
4. 验证订单核销对话框是否打开

#### 3.2 验证预填信息
1. 检查学生信息是否正确预填
2. 检查产品是否自动选中
3. 验证产品信息显示是否正确

#### 3.3 完成核销流程
1. 填写订单号
2. 选择核销平台
3. 上传订单截图
4. 填写备注信息
5. 提交核销申请
6. 验证提交是否成功

### 4. 异常情况测试

#### 4.1 权限测试
1. 使用没有核销权限的用户登录
2. 验证订单核销按钮是否不显示

#### 4.2 数据异常测试
1. 测试产品信息缺失的情况
2. 测试学生信息缺失的情况
3. 测试网络异常的情况

#### 4.3 边界条件测试
1. 测试搜索空字符串
2. 测试搜索不存在的学生
3. 测试选择已经被删除的学生

## 预期结果

### 正常流程
1. 用户能够顺利从产品列表进入订单核销
2. 系统正确验证学生适用性
3. 不适用的学生被正确标识和禁用
4. 核销流程能够正常完成

### 错误处理
1. 权限不足时正确隐藏功能
2. 数据异常时显示友好的错误提示
3. 网络异常时能够正确处理

## 测试检查点

- [ ] 订单核销按钮正确显示
- [ ] 学生选择对话框正常打开
- [ ] 产品信息正确显示
- [ ] 学生搜索功能正常
- [ ] 学生适用性验证正确
- [ ] 不适用学生正确标识
- [ ] 分页功能正常
- [ ] 学生选择功能正常
- [ ] 产品自动选中
- [ ] 核销流程完整
- [ ] 权限控制正确
- [ ] 错误处理友好

## 注意事项

1. 测试时请确保有足够的测试数据
2. 注意观察控制台是否有错误信息
3. 测试不同浏览器的兼容性
4. 测试移动端的响应式效果
