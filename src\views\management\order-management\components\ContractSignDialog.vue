<template>
  <el-dialog 
    title="合同签署" 
    v-model="dialogVisible" 
    width="600px" 
    append-to-body
    @close="handleClose"
  >
    <div class="contract-sign-content">
      <div class="order-info">
        <h4>订单信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ orderInfo.no }}</el-descriptions-item>
          <el-descriptions-item label="订单标题">{{ orderInfo.body }}</el-descriptions-item>
          <el-descriptions-item label="学生姓名">{{ orderInfo.studentName }}</el-descriptions-item>
          <el-descriptions-item label="销售员">{{ orderInfo.salerName }}</el-descriptions-item>
          <el-descriptions-item label="订单金额">¥{{ (orderInfo.totalAmt / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusType(orderInfo.orderStatus)" size="small">
              {{ orderInfo.orderStatus }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="sign-status" v-if="signInfo">
        <h4>签署状态</h4>
        <el-alert
          :title="getSignStatusText(signInfo.status)"
          :type="getSignAlertType(signInfo.status)"
          :closable="false"
          show-icon
        />
        
        <div class="sign-actions" v-if="signInfo.status === 'pending'">
          <el-button type="primary" @click="copySignUrl" v-if="signInfo.signUrl">
            <el-icon><CopyDocument /></el-icon>
            复制签署链接
          </el-button>
<!--          <el-button type="success" @click="sendWechatNotification">-->
<!--            <el-icon><Message /></el-icon>-->
<!--            发送微信通知-->
<!--          </el-button>-->
<!--          <el-button type="info" @click="refreshSignStatus">-->
<!--            <el-icon><Refresh /></el-icon>-->
<!--            刷新状态-->
<!--          </el-button>-->
        </div>

        <div class="sign-url" v-if="signInfo.signUrl && signInfo.status === 'pending'">
          <h5>签署链接：</h5>
          <el-input 
            v-model="signInfo.signUrl" 
            readonly 
            type="textarea" 
            :rows="3"
            class="sign-url-input"
          />
          <p class="sign-tip">
            <el-icon><InfoFilled /></el-icon>
            请将此链接发送给客户进行合同签署，链接有效期为180天
          </p>
        </div>
      </div>

      <div class="loading-container" v-if="loading">
        <el-skeleton :rows="3" animated />
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
        <el-button 
          type="primary" 
          @click="handleInitiateSign" 
          :loading="initiating"
          v-if="!signInfo || signInfo.status === 'failed'"
          v-hasPermi="['order:manager:sign']"
        >
          {{ signInfo?.status === 'failed' ? '重新发起签署' : '发起签署' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="ContractSignDialog">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { CopyDocument, Message, Refresh, InfoFilled } from '@element-plus/icons-vue'
import { initiateContractSignApi } from '@/api/management/order'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderInfo: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const loading = ref(false)
const initiating = ref(false)
const signInfo = ref(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const getStatusType = (status) => {
  const statusMap = {
    '未支付': 'warning',
    '已支付': 'success',
    '已取消': 'danger',
    '已退款': 'info'
  }
  return statusMap[status] || 'info'
}

const getSignStatusText = (status) => {
  const statusMap = {
    'pending': '等待客户签署',
    'signed': '签署完成',
    'failed': '签署失败',
    'expired': '签署链接已过期'
  }
  return statusMap[status] || '未知状态'
}

const getSignAlertType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'signed': 'success',
    'failed': 'error',
    'expired': 'error'
  }
  return typeMap[status] || 'info'
}

const handleInitiateSign = async () => {
  try {
    initiating.value = true
    const signUrlDto = await initiateContractSignApi(props.orderInfo.id)
    
    // 模拟获取签署信息（实际应该调用API获取）
    signInfo.value = {
      status: 'pending',
      signUrl: signUrlDto.data.shortUrl,
      createTime: new Date().toISOString()
    }
    
    ElMessage.success('合同签署发起成功')
    emit('success')
  } catch (error) {
    ElMessage.error('发起合同签署失败')
  } finally {
    initiating.value = false
  }
}

const copySignUrl = async () => {
  try {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(signInfo.value.signUrl)
      ElMessage.success('签署链接已复制到剪贴板')
    } else {
      const textArea = document.createElement('textarea')
      textArea.value = signInfo.value.signUrl
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('签署链接已复制到剪贴板')
    }
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const sendWechatNotification = () => {
  ElMessage.success('微信通知已发送')
}

const refreshSignStatus = () => {
  ElMessage.info('签署状态已刷新')
  // 这里应该调用API刷新签署状态
}

const handleClose = () => {
  signInfo.value = null
  dialogVisible.value = false
}

// 监听对话框打开
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.orderInfo.id) {
    // 这里应该调用API获取签署状态
    // 暂时模拟数据
    loading.value = true
    setTimeout(() => {
      loading.value = false
      // 根据订单的签署状态设置初始数据
      if (props.orderInfo.signStatus === '已签署') {
        signInfo.value = {
          status: 'signed',
          signUrl: null,
          createTime: new Date().toISOString()
        }
      } else if (props.orderInfo.signStatus === '签署中') {
        signInfo.value = {
          status: 'pending',
          signUrl: 'https://esign.cn/sign/xxxxxxxx',
          createTime: new Date().toISOString()
        }
      }
    }, 1000)
  }
})
</script>

<style lang="scss" scoped>
.contract-sign-content {
  .order-info {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-weight: 600;
    }
  }
  
  .sign-status {
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-weight: 600;
    }
    
    .sign-actions {
      margin: 16px 0;
      
      .el-button {
        margin-right: 12px;
      }
    }
    
    .sign-url {
      margin-top: 20px;
      
      h5 {
        margin: 0 0 8px 0;
        color: #606266;
        font-weight: 500;
      }
      
      .sign-url-input {
        margin-bottom: 8px;
      }
      
      .sign-tip {
        margin: 0;
        color: #909399;
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
  
  .loading-container {
    padding: 20px 0;
  }
}

.dialog-footer {
  .el-button {
    margin-left: 12px;
  }
}
</style>
