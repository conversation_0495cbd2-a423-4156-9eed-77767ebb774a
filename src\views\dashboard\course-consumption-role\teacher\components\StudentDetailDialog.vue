<template>
  <el-dialog
    v-model="visible"
    title="学生课消详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-if="studentData" class="student-detail">
      <!-- 学生基本信息 -->
      <div class="student-info">
        <h3>{{ studentData.studentName }}</h3>
        <p class="student-phone">手机号: {{ studentData.studentPhone }}</p>
      </div>

      <!-- 课消统计卡片 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-value">{{ formatNumber(studentData.totalConsumption) }}</div>
            <div class="stat-label">总课消(课时)</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-value">{{ formatNumber(studentData.weeklyConsumption) }}</div>
            <div class="stat-label">周课消(课时)</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-value">{{ formatNumber(studentData.monthlyConsumption) }}</div>
            <div class="stat-label">月课消(课时)</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-value">{{ studentData.consumptionCount }}</div>
            <div class="stat-label">课消次数</div>
          </div>
        </el-col>
      </el-row>

      <!-- 学科分布图表 -->
      <div class="subject-chart-section">
        <h4>学科课消分布</h4>
        <div ref="subjectChart" class="subject-chart"></div>
      </div>

      <!-- 详细信息 -->
      <div class="detail-info">
        <h4>详细信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="最后课消时间">
            {{ formatDateTime(studentData.lastConsumptionTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="课消次数">
            {{ studentData.consumptionCount }}次
          </el-descriptions-item>
          <el-descriptions-item label="总课消课时">
            {{ formatNumber(studentData.totalConsumption) }}课时
          </el-descriptions-item>
          <el-descriptions-item label="平均每次课消">
            {{ formatNumber(avgConsumptionPerTime) }}课时
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 学科详情表格 -->
      <div class="subject-detail">
        <h4>学科课消详情</h4>
        <el-table :data="studentData.subjectConsumptions" border stripe>
          <el-table-column prop="subject" label="学科" width="120" />
          <el-table-column prop="consumption" label="课消课时" width="120">
            <template #default="{ row }">
              {{ formatNumber(row.consumption) }}课时
            </template>
          </el-table-column>
          <el-table-column prop="count" label="课消次数" width="120">
            <template #default="{ row }">
              {{ row.count }}次
            </template>
          </el-table-column>
          <el-table-column prop="percentage" label="占比" width="120">
            <template #default="{ row }">
              {{ formatNumber(row.percentage) }}%
            </template>
          </el-table-column>
          <el-table-column label="平均每次课消" min-width="120">
            <template #default="{ row }">
              {{ formatNumber(row.consumption / row.count) }}课时
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import * as echarts from 'echarts'
import { formatDateTime } from '@/utils/date'

export default {
  name: 'StudentDetailDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    studentData: {
      type: Object,
      default: null
    }
  },
  emits: ['update:modelValue', 'close'],
  data() {
    return {
      subjectChart: null
    }
  },
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    },
    avgConsumptionPerTime() {
      if (!this.studentData || !this.studentData.consumptionCount || this.studentData.consumptionCount === 0) {
        return 0
      }
      return this.studentData.totalConsumption / this.studentData.consumptionCount
    }
  },
  watch: {
    visible(val) {
      if (val && this.studentData) {
        this.$nextTick(() => {
          this.initSubjectChart()
        })
      }
    },
    studentData: {
      handler() {
        if (this.visible && this.studentData) {
          this.$nextTick(() => {
            this.initSubjectChart()
          })
        }
      },
      deep: true
    }
  },
  beforeUnmount() {
    if (this.subjectChart) {
      this.subjectChart.dispose()
    }
  },
  methods: {
    handleClose() {
      this.visible = false
      this.$emit('close')
    },

    initSubjectChart() {
      if (!this.$refs.subjectChart || !this.studentData?.subjectConsumptions) return

      if (this.subjectChart) {
        this.subjectChart.dispose()
      }

      this.subjectChart = echarts.init(this.$refs.subjectChart)

      const data = this.studentData.subjectConsumptions.map(item => ({
        name: item.subject,
        value: item.consumption
      }))

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c}课时 ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '学科课消',
            type: 'pie',
            radius: '50%',
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      this.subjectChart.setOption(option)
    },

    formatNumber(value) {
      if (value === null || value === undefined) return '0'
      const num = Number(value)
      if (isNaN(num)) return value
      return num % 1 === 0 ? num.toString() : num.toFixed(2)
    },

    formatDateTime(dateTime) {
      return formatDateTime(dateTime)
    }
  }
}
</script>

<style scoped>
.student-detail {
  padding: 20px 0;
}

.student-info {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.student-info h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #303133;
}

.student-phone {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.subject-chart-section {
  margin-bottom: 30px;
}

.subject-chart-section h4,
.detail-info h4,
.subject-detail h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.subject-chart {
  height: 300px;
  width: 100%;
}

.detail-info {
  margin-bottom: 30px;
}

.subject-detail {
  margin-bottom: 20px;
}
</style>
