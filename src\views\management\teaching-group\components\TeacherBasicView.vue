<template>
  <div class="teacher-basic-view">
    <el-table
      :data="teachers"
      @selection-change="handleSelectionChange"
      stripe
      style="width: 100%"
      max-height="500px"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="姓名" width="100" fixed="left" />
      <el-table-column prop="nickname" label="昵称" width="100">
        <template #default="{ row }">
          <span>{{ row.nickname || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="gender" label="性别" width="80">
        <template #default="{ row }">
          <el-tag :type="getGenderTagType(row.gender)" size="small">
            {{ getGenderText(row.gender) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="phone" label="手机号码" width="120" />
      <el-table-column prop="email" label="邮箱" width="180" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.email || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="education" label="最高学历" width="100">
        <template #default="{ row }">
          <span>{{ row.education || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="graduateSchool" label="毕业院校" width="150" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.graduateSchool || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="major" label="毕业专业" width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.major || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="isNormalUniversity" label="是否师范类" width="100">
        <template #default="{ row }">
          <el-tag :type="row.isNormalUniversity ? 'success' : 'info'" size="small">
            {{ row.isNormalUniversity ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="teachingCertificateLevel" label="教资级别" width="120">
        <template #default="{ row }">
          <span>{{ row.teachingCertificateLevel || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="subjects" label="教授学科" width="150" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.subjects && row.subjects.length > 0" class="subjects-container">
            <el-tag
              v-for="subject in row.subjects"
              :key="subject"
              size="small"
              class="subject-tag"
            >
              {{ subject }}
            </el-tag>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="englishQualification" label="英语资质" width="120">
        <template #default="{ row }">
          <span>{{ row.englishQualification || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="mandarinQualification" label="普通话资质" width="120">
        <template #default="{ row }">
          <span>{{ row.mandarinQualification || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="teachingYears" label="教龄" width="80">
        <template #default="{ row }">
          <span>{{ row.teachingYears ? `${row.teachingYears}年` : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="80">
        <template #default="{ row }">
          <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
            {{ row.status === 'active' ? '在职' : '离职' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleViewDetail(row)">
            查看详情
          </el-button>
          <el-button type="success" link @click="handleViewSchedule(row)">
            查看课表
          </el-button>
          <el-button type="warning" link @click="handleAssignStudents(row)">
            分配学生
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 空状态 -->
    <div v-if="teachers.length === 0" class="empty-state">
      <el-empty description="暂无教师数据" />
    </div>
  </div>
</template>

<script setup>
import { defineEmits } from 'vue'

// Props
const props = defineProps({
  teachers: {
    type: Array,
    default: () => []
  },
  selectedTeachers: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['selection-change', 'view-detail', 'view-schedule', 'assign-students'])

// 方法
const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

const handleViewDetail = (teacher) => {
  emit('view-detail', teacher)
}

const handleViewSchedule = (teacher) => {
  // 触发事件，让父组件处理课表查看
  emit('view-schedule', teacher)
}

const handleAssignStudents = (teacher) => {
  emit('assign-students', teacher)
}

const getGenderText = (gender) => {
  const genderMap = {
    0: '男',
    1: '女',
    2: '未知'
  }
  return genderMap[gender] || '未知'
}

const getGenderTagType = (gender) => {
  const typeMap = {
    0: 'primary',
    1: 'success',
    2: 'info'
  }
  return typeMap[gender] || 'info'
}
</script>

<style lang="scss" scoped>
.teacher-basic-view {
  .subjects-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .subject-tag {
      margin: 0;
    }
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
  }

  :deep(.el-table) {
    .el-table__header {
      th {
        background-color: #fafafa;
        color: #606266;
        font-weight: 600;
      }
    }

    .el-table__body {
      tr:hover {
        background-color: #f5f7fa;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .teacher-basic-view {
    :deep(.el-table) {
      font-size: 12px;

      .el-table-column--selection {
        width: 40px !important;
      }

      .el-button {
        padding: 4px 8px;
        font-size: 12px;
      }
    }
  }
}
</style>
