export declare interface Textbook{
    id: string;
    name: string;
    description: string;
    typeName: string;
    type:string;
    cover:string;
    wordList: string;
    // tags: string[];
    statWordCnt: Number;
    statUnitCnt: Number;
    grade:number;
    gradeName:string;
    publisher:string;
    required:string;
    semester:number;
    semesterName:string;
    stage:string;
}

export declare interface TextbookItem{
    id: Number;
    name: string;
}

export declare interface BookTree{
  nodeId:string;
  nodeType:number;
  wordId:string;
  nodeName:string;
  wordNum:number;
  displayOrder:number;
  checked:boolean;
  textbookType:string;
  parentNodeId:string;
  textbookId:string;
  childNodeList?:BookTree[];
  learnStatus:boolean;
  mistakes:string;
}

export declare interface TextbookTreeParam{
  nodeId?:string;
  nodeType:number;
  searchType?:string;
  studentId?:string;
  types?:string[];
  tags?:string[];
}

export declare interface LastWordInfo{
  textbookId?:string;
  unitItemId?:string;
  wordItemId?:string;
  word?:string;
  textbookTreeVo?:BookTree;
}

import request from '@/utils/request';

// 添加教材
export function addOrUpdateTextbook(data: any) {
  return request({
    url: '/word/textbook/addOrUpdate',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 添加教材
export function removeTextbook(data: any) {
  return request({
    url: '/word/textbook/remove',
    method: 'post',
    data: data
  });
}


// 获取教材列表
export function getTextbookList(params?: { pageNum: number; pageSize: number }) {
  return request({
    url: '/word/textbook/page',
    method: 'get',
    params: params
  });
}

// 获取教材列表
export function listAllApi() {
  return request({
    url: '/word/textbook/list-all',
    method: 'get',
  });
}

// 获取教材列表
export async function getTextbookTree(params : TextbookTreeParam) {
  return request({
    url: '/word/textbook/tree',
    method: 'get',
    params: params
  });
}

// 获取教材列表
export async function getLastTextbookId(studentId: string,textbookId: string) {
  return request({
    url: `/student/progress/word/${studentId}`,
    method: 'get',
    params: {
      textbookId:textbookId
    }
  });
}

// 教材下载讲义请求参数
export declare interface TextbookDownloadReq {
  textbookId: string;
  downloadType: 'whole' | 'range' | 'selected';
  startWordIndex?: number;
  endWordIndex?: number;
  textbookItemIds?: string[];
}

// 教材下载音频请求参数
export declare interface TextbookDownloadAudioReq {
  textbookId: string;
  downloadType: 'whole' | 'range' | 'selected';
  startWordIndex?: number;
  endWordIndex?: number;
  textbookItemIds?: string[];
}

// 教材下载练习请求参数
export declare interface TextbookDownloadExerciseReq {
  textbookId: string;
  downloadType: 'whole' | 'range' | 'selected';
  startWordIndex?: number;
  endWordIndex?: number;
  textbookItemIds?: string[];
}

// 教材下载讲义响应
export declare interface TextbookDownloadResp {
  body: string;
  headers: { [key: string]: string[] };
}

// 教材下载音频响应
export declare interface TextbookDownloadAudioResp {
  downloadUrl: string;
}

// 教材下载练习响应
export declare interface TextbookDownloadExerciseResp {
  body: string;
  headers: { [key: string]: string[] };
}

/**
 * 下载教材讲义
 * @param data 下载请求参数
 * @returns 下载响应
 */
export function downloadTextbookHandout(data: TextbookDownloadReq) {
  return request({
    url: '/word/textbook/download-handout',
    method: 'post',
    data: data
  });
}

/**
 * 下载教材音频
 * @param data 下载请求参数
 * @returns 下载响应
 */
export function downloadTextbookAudio(data: TextbookDownloadAudioReq) {
  return request({
    url: '/word/textbook/download-audio',
    method: 'post',
    data: data,
    // 音频下载可能需要较长时间，设置10分钟超时
    timeout: 600000
  });
}

/**
 * 下载教材练习
 * @param data 下载请求参数
 * @returns 下载响应
 */
export function downloadTextbookExercise(data: TextbookDownloadExerciseReq) {
  return request({
    url: '/word/textbook/download-exercise',
    method: 'post',
    data: data
  });
}

