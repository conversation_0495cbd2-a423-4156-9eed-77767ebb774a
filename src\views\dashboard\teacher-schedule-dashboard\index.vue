<template>
  <div class="teacher-schedule-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>老师可排课时间看板</h2>
      <p class="page-description">查看指定时间范围内各教学组和老师的可排课课次统计</p>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="queryForm" inline size="default">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
            @change="onDateRangeChange"
            style="width: 280px"
          />
        </el-form-item>

        <el-form-item label="快捷选择">
          <el-button-group>
            <el-button
              v-for="option in quickDateOptions"
              :key="option.value"
              :type="selectedQuickDate === option.value ? 'primary' : 'default'"
              size="default"
              @click="selectQuickDate(option.value)"
            >
              {{ option.label }}
            </el-button>
          </el-button-group>
        </el-form-item>

        <el-form-item label="教学组">
          <el-select
            v-model="queryForm.groupIds"
            placeholder="全部教学组"
            multiple
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="group in teachingGroups"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="loadDashboard" :loading="loading">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 总体统计卡片 -->
    <div class="stats-cards" v-if="dashboardData">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ dashboardData.overallStats.totalTeachers }}</div>
              <div class="stat-label">总教师数</div>
            </div>
            <el-icon class="stat-icon teacher-icon"><User /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ dashboardData.overallStats.totalAvailableSlots }}</div>
              <div class="stat-label">总可排课课次</div>
            </div>
            <el-icon class="stat-icon slots-icon"><Clock /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ dashboardData.overallStats.averageSlotsPerTeacher?.toFixed(1) || 0 }}</div>
              <div class="stat-label">平均每位教师课次</div>
            </div>
            <el-icon class="stat-icon average-icon"><TrendCharts /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ dashboardData.overallStats.totalDays }}</div>
              <div class="stat-label">查询天数</div>
            </div>
            <el-icon class="stat-icon days-icon"><Calendar /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 每日统计图表 -->
    <el-card class="chart-card" v-if="dashboardData && dashboardData.dailyStats.length > 0">
      <template #header>
        <div class="card-header">
          <span>每日可排课课次统计</span>
          <el-button type="text" @click="exportChart">导出图表</el-button>
        </div>
      </template>
      <div ref="dailyChart" class="chart-container"></div>
    </el-card>

    <!-- 教学组汇总对比图表 -->
    <el-card class="chart-card" v-if="dashboardData && dashboardData.groupStats.length > 0">
      <template #header>
        <div class="card-header">
          <span>教学组课次汇总对比</span>
          <el-button type="text" @click="exportGroupSummaryChart">导出教学组图表</el-button>
        </div>
      </template>
      <div ref="groupSummaryChart" class="chart-container group-summary-chart"></div>
    </el-card>

    <!-- 每日详细统计表格 -->
    <el-card class="table-card" v-if="dashboardData">
      <template #header>
        <div class="card-header">
          <span>每日详细统计</span>
          <el-button v-if="false" type="text" @click="exportTable">导出数据</el-button>
        </div>
      </template>
      
      <el-table
        :data="dashboardData.dailyStats"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'date', order: 'ascending' }"
        type="expand"
      >
        <el-table-column type="expand">
          <template #default="{ row }">
            <div class="expand-content">
              <h4>{{ row.date }} 各教学组统计</h4>

              <!-- 当天教学组柱状图 -->
              <div class="day-group-chart-container" v-if="row.groupStats && row.groupStats.length > 0">
                <div
                  :ref="el => setDayGroupChartRef(el, row.date)"
                  class="day-group-chart"
                ></div>
              </div>



              <el-row :gutter="8" v-if="row.groupStats && row.groupStats.length > 0">
                <el-col
                  :span="4"
                  v-for="groupStat in row.groupStats"
                  :key="groupStat.groupId"
                  style="margin-bottom: 8px"
                >
                  <div class="group-mini-card">
                    <div class="group-mini-header">
                      <span class="group-name">{{ groupStat.groupName }}</span>
                    </div>
                    <div class="group-mini-stats">
                      <div class="mini-stat">
                        <span class="mini-label">课次:</span>
                        <span class="mini-value">{{ groupStat.availableSlots }}</span>
                      </div>
                      <div class="mini-stat">
                        <span class="mini-label">教师:</span>
                        <span class="mini-value">{{ groupStat.availableTeachers }}人</span>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <div v-else class="no-group-data">
                <span>暂无教学组数据</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="日期" width="120" sortable>
          <template #default="{ row }">
            <div>{{ row.date }}</div>
            <div class="weekday-tag">{{ row.weekdayName }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="totalAvailableSlots" label="总可排课课次" width="140" sortable>
          <template #default="{ row }">
            <el-tag :type="getSlotsTagType(row.totalAvailableSlots)" size="default">
              {{ row.totalAvailableSlots }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="availableTeachers" label="有课时教师数" width="140" sortable>
          <template #default="{ row }">
            <span class="teacher-count">{{ row.availableTeachers }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="averageSlotsPerAvailableTeacher" label="平均课次" width="120" sortable>
          <template #default="{ row }">
            <span class="average-slots">{{ row.averageSlotsPerAvailableTeacher?.toFixed(1) || 0 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="课次分布" min-width="150">
          <template #default="{ row }">
            <div class="slots-distribution">
              <div class="distribution-bar">
                <div
                  class="distribution-fill"
                  :style="{ width: getDistributionWidth(row.totalAvailableSlots) }"
                ></div>
              </div>
              <span class="distribution-text">{{ row.totalAvailableSlots }} / {{ maxDailySlots }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="false" label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewDayDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 教学组统计 -->
    <el-card class="group-stats-card" v-if="dashboardData && dashboardData.groupStats.length > 0">
      <template #header>
        <div class="card-header">
          <span>教学组统计</span>
          <el-button v-if="false" type="text" @click="exportGroupStats">导出教学组数据</el-button>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col
          :span="8"
          v-for="group in dashboardData.groupStats"
          :key="group.groupId"
          style="margin-bottom: 20px"
        >
          <el-card class="group-card" shadow="hover">
            <div class="group-header">
              <h4>{{ group.groupName }}</h4>
              <el-tag type="info" size="small">{{ group.teacherCount }}位教师</el-tag>
            </div>

            <div class="group-stats">
              <div class="stat-row">
                <span class="stat-label">总可排课课次:</span>
                <span class="stat-value primary">{{ group.totalAvailableSlots }}</span>
              </div>
              <div class="stat-row">
                <span class="stat-label">平均每位教师:</span>
                <span class="stat-value success">{{ group.averageSlotsPerTeacher }}</span>
              </div>
            </div>

            <!-- 教学组柱状图 -->
            <div class="group-chart-container">
              <div
                :ref="el => setGroupChartRef(el, group.groupId)"
                class="group-chart"
              ></div>
            </div>

            <!-- 暂时隐藏功能入口 -->
            <!--
            <div class="group-actions">
              <el-button type="text" size="small" @click="viewGroupDetail(group)">
                查看教师详情
              </el-button>
              <el-button type="text" size="small" @click="viewGroupTrend(group)">
                查看趋势
              </el-button>
            </div>
            -->
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 空状态 -->
    <el-empty
      v-if="!loading && !dashboardData"
      description="请选择时间范围查询数据"
      :image-size="120"
    />

    <!-- 日详情对话框 -->
    <DayDetailDialog
      v-model="dayDetailVisible"
      :date="selectedDate"
      :group-ids="queryForm.groupIds"
      @refresh="loadDashboard"
    />

    <!-- 教学组教师详情对话框 -->
    <GroupTeacherDetailDialog
      v-model="groupTeacherDetailVisible"
      :group="selectedGroup"
      :date-range="{ startDate: queryForm.startDate, endDate: queryForm.endDate }"
      @edit-teacher="handleEditTeacher"
    />

    <!-- 教学组趋势分析对话框 -->
    <GroupTrendDialog
      v-model="groupTrendVisible"
      :group="selectedGroup"
      :date-range="{ startDate: queryForm.startDate, endDate: queryForm.endDate }"
    />
  </div>
</template>

<script setup name="TeacherScheduleDashboard">
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
// import { Search, Refresh, User, Clock } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import DayDetailDialog from './components/DayDetailDialog.vue'
import GroupTeacherDetailDialog from './components/GroupTeacherDetailDialog.vue'
import GroupTrendDialog from './components/GroupTrendDialog.vue'
import { teacherScheduleDashboardApi } from '@/api/management/teacher-schedule-dashboard'
import { teachingGroupApi } from '@/api/management/teachingGroup'

// 响应式数据
const loading = ref(false)
const dashboardData = ref(null)
const dateRange = ref([])
const selectedQuickDate = ref('')
const teachingGroups = ref([])
const dayDetailVisible = ref(false)
const selectedDate = ref('')
const groupTeacherDetailVisible = ref(false)
const groupTrendVisible = ref(false)
const selectedGroup = ref(null)

// 图表实例
const dailyChart = ref(null)
const groupSummaryChart = ref(null)
let chartInstance = null
let groupSummaryChartInstance = null
const groupChartRefs = ref(new Map()) // 存储教学组图表DOM引用
const groupChartInstances = ref(new Map()) // 存储教学组图表实例

const dayGroupChartRefs = ref(new Map()) // 存储展开当天教学组图表DOM引用
const dayGroupChartInstances = ref(new Map()) // 存储展开当天教学组图表实例

// 查询表单
const queryForm = reactive({
  startDate: '',
  endDate: '',
  groupIds: []
})

// 快捷日期选项
const quickDateOptions = ref([
  { label: '今天', value: 'today' },
  { label: '明天', value: 'tomorrow' },
  { label: '本周', value: 'this_week' },
  { label: '下周', value: 'next_week' },
  { label: '本月', value: 'this_month' },
  { label: '下月', value: 'next_month' }
])

// 计算属性
const maxDailySlots = computed(() => {
  if (!dashboardData.value || !dashboardData.value.dailyStats.length) return 0
  return Math.max(...dashboardData.value.dailyStats.map(day => day.totalAvailableSlots))
})

// 禁用日期函数
const disabledDate = (time) => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  
  // 不能选择今天之前的日期
  if (time.getTime() < today.getTime()) {
    return true
  }
  
  // 如果已选择开始日期，限制结束日期不能超过31天
  if (dateRange.value && dateRange.value[0]) {
    const startDate = new Date(dateRange.value[0])
    const maxEndDate = new Date(startDate.getTime() + 31 * 24 * 60 * 60 * 1000)
    return time.getTime() > maxEndDate.getTime()
  }
  
  return false
}

// 方法
const loadTeachingGroups = async () => {
  try {
    const response = await teachingGroupApi.getTeachingGroups({ pageNum: 1, pageSize: 1000 })
    teachingGroups.value = response.data?.records || []
  } catch (error) {
    console.error('加载教学组失败:', error)
  }
}

const selectQuickDate = async (quickDateType) => {
  try {
    selectedQuickDate.value = quickDateType
    const response = await teacherScheduleDashboardApi.getQuickDateRange(quickDateType)
    const { startDate, endDate } = response.data

    dateRange.value = [startDate, endDate]
    queryForm.startDate = startDate
    queryForm.endDate = endDate

    // 自动查询
    await loadDashboard()
  } catch (error) {
    ElMessage.error('获取快捷日期失败: ' + error.message)
  }
}

const onDateRangeChange = (dates) => {
  selectedQuickDate.value = ''
  if (dates && dates.length === 2) {
    queryForm.startDate = dates[0]
    queryForm.endDate = dates[1]
  } else {
    queryForm.startDate = ''
    queryForm.endDate = ''
  }
}

const loadDashboard = async () => {
  if (!queryForm.startDate || !queryForm.endDate) {
    ElMessage.warning('请选择查询时间范围')
    return
  }

  loading.value = true
  try {
    const response = await teacherScheduleDashboardApi.getDashboard(queryForm)
    dashboardData.value = response.data

    // 渲染图表
    await nextTick()
    renderChart()
    renderGroupSummaryChart()
    renderGroupCharts()
    // renderDayGroupCharts() 现在在DOM元素创建时自动调用

    ElMessage.success('数据加载成功')
  } catch (error) {
    ElMessage.error('加载数据失败: ' + error.message)
    dashboardData.value = null
  } finally {
    loading.value = false
  }
}



const resetQuery = () => {
  dateRange.value = []
  selectedQuickDate.value = ''
  queryForm.startDate = ''
  queryForm.endDate = ''
  queryForm.groupIds = []
  dashboardData.value = null

  // 清理主图表
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }

  // 清理教学组汇总图表
  if (groupSummaryChartInstance) {
    groupSummaryChartInstance.dispose()
    groupSummaryChartInstance = null
  }

  // 清理教学组图表
  groupChartInstances.value.forEach(instance => {
    instance.dispose()
  })
  groupChartInstances.value.clear()
  groupChartRefs.value.clear()



  // 清理当天教学组图表
  dayGroupChartInstances.value.forEach(instance => {
    instance.dispose()
  })
  dayGroupChartInstances.value.clear()
  dayGroupChartRefs.value.clear()
}

const renderChart = () => {
  if (!dailyChart.value || !dashboardData.value) return
  
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  chartInstance = echarts.init(dailyChart.value)
  
  const option = {
    title: {
      text: '每日可排课课次趋势',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const data = params[0]
        const dayData = dashboardData.value.dailyStats[data.dataIndex]
        return `
          <div>
            <div><strong>${dayData.date} ${dayData.weekdayName}</strong></div>
            <div>可排课课次: ${data.value}</div>
            <div>有课时教师: ${dayData.availableTeachers}人</div>
            <div>平均课次: ${dayData.averageSlotsPerAvailableTeacher?.toFixed(1) || 0}</div>
          </div>
        `
      }
    },
    xAxis: {
      type: 'category',
      data: dashboardData.value.dailyStats.map(day => `${day.date.slice(5)}\n${day.weekdayName}`),
      axisLabel: {
        interval: 0,
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      name: '课次',
      nameTextStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '可排课课次',
        type: 'bar',
        data: dashboardData.value.dailyStats.map(day => day.totalAvailableSlots),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#409EFF' },
            { offset: 1, color: '#79bbff' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: '#337ecc'
          }
        }
      }
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  }
  
  chartInstance.setOption(option)
  
  // 点击事件
//   chartInstance.on('click', (params) => {
//     const dayData = dashboardData.value.dailyStats[params.dataIndex]
//     viewDayDetail(dayData)
//   })
}

const getSlotsTagType = (slots) => {
  if (slots >= 20) return 'success'
  if (slots >= 10) return 'warning'
  if (slots >= 5) return 'info'
  return 'danger'
}

const getDistributionWidth = (slots) => {
  if (maxDailySlots.value === 0) return '0%'
  return `${(slots / maxDailySlots.value) * 100}%`
}

const viewDayDetail = (dayData) => {
  selectedDate.value = dayData.date
  dayDetailVisible.value = true
}

const exportChart = () => {
  if (!chartInstance) {
    ElMessage.warning('暂无图表数据')
    return
  }
  
  const url = chartInstance.getDataURL({
    type: 'png',
    pixelRatio: 2,
    backgroundColor: '#fff'
  })
  
  const link = document.createElement('a')
  link.href = url
  link.download = `老师可排课时间统计_${queryForm.startDate}_${queryForm.endDate}.png`
  link.click()
}

const exportTable = () => {
  if (!dashboardData.value) {
    ElMessage.warning('暂无数据')
    return
  }

  // 这里可以实现Excel导出功能
  ElMessage.info('导出功能开发中...')
}

const exportGroupStats = () => {
  if (!dashboardData.value || !dashboardData.value.groupStats.length) {
    ElMessage.warning('暂无教学组数据')
    return
  }

  // 这里可以实现教学组数据导出功能
  ElMessage.info('教学组数据导出功能开发中...')
}

const exportGroupSummaryChart = () => {
  if (!groupSummaryChartInstance) {
    ElMessage.warning('暂无教学组汇总图表数据')
    return
  }

  const url = groupSummaryChartInstance.getDataURL({
    type: 'png',
    pixelRatio: 2,
    backgroundColor: '#fff'
  })

  const link = document.createElement('a')
  link.href = url
  link.download = `教学组课次汇总对比_${queryForm.startDate}_${queryForm.endDate}.png`
  link.click()
}

const viewGroupDetail = (group) => {
  if (!queryForm.startDate || !queryForm.endDate) {
    ElMessage.warning('请先选择查询时间范围')
    return
  }

  selectedGroup.value = group
  groupTeacherDetailVisible.value = true
}

const viewGroupTrend = (group) => {
  if (!queryForm.startDate || !queryForm.endDate) {
    ElMessage.warning('请先选择查询时间范围')
    return
  }

  selectedGroup.value = group
  groupTrendVisible.value = true
}

const handleEditTeacher = (teacher) => {
  ElMessage.info(`编辑教师 ${teacher.name} 的信息`)
  // 这里可以打开教师编辑对话框或跳转到编辑页面
}

// 渲染教学组汇总对比图表
const renderGroupSummaryChart = () => {
  if (!groupSummaryChart.value || !dashboardData.value || !dashboardData.value.groupStats.length) return

  if (groupSummaryChartInstance) {
    groupSummaryChartInstance.dispose()
  }

  groupSummaryChartInstance = echarts.init(groupSummaryChart.value)

  // 准备数据
  const groupNames = dashboardData.value.groupStats.map(group => group.groupName)
  const groupSlots = dashboardData.value.groupStats.map(group => group.totalAvailableSlots)
  const teacherCounts = dashboardData.value.groupStats.map(group => group.teacherCount)

  // 生成颜色数组
  const colors = [
    '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',
    '#79bbff', '#95d475', '#eebe77', '#f78989', '#a6a9ad'
  ]

  const option = {
    title: {
      text: '教学组课次汇总对比',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params) => {
        const dataIndex = params[0].dataIndex
        const groupName = groupNames[dataIndex]
        const slots = groupSlots[dataIndex]
        const teachers = teacherCounts[dataIndex]
        const avgSlots = teachers > 0 ? (slots / teachers).toFixed(1) : 0

        return `
          <div>
            <div><strong>${groupName}</strong></div>
            <div>总可排课课次: ${slots}</div>
            <div>教师数量: ${teachers}人</div>
            <div>平均每位教师: ${avgSlots}课次</div>
          </div>
        `
      }
    },
    xAxis: {
      type: 'category',
      data: groupNames,
      axisLabel: {
        interval: 0,
        fontSize: 12,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '课次',
      nameTextStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '可排课课次',
        type: 'bar',
        data: groupSlots,
        itemStyle: {
          color: (params) => {
            return colors[params.dataIndex % colors.length]
          }
        },
        barWidth: '60%',
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    }
  }

  groupSummaryChartInstance.setOption(option)

  // 点击事件
//   groupSummaryChartInstance.on('click', (params) => {
//     const group = dashboardData.value.groupStats[params.dataIndex]
//     viewGroupDetail(group)
//   })
}

// 设置教学组图表DOM引用
const setGroupChartRef = (el, groupId) => {
  if (el) {
    groupChartRefs.value.set(groupId, el)
  }
}

// 设置当天教学组图表DOM引用
const setDayGroupChartRef = (el, date) => {
  if (el) {
    dayGroupChartRefs.value.set(date, el)
    // 当DOM元素创建后，立即渲染图表
    nextTick(() => {
      renderSingleDayGroupChart(date)
    })
  }
}

// 渲染单个日期的教学组图表
const renderSingleDayGroupChart = (date) => {
  if (!dashboardData.value || !dashboardData.value.dailyStats) {
    return
  }

  const dayData = dashboardData.value.dailyStats.find(day => day.date === date)
  if (!dayData) {
    return
  }

  if (!dayData.groupStats || dayData.groupStats.length === 0) {
    return
  }

  const chartEl = dayGroupChartRefs.value.get(date)
  if (!chartEl) {
    return
  }

  // 销毁已存在的图表实例
  const existingInstance = dayGroupChartInstances.value.get(date)
  if (existingInstance) {
    existingInstance.dispose()
  }

  // 创建新的图表实例
  const chartInstance = echarts.init(chartEl)
  dayGroupChartInstances.value.set(date, chartInstance)

  // 准备图表数据
  const groupNames = dayData.groupStats.map(gs => gs.groupName)
  const groupSlots = dayData.groupStats.map(gs => gs.availableSlots)
  const groupTeachers = dayData.groupStats.map(gs => gs.availableTeachers)

  // 生成颜色数组
  const colors = [
    '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',
    '#79bbff', '#95d475', '#eebe77', '#f78989', '#a6a9ad'
  ]

  const option = {
    title: {
      text: `${dayData.date} ${dayData.weekdayName} 各教学组课次分布`,
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params) => {
        const dataIndex = params[0].dataIndex
        const groupName = groupNames[dataIndex]
        const slots = groupSlots[dataIndex]
        const teachers = groupTeachers[dataIndex]

        return `
          <div>
            <div><strong>${groupName}</strong></div>
            <div>可排课课次: ${slots}</div>
            <div>有课时教师: ${teachers}人</div>
          </div>
        `
      }
    },
    xAxis: {
      type: 'category',
      data: groupNames,
      axisLabel: {
        fontSize: 12,
        interval: 0,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '课次',
      nameTextStyle: {
        fontSize: 12
      },
      axisLabel: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '可排课课次',
        type: 'bar',
        data: groupSlots,
        itemStyle: {
          color: (params) => {
            return colors[params.dataIndex % colors.length]
          }
        },
        barWidth: '60%',
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ],
    grid: {
      left: '10%',
      right: '10%',
      top: '20%',
      bottom: '25%'
    }
  }

  chartInstance.setOption(option)

  // 点击事件
//   chartInstance.on('click', (params) => {
//     const groupName = groupNames[params.dataIndex]
//     ElMessage.info(`查看 ${groupName} 在 ${dayData.date} 的详细信息`)
//   })
}





// 渲染教学组图表
const renderGroupCharts = () => {
  if (!dashboardData.value || !dashboardData.value.groupStats) return

  dashboardData.value.groupStats.forEach(group => {
    const chartEl = groupChartRefs.value.get(group.groupId)
    if (!chartEl) return

    // 销毁已存在的图表实例
    const existingInstance = groupChartInstances.value.get(group.groupId)
    if (existingInstance) {
      existingInstance.dispose()
    }

    // 创建新的图表实例
    const chartInstance = echarts.init(chartEl)
    groupChartInstances.value.set(group.groupId, chartInstance)

    // 获取该教学组的每日数据
    const dailyData = dashboardData.value.dailyStats.map(day => {
      const groupStat = day.groupStats?.find(gs => gs.groupId === group.groupId)
      return {
        date: day.date.slice(5), // 只显示月-日
        weekday: day.weekdayName,
        slots: groupStat?.availableSlots || 0
      }
    })

    const option = {
      title: {
        text: '每日课次',
        left: 'center',
        textStyle: {
          fontSize: 12,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const data = params[0]
          const dayData = dailyData[data.dataIndex]
          return `
            <div>
              <div><strong>${dayData.date} ${dayData.weekday}</strong></div>
              <div>可排课课次: ${data.value}</div>
            </div>
          `
        }
      },
      xAxis: {
        type: 'category',
        data: dailyData.map(d => d.date),
        axisLabel: {
          fontSize: 10,
          interval: 0,
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 10
        }
      },
      series: [
        {
          name: '课次',
          type: 'bar',
          data: dailyData.map(d => d.slots),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#67C23A' },
              { offset: 1, color: '#95d475' }
            ])
          },
          barWidth: '60%'
        }
      ],
      grid: {
        left: '10%',
        right: '10%',
        top: '25%',
        bottom: '15%'
      }
    }

    chartInstance.setOption(option)

    // 点击事件
    // chartInstance.on('click', (params) => {
    //   const dayData = dashboardData.value.dailyStats[params.dataIndex]
    //   viewDayDetail(dayData)
    // })
  })
}





// 生命周期
onMounted(() => {
  loadTeachingGroups()
  
  // 默认选择明天
  selectQuickDate('tomorrow')
})
</script>

<style lang="scss" scoped>
.teacher-schedule-dashboard {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      font-weight: 500;
    }

    .page-description {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }

  .filter-card {
    margin-bottom: 20px;

    :deep(.el-card__body) {
      padding: 20px;
    }

    .el-form {
      .el-form-item {
        margin-bottom: 0;
        margin-right: 20px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .stats-cards {
    margin-bottom: 20px;

    .stat-card {
      position: relative;
      overflow: hidden;

      :deep(.el-card__body) {
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .stat-content {
        .stat-value {
          font-size: 32px;
          font-weight: bold;
          color: #303133;
          line-height: 1;
          margin-bottom: 8px;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
        }
      }

      .stat-icon {
        font-size: 48px;
        opacity: 0.3;

        &.teacher-icon {
          color: #409EFF;
        }

        &.slots-icon {
          color: #67C23A;
        }

        &.average-icon {
          color: #E6A23C;
        }

        &.days-icon {
          color: #F56C6C;
        }
      }
    }
  }

  .chart-card,
  .table-card,
  .group-stats-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .chart-container {
      height: 400px;
      width: 100%;
    }
  }

  .group-stats-card {
    .group-card {
      height: 100%;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .group-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h4 {
          margin: 0;
          color: #303133;
          font-size: 16px;
          font-weight: 500;
        }
      }

      .group-stats {
        margin-bottom: 16px;

        .stat-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .stat-label {
            font-size: 13px;
            color: #909399;
          }

          .stat-value {
            font-size: 16px;
            font-weight: 600;

            &.primary {
              color: #409EFF;
            }

            &.success {
              color: #67C23A;
            }

            &.warning {
              color: #E6A23C;
            }
          }
        }
      }

      .group-chart-container {
        margin-bottom: 16px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        background-color: #fafafa;

        .group-chart {
          height: 120px;
          width: 100%;
        }
      }

      .group-actions {
        display: flex;
        justify-content: space-between;
        border-top: 1px solid #f0f0f0;
        padding-top: 12px;

        .el-button {
          padding: 4px 8px;
          font-size: 12px;
        }
      }
    }
  }

  .weekday-tag {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
  }

  .teacher-count {
    font-weight: 500;
    color: #409EFF;
  }

  .average-slots {
    font-weight: 500;
    color: #67C23A;
  }

  .slots-distribution {
    display: flex;
    align-items: center;
    gap: 10px;

    .distribution-bar {
      flex: 1;
      height: 8px;
      background-color: #f0f0f0;
      border-radius: 4px;
      overflow: hidden;

      .distribution-fill {
        height: 100%;
        background: linear-gradient(90deg, #409EFF, #79bbff);
        border-radius: 4px;
        transition: width 0.3s ease;
      }
    }

    .distribution-text {
      font-size: 12px;
      color: #909399;
      white-space: nowrap;
    }
  }

  // 表格展开内容样式
  .expand-content {
    padding: 16px;
    background-color: #fafafa;

    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 500;
    }

    .group-mini-card {
      background: white;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 8px;
      transition: all 0.3s ease;
      min-height: 50px;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 1px 4px rgba(64, 158, 255, 0.1);
      }

      .group-mini-header {
        margin-bottom: 4px;

        .group-name {
          font-size: 11px;
          font-weight: 500;
          color: #303133;
          line-height: 1.2;
        }
      }

      .group-mini-stats {
        .mini-stat {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2px;

          .mini-label {
            font-size: 10px;
            color: #909399;
          }

          .mini-value {
            font-size: 11px;
            font-weight: 500;
            color: #409eff;
          }
        }
      }
    }

    .no-group-data {
      text-align: center;
      color: #c0c4cc;
      font-style: italic;
      padding: 20px;
    }
  }



  // 当天教学组图表样式
  .day-group-chart-container {
    margin-bottom: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .day-group-chart {
      height: 300px;
      width: 100%;
    }
  }
}
</style>
