<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
<!--      <el-form-item label="核销平台" prop="orderSource">-->
<!--        <el-select-->
<!--          v-model="queryParams.orderSource"-->
<!--          placeholder="请选择核销平台"-->
<!--          clearable-->
<!--        >-->
<!--          <el-option-->
<!--            v-for="item in OrderWriteroffSource"-->
<!--            :key="item.value"-->
<!--            :label="item.label"-->
<!--            :value="item.value"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="核销订单号" prop="woffOrderNo">
        <el-input
          v-model="queryParams.woffOrderNo"
          placeholder="请输入核销订单号"
          clearable
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="核销状态" prop="writerOffStatus">
        <el-select
          v-model="queryParams.writerOffStatus"
          placeholder="请输入核销状态"
          clearable
        >
          <el-option label="待审核" value="待审核" />
          <el-option label="审核通过" value="审核通过" />
          <el-option label="审核拒绝" value="审核拒绝" />
        </el-select>
      </el-form-item>
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="writerOffList" @selection-change="handleSelectionChange">
      <el-table-column label="核销订单号" align="center" prop="woffOrderNo" width="180" />
      <el-table-column label="核销平台" align="center" prop="woffOrderSource" width="80">
        <template #default="scope">
          <el-tag :type="getSourceTagType(scope.row.woffOrderSource)">
            {{ getSourceLabel(scope.row.woffOrderSource) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="产品编号" align="center" prop="productNo" width="200" />
      <el-table-column label="产品名称" align="center" prop="productName" width="200" />
      <el-table-column label="学生姓名" align="center" prop="studentName" width="120" />
      <el-table-column label="核销状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="核销人员" align="center" prop="woffUserName" width="120" />
      <el-table-column label="申请时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核时间" align="center" prop="approveTime" width="180">
        <template #default="scope">
          <span>{{ scope.row.approveTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单截图" align="center" prop="orderImg" width="120" class-name="image-column">
        <template #default="scope">
          <div v-if="scope.row.orderImg && scope.row.orderImg.length > 0" class="image-gallery">
            <div class="image-wrapper">
              <el-image
                :src="scope.row.orderImg[0]"
                style="width: 60px; height: 40px;"
                fit="cover"
                :preview-src-list="scope.row.orderImg"
                preview-teleported
              />
              <span
                v-if="scope.row.orderImg.length > 1"
                class="custom-badge"
              >
                {{ scope.row.orderImg.length }}
              </span>
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="320" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleDetail(scope.row)"
          >详情</el-button>
          <el-button
            v-if="canApprove(scope.row)"
            link
            type="success"
            icon="Check"
            @click="handleApprove(scope.row, '审核通过')"
            v-hasPermi="['order:writeoff:approve']"
          >审核通过</el-button>
          <el-button
            v-if="canApprove(scope.row)"
            link
            type="danger"
            icon="Close"
            @click="handleApprove(scope.row, '审核拒绝')"
            v-hasPermi="['order:writeoff:approve']"
          >审核拒绝</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 核销记录详情 -->
    <el-dialog v-model="detailVisible" title="核销记录详情" width="1200px" append-to-body>
      <OrderWriterOffDetail
        v-if="detailVisible && currentDetailId"
        :id="currentDetailId"
        @close="detailVisible = false"
        @approve="handleApproveFromDetail"
      />
    </el-dialog>

    <!-- 审核对话框 -->
    <WriteOffApprovalDialog
      v-model="approvalVisible"
      :writeoff-record="currentWriteOffRecord"
      :approval-result="currentApprovalResult"
      @success="handleApprovalSuccess"
    />

  </div>
</template>

<script setup name="OrderWriterOff">
import { ref, reactive, toRefs, getCurrentInstance } from 'vue'
import {delWriterOff, getWriterOff, listWriterOff, approveWriteOff} from "@/api/management/order-writeroff.js";
import { OrderWriteroffSource } from "@/api/management/common/OrderConstants.ts";
import OrderWriterOffDetail from './detail.vue';
import WriteOffApprovalDialog from './components/WriteOffApprovalDialog.vue';
import { ElMessage } from 'element-plus';

const { proxy } = getCurrentInstance();

const writerOffList = ref([]);
const detailVisible = ref(false);
const addVisible = ref(false);
const studentSelectVisible = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const currentDetailId = ref(null);
const selectedStudent = ref(null);
const dateRange = ref([]);

// 审核相关
const approvalVisible = ref(false);
const currentWriteOffRecord = ref(null);
const currentApprovalResult = ref('');

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    woffOrderNo: null,
    productName: null,
    orderSource: null,
    status: null,
    woffUserId: null,
    productId: null
  }
});

const { queryParams } = toRefs(data);

/** 查询核销记录列表 */
function getList() {
  loading.value = true;
  const params = { ...queryParams.value };
  if (dateRange.value && dateRange.value.length === 2) {
    params.beginTime = dateRange.value[0];
    params.endTime = dateRange.value[1];
  }
  listWriterOff(params).then(response => {
    writerOffList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  selectedStudent.value = null;
  studentSelectVisible.value = true;
}

/** 学生选择确认 */
function handleStudentSelect(student) {
  selectedStudent.value = student;
  studentSelectVisible.value = false;
  addVisible.value = true;
}

/** 新增成功回调 */
function handleAddSuccess() {
  getList();
}

/** 详情按钮操作 */
function handleDetail(row) {
  const id = row.id || ids.value[0];
  currentDetailId.value = id;
  detailVisible.value = true;
}

/** 删除按钮操作 */
function handleDelete(row) {
  const writerOffIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除核销记录编号为"' + writerOffIds + '"的数据项？').then(function() {
    return delWriterOff(writerOffIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('order/writeroff/export', {
    ...queryParams.value
  }, `writeroff_${new Date().getTime()}.xlsx`);
}

/** 获取平台标签类型 */
function getSourceTagType(source) {
  const typeMap = {
    'DOUDIAN': 'primary',
    'TAOBAO': 'success',
    'WECHAT': 'warning',
    'OTHER': 'info'
  };
  return typeMap[source] || 'info';
}

/** 获取平台标签文本 */
function getSourceLabel(source) {
  const item = OrderWriteroffSource.find(item => item.value === source);
  return item ? item.label : source;
}

/** 获取状态标签类型 */
function getStatusTagType(status) {
  const typeMap = {
    '待审核': 'primary',
    '审核通过': 'success',
    '审核拒绝': 'danger',
  };
  return typeMap[status] || 'info';
}

/** 判断是否可以审核 */
function canApprove(row) {
  return row.status === '待审核';
}

/** 处理审核操作 */
function handleApprove(row, result) {
  currentWriteOffRecord.value = row;
  currentApprovalResult.value = result;
  approvalVisible.value = true;
}

/** 从详情页面触发的审核 */
function handleApproveFromDetail(data) {
  handleApprove(data.record, data.result);
}

/** 审核成功回调 */
function handleApprovalSuccess() {
  approvalVisible.value = false;
  getList();
}

getList();
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.image-gallery {
  display: inline-block;
  position: relative;
}

.image-wrapper {
  position: relative;
  display: inline-block;
}

.custom-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #f56c6c;
  color: white;
  border-radius: 10px;
  padding: 0 6px;
  font-size: 12px;
  line-height: 18px;
  min-width: 18px;
  text-align: center;
  border: 2px solid #fff;
  z-index: 1000;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 确保表格单元格不会裁剪徽章 */
:deep(.image-column .cell) {
  overflow: visible !important;
  position: relative;
}

:deep(.image-column) {
  overflow: visible !important;
}
</style>