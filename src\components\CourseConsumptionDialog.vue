<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 内嵌独立查询页面 -->
    <CourseConsumptionQuery
      v-if="visible"
      :key="queryKey"
      :student-id="studentInfo?.studentId"
      :course-hours-id="courseHoursId"
      :student-name="studentInfo?.studentName"
      :student-phone="studentInfo?.studentPhone"
      :subject="studentInfo?.subject"
      :specification="studentInfo?.specification"
      :is-dialog-mode="true"
    />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, watch, ref } from 'vue'
import CourseConsumptionQuery from '@/views/management/course-consumption/index.vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  studentInfo: {
    type: Object,
    default: null
  },
  courseHoursId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const dialogTitle = computed(() => {
  if (props.studentInfo) {
    const baseTitle = `课消记录 - ${props.studentInfo.studentName} (${props.studentInfo.subject} - ${props.studentInfo.specification})`
    return props.courseHoursId ? `${baseTitle} - 课时记录` : baseTitle
  }
  return '课消记录'
})

// 查询组件的key，用于强制重新渲染
const queryKey = ref(0)

// 监听关键参数变化，强制重新渲染查询组件
watch([() => props.courseHoursId, () => props.studentInfo?.id], () => {
  if (props.visible) {
    queryKey.value++
  }
}, { immediate: false })

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

/* 对话框内容样式调整 */
:deep(.el-dialog__body) {
  padding: 10px 20px;
}

/* 隐藏内嵌页面的卡片边框 */
:deep(.box-card) {
  border: none;
  box-shadow: none;
}

:deep(.box-card .el-card__header) {
  display: none;
}

:deep(.box-card .el-card__body) {
  padding: 0;
}
</style>
