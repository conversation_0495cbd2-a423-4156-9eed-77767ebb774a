<template>
  <div class="teaching-group-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>教学组管理</h2>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline class="search-form">
        <div class="form-left">
          <el-form-item label="组名">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入教学组名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>

          <el-form-item label="组长">
            <el-select
              v-model="searchForm.leaderId"
              placeholder="请选择组长"
              clearable
              filterable
              style="width: 150px"
            >
              <el-option
                v-for="teacher in allTeachers"
                :key="teacher.id"
                :label="teacher.name"
                :value="teacher.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </div>
        <div class="form-right">
          <!-- <el-button type="success" @click="handleCreateTeacher">
            <el-icon><Plus /></el-icon>
            创建教师
          </el-button> -->
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            创建教学组
          </el-button>
        </div>
      </el-form>
    </el-card>

    <!-- 教学组列表 -->
    <el-card class="table-card">

      <el-table
        v-loading="loading"
        :data="teachingGroups"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="name" label="教学组名称" min-width="150" />
        <el-table-column prop="leaderName" label="组长" width="100">
          <template #default="{ row }">
            <span>{{ row.leaderName || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="adminName" label="教务" width="100">
          <template #default="{ row }">
            <span>{{ row.adminName || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="memberCount" label="教师数量" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === "active" ? "活跃" : "停用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="description"
          label="描述"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)"> 编辑 </el-button>
            <el-button type="primary" link @click="handleManageTeachers(row)">
              管理教师
            </el-button>
            <el-button type="danger" link @click="handleDelete(row)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑教学组对话框 -->
    <CreateEditDialog
      v-model="dialogVisible"
      :group="currentGroup"
      :all-teachers="allTeachers"
      @success="handleDialogSuccess"
    />

    <!-- 教师管理对话框 -->
    <TeacherManageDialog
      v-model="teacherDialogVisible"
      :group="currentGroup"
      @success="handleTeacherDialogSuccess"
    />

    <!-- 创建教师对话框 -->
    <CreateTeacherDialog
      v-model="createTeacherDialogVisible"
      @success="handleCreateTeacherSuccess"
    />
  </div>
</template>

<script setup name="TeachingGroupManagement">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Search, Refresh } from "@element-plus/icons-vue";
import { useTeachingGroupStore } from "@/stores/teachingGroup";
import { formatDateTime } from "@/utils/date";
import CreateEditDialog from "./components/CreateEditDialog.vue";
import TeacherManageDialog from "./components/TeacherManageDialog.vue";
import CreateTeacherDialog from "./components/CreateTeacherDialog.vue";

// 使用store
const teachingGroupStore = useTeachingGroupStore();

// 响应式数据
const searchForm = reactive({
  name: "",
  leaderId: "",
});

const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0,
});

const dialogVisible = ref(false);
const teacherDialogVisible = ref(false);
const createTeacherDialogVisible = ref(false);
const currentGroup = ref(null);

// 计算属性
const teachingGroups = computed(() => teachingGroupStore.teachingGroups);
const allTeachers = computed(() => teachingGroupStore.allTeachers);
const loading = computed(() => teachingGroupStore.loading);

// 方法
const fetchData = async () => {
  const params = {
    pageNum: pagination.pageNum,
    pageSize: pagination.pageSize,
    ...searchForm,
  };

  const result = await teachingGroupStore.fetchTeachingGroups(params);
  if (result) {
    pagination.total = result.total;
  }
};

const handleSearch = () => {
  pagination.pageNum = 1;
  fetchData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    name: "",
    leaderId: "",
  });
  pagination.pageNum = 1;
  fetchData();
};

const handleCreate = () => {
  currentGroup.value = null;
  dialogVisible.value = true;
};

const handleCreateTeacher = () => {
  createTeacherDialogVisible.value = true;
};

const handleEdit = (row) => {
  currentGroup.value = { ...row };
  dialogVisible.value = true;
};

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除教学组"${row.name}"吗？`, "确认删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const success = await teachingGroupStore.deleteTeachingGroup(row.id);
    if (success) {
      fetchData();
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败:", error);
    }
  }
};

const handleManageTeachers = (row) => {
  currentGroup.value = { ...row };
  teacherDialogVisible.value = true;
};

const handleSizeChange = (size) => {
  pagination.pageSize = size;
  pagination.pageNum = 1;
  fetchData();
};

const handleCurrentChange = (page) => {
  pagination.pageNum = page;
  fetchData();
};

const handleDialogSuccess = () => {
  dialogVisible.value = false;
  fetchData();
};

const handleTeacherDialogSuccess = () => {
  teacherDialogVisible.value = false;
  fetchData();
};

const handleCreateTeacherSuccess = () => {
  createTeacherDialogVisible.value = false;
  fetchData();
};

// 生命周期
onMounted(async () => {
  await Promise.all([teachingGroupStore.fetchAllTeachers(), fetchData()]);
});
</script>

<style lang="scss" scoped>
.teaching-group-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }
  }

  .search-card {
    margin-bottom: 20px;

    :deep(.el-card__body) {
      padding: 16px 20px;
    }

    .search-form {
      margin-bottom: 0;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .form-left {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 0;
      }

      .form-right {
        display: flex;
        gap: 8px;
        flex-shrink: 0;
      }
    }
  }

  .table-card {
    :deep(.el-card__body) {
      padding: 0;
    }

    .el-table {
      :deep(.el-table__header) {
        th {
          background-color: #fafafa;
          color: #606266;
          font-weight: 600;
        }
      }
    }

    .pagination-container {
      padding: 16px 20px;
      display: flex;
      justify-content: flex-end;
      border-top: 1px solid #ebeef5;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .teaching-group-container {
    .page-header {
      flex-direction: column;
      align-items: stretch;

      .header-left {
        .stats-cards {
          flex-wrap: wrap;
        }
      }

      .header-right {
        margin-top: 16px;
        align-self: flex-end;
      }
    }
  }
}

@media (max-width: 768px) {
  .teaching-group-container {
    padding: 16px;

    .page-header {
      .header-left {
        .stats-cards {
          .stats-card {
            min-width: 100px;
          }
        }
      }
    }

    .search-card {
      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 16px;
          margin-right: 0;
          width: 100%;

          .el-input,
          .el-select {
            width: 100% !important;
          }
        }
      }
    }
  }
}
</style>
