<template>
  <div class="writeoff-detail">
    <div v-if="detailData" class="detail-content">
      <!-- 核销信息 -->
      <el-card class="info-section" shadow="never">
        <template #header>
          <h3>核销信息</h3>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="核销订单号">{{ detailData.orderWriterOffs?.woffOrderNo || '-' }}</el-descriptions-item>
          <el-descriptions-item label="核销平台">
            <el-tag :type="getSourceTagType(detailData.orderWriterOffs?.woffOrderSource)">
              {{ detailData.orderWriterOffs?.woffOrderSource || '-' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="核销状态">
            <el-tag :type="getStatusTagType(detailData.orderWriterOffs?.status)">
              {{ detailData.orderWriterOffs?.status || '-' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="核销时间">{{ formatTime(detailData.orderWriterOffs?.createTime) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="学生姓名">{{ detailData.userStudentExt?.name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="核销人员">{{ detailData.orderWriterOffs?.woffUserName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="核销备注">{{ detailData.orderWriterOffs?.remark || '-' }}</el-descriptions-item>
          <el-descriptions-item label="订单截图">
            <el-image
                v-if="detailData.orderWriterOffs?.orderImg"
                :src="detailData.orderWriterOffs?.orderImg"
                style="width: 60px; height: 40px;"
                fit="cover"
                :preview-src-list="[detailData.orderWriterOffs?.orderImg]"
                preview-teleported
            />
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 产品信息 -->
      <el-card class="info-section" shadow="never">
        <template #header>
          <h3>产品信息</h3>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="产品名称">{{ detailData.orderWriterOffs?.product?.name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="产品编号">{{ detailData.orderWriterOffs?.product?.productNo || '-' }}</el-descriptions-item>
          <el-descriptions-item label="学科">{{ detailData.orderWriterOffs?.product?.subject || '-' }}</el-descriptions-item>
          <el-descriptions-item label="课程类型">{{ detailData.orderWriterOffs?.product?.courseType || '-' }}</el-descriptions-item>
          <el-descriptions-item label="适用年级">
            <el-tag 
              v-for="grade in detailData.orderWriterOffs?.product?.applicableGrades || []"
              :key="grade" 
              size="small" 
              style="margin-right: 5px;"
            >
              {{ grade }}
            </el-tag>
            <span v-if="!detailData.orderWriterOffs?.product?.applicableGrades?.length">-</span>
          </el-descriptions-item>
          <el-descriptions-item label="单价">{{ formatPrice(detailData.orderWriterOffs?.product?.unitPrice) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="课时">{{ detailData.orderWriterOffs?.product?.quantity || '-' }}&nbsp;&nbsp;赠送课时：{{ detailData.orderWriterOffs?.product?.bonusHoursQuantity || '-' }}</el-descriptions-item>
          <el-descriptions-item label="原价">{{ formatPrice(detailData.orderWriterOffs?.product?.originalPrice) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="售价">{{ formatPrice(detailData.orderWriterOffs?.product?.sellingPrice) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="教材费">{{ formatPrice(detailData.orderWriterOffs?.product?.materialFee) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="产品描述">{{ detailData.orderWriterOffs?.product?.description || '-' }}</el-descriptions-item>
          <el-descriptions-item label="产品状态">
            <el-tag :type="detailData.orderWriterOffs?.product?.status === '上架' ? 'success' : 'danger'">
              {{ detailData.orderWriterOffs?.product?.status || '-' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 审核信息 -->
      <el-card class="info-section" shadow="never">
        <template #header>
          <h3>审核信息</h3>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="审核状态">
            <el-tag :type="getStatusTagType(detailData.orderWriterOffs?.status)">
              {{ detailData.orderWriterOffs?.status || '-' }}
            </el-tag></el-descriptions-item>
          <el-descriptions-item label="原因">{{ detailData.orderWriterOffs?.reason || '-' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <div v-else class="no-data">
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup name="OrderWriterOffDetail">
import {onMounted, ref, watch} from 'vue'
import {getWriterOff} from "@/api/management/order-writeroff.js"

const props = defineProps({
  id: {
    type: [String, Number],
    required: true
  }
})

const emit = defineEmits(['close', 'approve'])

const loading = ref(false)
const detailData = ref(null)

/** 获取详情数据 */
const getDetailData = async () => {
  if (!props.id) return

  loading.value = true
  try {
    const response = await getWriterOff(props.id)
    detailData.value = response.data
  } catch (error) {
    console.error('获取核销详情失败:', error)
  } finally {
    loading.value = false
  }
}


/** 获取平台标签类型 */
const getSourceTagType = (source) => {
  const typeMap = {
    '抖店': 'primary',
    '星橙CRM': 'success',
    '系统创建': 'warning'
  }
  return typeMap[source] || 'info'
}

/** 获取状态标签类型 */
const getStatusTagType = (status) => {
  const typeMap = {
    '待核销': 'primary',
    '审核通过': 'success',
    '审核拒绝': 'warning'
  }
  return typeMap[status] || 'info'
}


/** 格式化时间 */
const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  try {
    const date = new Date(timeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    return timeStr
  }
}

/** 格式化价格 */
const formatPrice = (price) => {
  if (price === null || price === undefined) return '-'
  // 将分转换为元
  const yuan = (price / 100).toFixed(2)
  return `¥${yuan}`
}

// 监听 id 变化
watch(() => props.id, () => {
  if (props.id) {
    getDetailData()
  }
}, { immediate: true })

onMounted(() => {
  getDetailData()
})
</script>

<style scoped>
.writeoff-detail {
  padding: 20px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-section {
  border: 1px solid #e4e7ed;
}

.info-section :deep(.el-card__header) {
  background-color: #f5f7fa;
  padding: 12px 20px;
}

.info-section h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #303133;
}

.approval-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 20px 0;
}

.approval-actions .el-button {
  min-width: 120px;
}
</style>