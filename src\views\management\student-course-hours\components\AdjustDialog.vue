<template>
  <el-dialog
    v-model="dialogVisible"
    title="课时调整"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="!studentInfo" class="no-student-info">
      <el-alert
        title="提示"
        description="课时调整只能从学生课时列表的操作按钮进入"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>

    <div v-else>
      <!-- 订单课时包警告 -->
      <el-alert
        v-if="isOrderBasedCourseHours(currentHoursInfo)"
        title="警告"
        description="此课时包来自订单，禁止调整课时"
        type="error"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      />
      
      <!-- 失效课时包警告 -->
      <el-alert
        v-if="isCourseHoursInactive(currentHoursInfo)"
        title="警告"
        description="此课时包已失效，禁止调整课时"
        type="error"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      />

      <!-- 学生信息（只读显示） -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">学生课时信息</span>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="学生姓名">{{ studentInfo.studentName }}</el-descriptions-item>
          <el-descriptions-item label="学生手机">{{ studentInfo.studentPhone }}</el-descriptions-item>
          <el-descriptions-item label="学科">{{ studentInfo.subject }}</el-descriptions-item>
          <el-descriptions-item label="课型">{{ studentInfo.specification }}</el-descriptions-item>
          <el-descriptions-item label="性质">{{ studentInfo.nature }}</el-descriptions-item>
          <el-descriptions-item label="状态">{{ studentInfo.status }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 当前课时统计 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">当前课时统计</span>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="总课时">
            <span class="hours-number">{{ currentHoursInfo?.totalHours || 0 }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="剩余课时">
            <span class="hours-number" :class="{ 'low-hours': (currentHoursInfo?.remainingHours || 0) <= 5 }">
              {{ currentHoursInfo?.remainingHours || 0 }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="购买课时">
            <div class="hours-detail-item">
              <span class="hours-number">总: {{ currentHoursInfo?.purchasedHours || 0 }}</span>
              <span class="hours-number remaining" :class="{ 'low-hours': (currentHoursInfo?.remainingPurchasedHours || 0) <= 2 }">
                剩: {{ currentHoursInfo?.remainingPurchasedHours || 0 }}
              </span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="赠送课时">
            <div class="hours-detail-item">
              <span class="hours-number">总: {{ currentHoursInfo?.giftHours || 0 }}</span>
              <span class="hours-number remaining" :class="{ 'low-hours': (currentHoursInfo?.remainingGiftHours || 0) <= 2 }">
                剩: {{ currentHoursInfo?.remainingGiftHours || 0 }}
              </span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="单价">
            <span class="hours-number">¥{{ currentHoursInfo?.unitPrice || 0 }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 课时调整表单 -->
      <el-card class="adjust-card" shadow="never">
        <template #header>
          <span class="card-title">课时调整</span>
        </template>

        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          :disabled="!canAdjustCourseHours(currentHoursInfo)"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="购买课时" prop="purchasedHours">
                <el-input-number
                  v-model="form.purchasedHours"
                  :min="0"
                  :max="9999.99"
                  :precision="2"
                  :step="0.5"
                  placeholder="请输入购买课时"
                  style="width: 100%"
                />
                <div class="form-tip">学生购买的课时总数</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="剩余购买课时" prop="remainingPurchasedHours">
                <el-input-number
                  v-model="form.remainingPurchasedHours"
                  :min="0"
                  :max="form.purchasedHours || 9999.99"
                  :precision="2"
                  :step="0.5"
                  placeholder="请输入剩余购买课时"
                  style="width: 100%"
                />
                <div class="form-tip">购买课时中尚未消费的部分</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="赠送课时" prop="giftHours">
                <el-input-number
                  v-model="form.giftHours"
                  :min="0"
                  :max="9999.99"
                  :precision="2"
                  :step="0.5"
                  placeholder="请输入赠送课时"
                  style="width: 100%"
                />
                <div class="form-tip">系统或机构赠送的课时总数</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="剩余赠送课时" prop="remainingGiftHours">
                <el-input-number
                  v-model="form.remainingGiftHours"
                  :min="0"
                  :max="form.giftHours || 9999.99"
                  :precision="2"
                  :step="0.5"
                  placeholder="请输入剩余赠送课时"
                  style="width: 100%"
                />
                <div class="form-tip">赠送课时中尚未消费的部分</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="单价" prop="unitPrice">
            <el-input-number
              v-model="form.unitPrice"
              :min="0"
              :precision="2"
              :step="1"
              placeholder="请输入单价"
              style="width: 100%"
            />
            <div class="form-tip">修改课时包的单价</div>
          </el-form-item>

          <el-form-item label="调整原因" prop="adjustmentReason">
            <el-input
              v-model="form.adjustmentReason"
              type="textarea"
              :rows="3"
              placeholder="请详细说明调整原因"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <!-- 调整预览 -->
          <el-form-item label="调整预览" v-if="hasAdjustment">
            <el-alert
              :title="adjustmentPreview"
              type="info"
              :closable="false"
              show-icon
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          :disabled="!studentInfo || !hasAdjustment || !canAdjustCourseHours(currentHoursInfo)"
        >
          确定调整
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { updateStudentCourseHours, getCourseHoursById } from '@/api/management/studentCourseHours'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  studentInfo: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const formRef = ref()
const submitting = ref(false)
const loading = ref(false)
const currentHoursInfo = ref(null)

// 表单数据
const form = ref({
  purchasedHours: 0,
  remainingPurchasedHours: 0,
  giftHours: 0,
  remainingGiftHours: 0,
  unitPrice: 0,
  adjustmentReason: ''
})

// 表单验证规则
const rules = {
  purchasedHours: [
    { required: true, message: '请输入购买课时', trigger: 'blur' },
    {
      validator: (_rule, value, callback) => {
        if (value < 0) {
          callback(new Error('购买课时不能小于0'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  remainingPurchasedHours: [
    { required: true, message: '请输入剩余购买课时', trigger: 'blur' },
    {
      validator: (_rule, value, callback) => {
        if (value < 0) {
          callback(new Error('剩余购买课时不能小于0'))
        } else if (value > form.value.purchasedHours) {
          callback(new Error('剩余购买课时不能大于购买课时'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  giftHours: [
    { required: true, message: '请输入赠送课时', trigger: 'blur' },
    {
      validator: (_rule, value, callback) => {
        if (value < 0) {
          callback(new Error('赠送课时不能小于0'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  remainingGiftHours: [
    { required: true, message: '请输入剩余赠送课时', trigger: 'blur' },
    {
      validator: (_rule, value, callback) => {
        if (value < 0) {
          callback(new Error('剩余赠送课时不能小于0'))
        } else if (value > form.value.giftHours) {
          callback(new Error('剩余赠送课时不能大于赠送课时'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  unitPrice: [
    { required: true, message: '请输入单价', trigger: 'blur' },
    {
      validator: (_rule, value, callback) => {
        if (value < 0) {
          callback(new Error('单价不能小于0'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  adjustmentReason: [
    { required: true, message: '请输入调整原因', trigger: 'blur' },
    { min: 5, max: 500, message: '调整原因长度在5-500个字符', trigger: 'blur' }
  ]
}

// 计算属性
const hasAdjustment = computed(() => {
  if (!currentHoursInfo.value) return false

  const hasHoursChange =
    form.value.purchasedHours !== (currentHoursInfo.value.purchasedHours || 0) ||
    form.value.remainingPurchasedHours !== (currentHoursInfo.value.remainingPurchasedHours || 0) ||
    form.value.giftHours !== (currentHoursInfo.value.giftHours || 0) ||
    form.value.remainingGiftHours !== (currentHoursInfo.value.remainingGiftHours || 0)

  const hasPriceChange = form.value.unitPrice !== (currentHoursInfo.value.unitPrice || 0)

  return hasHoursChange || hasPriceChange
})

const adjustmentPreview = computed(() => {
  if (!hasAdjustment.value || !currentHoursInfo.value) return ''

  const changes = []

  // 计算总课时变化
  const oldTotal = (currentHoursInfo.value.purchasedHours || 0) + (currentHoursInfo.value.giftHours || 0)
  const newTotal = form.value.purchasedHours + form.value.giftHours

  // 计算剩余课时变化
  const oldRemaining = (currentHoursInfo.value.remainingPurchasedHours || 0) + (currentHoursInfo.value.remainingGiftHours || 0)
  const newRemaining = form.value.remainingPurchasedHours + form.value.remainingGiftHours

  if (oldTotal !== newTotal) {
    changes.push(`总课时：${oldTotal} → ${newTotal}`)
  }

  if (oldRemaining !== newRemaining) {
    changes.push(`剩余课时：${oldRemaining} → ${newRemaining}`)
  }

  if (form.value.unitPrice !== (currentHoursInfo.value.unitPrice || 0)) {
    changes.push(`单价：¥${currentHoursInfo.value.unitPrice || 0} → ¥${form.value.unitPrice}`)
  }

  return changes.length > 0 ? `将修改：${changes.join('，')}` : ''
})

// 监听对话框打开，获取最新数据
watch(() => props.visible, async (newVal) => {
  if (newVal && props.studentInfo) {
    await fetchLatestHoursInfo()
  }
})

// 监听课时信息变化，初始化表单数据
watch(() => currentHoursInfo.value, (newVal) => {
  if (newVal) {
    form.value.purchasedHours = newVal.purchasedHours || 0
    form.value.remainingPurchasedHours = newVal.remainingPurchasedHours || 0
    form.value.giftHours = newVal.giftHours || 0
    form.value.remainingGiftHours = newVal.remainingGiftHours || 0
    form.value.unitPrice = newVal.unitPrice || 0
  }
})

// 检查是否为来自订单的课时包
const isOrderBasedCourseHours = (courseHours) => {
  if (!courseHours) return false;
  
  // 检查是否有关联的订单ID
  if (courseHours.orderId && courseHours.orderId.trim() !== '') {
    return true;
  }
  
  // 检查课时来源类型是否为订单
  if (courseHours.sourceType === '订单') {
    return true;
  }
  
  return false;
};

// 检查课时包是否失效
const isCourseHoursInactive = (courseHours) => {
  if (!courseHours) return false;
  return courseHours.status === 'inactive';
};

// 检查是否可以调整课时
const canAdjustCourseHours = (courseHours) => {
  return !isOrderBasedCourseHours(courseHours) && !isCourseHoursInactive(courseHours);
};

// 获取最新课时信息
const fetchLatestHoursInfo = async () => {
  if (!props.studentInfo || !props.studentInfo.id) return

  loading.value = true
  try {
    const response = await getCourseHoursById(props.studentInfo.id)

    if (response.code === 200) {
      currentHoursInfo.value = response.data
    } else {
      ElMessage.error('获取最新课时信息失败')
      currentHoursInfo.value = props.studentInfo // 使用传入的信息作为备选
    }
  } catch (error) {
    console.error('获取最新课时信息失败:', error)
    ElMessage.error('获取最新课时信息失败')
    currentHoursInfo.value = props.studentInfo // 使用传入的信息作为备选
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!props.studentInfo) {
    ElMessage.error('请从学生课时列表进入调整功能')
    return
  }

  if (!canAdjustCourseHours(currentHoursInfo.value)) {
    if (isOrderBasedCourseHours(currentHoursInfo.value)) {
      ElMessage.error('来自订单的课时包禁止调整课时')
    } else if (isCourseHoursInactive(currentHoursInfo.value)) {
      ElMessage.error('失效的课时包禁止调整课时')
    }
    return
  }

  if (!hasAdjustment.value) {
    ElMessage.error('请至少调整一项课时')
    return
  }

  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitting.value = true

    const requestData = {
      courseHoursId: props.studentInfo.id,
      studentId: props.studentInfo.studentId,
      subject: props.studentInfo.subject,
      specification: props.studentInfo.specification,
      purchasedHours: form.value.purchasedHours,
      remainingPurchasedHours: form.value.remainingPurchasedHours,
      giftHours: form.value.giftHours,
      remainingGiftHours: form.value.remainingGiftHours,
      unitPrice: form.value.unitPrice,
      adjustmentReason: form.value.adjustmentReason
    }

    await updateStudentCourseHours(requestData)

    ElMessage.success('课时调整成功')
    emit('success')
    handleClose()

  } catch (error) {
    console.error('课时调整失败:', error)
    ElMessage.error('课时调整失败: ' + (error.message || '未知错误'))
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  form.value = {
    purchasedHours: 0,
    remainingPurchasedHours: 0,
    giftHours: 0,
    remainingGiftHours: 0,
    unitPrice: 0,
    adjustmentReason: ''
  }
  currentHoursInfo.value = null
  emit('update:visible', false)
}
</script>

<style scoped>
.no-student-info {
  text-align: center;
  padding: 40px 20px;
}

.info-card {
  margin-bottom: 20px;
}

.adjust-card {
  margin-bottom: 20px;
}

.card-title {
  font-weight: bold;
  color: #303133;
}

.hours-number {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
}

.low-hours {
  color: #f56c6c !important;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.hours-detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.hours-detail-item .remaining {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}
</style>
