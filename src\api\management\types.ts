// 教学组管理相关类型定义

// 基础响应接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页响应接口
export interface PageResponse<T> {
  total: number
  rows?: T[]
  records?: T[]
  pageNum: number
  pageSize: number
}

// 教学组基础信息
export interface TeachingGroup {
  id: string
  name: string
  description?: string
  leaderId?: string
  leaderName?: string
  adminId?: string
  adminName?: string
  memberCount: number
  status: 'active' | 'inactive'
  createTime: string
  updateTime?: string
  remark?: string
}

// 教师基础信息
export interface Teacher {
  id: string
  name: string
  nickname?: string
  gender: '1' | '2' | '3'
  age?: number // 年龄
  phone: string
  email?: string
  avatar?: string
  groupId?: string
  groupName?: string
  currentLocation?: string // 目前所在地
  employmentType?: 'full_time' | 'intended_full_time' | 'part_time' // 兼职/全职/意向全职
  currentStatus?: string // 目前状态（上班族、学生、居家办公）

  // 教育背景
  education?: string // 最高学历
  graduateSchool?: string // 毕业院校
  major?: string // 毕业专业
  universityType?: '双一流' | '985' | '211' | '一本' | '普通' // 大学属性
  isNormalUniversity?: boolean // 是否师范类
  studyAbroad?: boolean // 是否留学
  studyAbroadCountry?: string // 留学国家

  // 教学资质
  teachingCertificateLevel?: string // 教资级别
  subjects?: string[] // 教授学科
  englishQualification?: string // 英语资质
  mandarinQualification?: string // 普通话资质
  communicationAbility?: '优秀' | '良好' | '一般' | '薄弱' // 沟通能力

  // 教学经历
  teachingExperience?: string // 教学经历（年龄层）
  taughtCourses?: string[] // 教过课程
  teachingYears?: number // 教龄
  awards?: string // 获奖奖项

  // 教学风格和适配
  teachingStyle?: string[] // 上课风格
  englishPronunciation?: '良好' | '一般' | '正常' | '优秀（母语水平）' // 英语发音
  suitableGrades?: string[] // 适合学生年级
  suitableLevels?: string[] // 适合学生学习程度
  suitablePersonality?: '外向活泼' | '内向腼腆' | '都适合' // 适合学生性格

  // 暑期课上课时间
  summerScheduleType?: 'full' | 'golden' | 'other' // 全满档、黄金档、其他档

  other?: string // 其他
  introduction?: string // 简介
  status: 'active' | 'inactive'
  createTime: string
  updateTime?: string

  // 新增统计字段
  trialCoursePassRate?: number // 试听课通过率（3个月内）
  totalTeachingHours?: number // 系统已上课时（分钟）
  currentStudentCount?: number // 老师现有学生数
}

// 教师可上课时间状态
export interface TeacherTimeSlot {
  id?: string
  teacherId: string
  weekday: number // 1-7, 1为周一, 7为周日 (Java DayOfWeek格式)
  startTime: string // 开始时间，格式: "09:15"
  endTime: string // 结束时间，格式: "10:55"
  status: 'available' | 'scheduled' // 可上课、已排课
  remark?: string // 备注
}

// 教师带教信息
export interface TeacherTeachingInfo {
  teacherId: string
  teacherName: string
  currentStudents: number // 在教学生数
  unconsumedHours: number // 未消课时
  consumedHours: number // 已消课时
  totalHours: number // 总课时
}

// 创建教学组请求参数
export interface CreateTeachingGroupParams {
  name: string
  description?: string
  leaderId?: string
  adminId?: string
  remark?: string
}

// 更新教学组请求参数
export interface UpdateTeachingGroupParams {
  id: string
  name?: string
  description?: string
  leaderId?: string
  adminId?: string
  remark?: string
}

// 获取教学组列表请求参数
export interface GetTeachingGroupsParams {
  pageNum?: number
  pageSize?: number
  name?: string
  status?: 'active' | 'inactive'
  leaderId?: string
  adminId?: string
}

// 分配教师到教学组请求参数
export interface AssignTeacherParams {
  groupId: string
  teacherIds: string[]
}

// 移除教师从教学组请求参数
export interface RemoveTeacherParams {
  groupId: string
  teacherIds: string[]
}

// 获取教学组教师列表请求参数
export interface GetGroupTeachersParams {
  groupId: string
  pageNum?: number
  pageSize?: number
  name?: string
  status?: 'active' | 'inactive'
}

// 获取教师可上课时间请求参数
export interface GetTeacherTimeSlotsParams {
  teacherId: string
}

// 更新教师可上课时间请求参数
export interface UpdateTeacherTimeSlotsParams {
  teacherId: string
  timeSlots: TeacherTimeSlot[]
}

// 获取教师带教信息请求参数
export interface GetTeacherTeachingInfoParams {
  teacherId: string
}

// 教师时间表更新检查响应
export interface TeacherTimeSlotUpdateCheckResp {
  teacherId: string
  lastUpdateTime?: string
  daysSinceLastUpdate?: number
  needsUpdate: boolean
  message: string
}

// 教师视图类型
export type TeacherViewType = 'basic' | 'schedule' | 'teaching'

// 教学组角色类型
export type GroupRoleType = 'leader' | 'admin' | 'member'

// 用户角色信息
export interface UserRole {
  id: string
  name: string
  phone: string
  email?: string
  avatar?: string
}

// 可分配的教师列表（不在任何教学组的教师）
export interface AvailableTeacher {
  id: string
  name: string
  phone: string
  email?: string
  status: 'active' | 'inactive'
}

// 教学组统计信息
export interface TeachingGroupStats {
  totalGroups: number
  activeGroups: number
  totalTeachers: number
  unassignedTeachers: number
}
