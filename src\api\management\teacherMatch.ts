import request from '@/utils/request'

// 学生时间段
export interface StudentTimeSlot {
  weekday: number // 0-6, 0为周日
  startTime: string // 开始时间，格式: "09:15"
  endTime: string // 结束时间，格式: "10:55"
}

// 试听课时间
export interface TrialClassTime {
  date: string // 试听课日期 (YYYY-MM-DD格式)
  startTime: string // 开始时间 (HH:mm格式)
  endTime: string // 结束时间 (HH:mm格式)
}

// 匹配老师请求
export interface MatchTeachersRequest {
  studentId: string
  startDate: string
  timeSlots: StudentTimeSlot[]
  trialClassTime?: TrialClassTime // 试听课时间（可选）
  keyword?: string
  groupIds?: string[]

  // ========== 基础信息筛选 ==========
  gender?: string
  minAge?: number
  maxAge?: number
  employmentType?: string
  currentStatus?: string

  // ========== 教育背景筛选 ==========
  education?: string[]
  universityType?: string[]
  isNormalUniversity?: boolean
  studyAbroad?: boolean

  // ========== 教学资质筛选 ==========
  teachingCertificateLevel?: string[]
  subjects?: string[]
  englishQualification?: string[]
  mandarinQualification?: string[]
  communicationAbility?: string[]
  englishPronunciation?: string[]
  minTeachingYears?: number
  maxTeachingYears?: number

  // ========== 教学经历和风格筛选 ==========
  taughtCourses?: string[]
  teachingStyle?: string[]
  suitableGrades?: string[]
  suitableLevels?: string[]
  suitablePersonality?: string

  // ========== 暑期课上课时间筛选 ==========
  summerScheduleType?: string

  // ========== 课点更新天数筛选 ==========
  timeSlotUpdateDays?: number
}

// 教师可用时间段
export interface TeacherAvailableTimeSlot {
  weekday: number
  startTime: string
  endTime: string
  status: string
}

// 匹配的教师信息
export interface MatchedTeacher {
  teacherId: string
  teacherName: string
  teacherPhone: string
  groupName: string

  // ========== 基础信息 ==========
  gender?: string
  age?: number
  employmentType?: string
  currentStatus?: string

  // ========== 教育背景 ==========
  education?: string
  graduateSchool?: string
  universityType?: string
  isNormalUniversity?: boolean
  studyAbroad?: boolean
  studyAbroadCountry?: string

  // ========== 教学资质 ==========
  teachingCertificateLevel?: string
  subjects: string[]
  englishQualification?: string
  mandarinQualification?: string
  communicationAbility?: string
  englishPronunciation?: string
  teachingYears: number

  // ========== 教学经历和风格 ==========
  taughtCourses?: string[]
  teachingStyle?: string[]
  suitableGrades?: string[]
  suitableLevels?: string[]
  suitablePersonality?: string

  // ========== 暑期课上课时间 ==========
  summerScheduleType?: string

  currentStudents: number
  matchedTimeSlots: number
  matchPercentage: number
  availableTimeSlots: TeacherAvailableTimeSlot[]
  status: string
}

// 学生信息
export interface StudentInfo {
  studentId: string
  studentName: string
  studentPhone: string
  grade: string
  school: string
}

// 匹配老师响应
export interface MatchTeachersResponse {
  teachers: MatchedTeacher[]
  totalCount: number
  studentInfo: StudentInfo
}

// 每日时间安排
export interface DailySchedule {
  date: string
  weekday: number
  timeSlots: TeacherAvailableTimeSlot[]
}

// 教师详细时间安排（用于周视图）
export interface TeacherWeeklySchedule {
  teacherId: string
  teacherName: string
  startDate: string
  endDate: string
  dailySchedules: DailySchedule[]
}

/**
 * 根据学生信息匹配教师
 */
export function matchTeachersApi(data: MatchTeachersRequest): Promise<{ data: MatchTeachersResponse }> {
  return request({
    url: '/management/teacher-match/match',
    method: 'post',
    data
  })
}

/**
 * 获取教师详细时间安排（周视图）
 */
export function getTeacherWeeklyScheduleApi(teacherId: string, startDate: string): Promise<{ data: TeacherWeeklySchedule }> {
  return request({
    url: `/management/teacher-match/teacher/${teacherId}/weekly-schedule`,
    method: 'get',
    params: { startDate }
  })
}
