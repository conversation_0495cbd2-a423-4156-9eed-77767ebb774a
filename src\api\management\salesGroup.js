import request from '@/utils/request'

// 获取销售组列表
export function getSalesGroupListApi(params) {
  return request({
    url: '/sales/group/list',
    method: 'get',
    params
  })
}

// 获取销售组详情
export function getSalesGroupDetailApi(id) {
  return request({
    url: `/sales/group/${id}`,
    method: 'get'
  })
}

// 创建销售组
export function createSalesGroupApi(data) {
  return request({
    url: '/sales/group',
    method: 'post',
    data
  })
}

// 更新销售组
export function updateSalesGroupApi(data) {
  return request({
    url: '/sales/group',
    method: 'put',
    data
  })
}

// 删除销售组
export function deleteSalesGroupApi(ids) {
  return request({
    url: `/sales/group/${ids}`,
    method: 'delete'
  })
}

// 获取销售组成员列表
export function getSalesGroupMembersApi(groupId) {
  return request({
    url: `/sales/group/${groupId}/members`,
    method: 'get'
  })
}

// 添加销售组成员（单个）
export function addSalesGroupMemberApi(data) {
  return request({
    url: '/sales/group/member',
    method: 'post',
    data
  })
}

// 批量添加销售组成员
export function addSalesGroupMembersApi(data) {
  return request({
    url: '/sales/group/members',
    method: 'post',
    data
  })
}

// 移除销售组成员
export function removeSalesGroupMemberApi(data) {
  return request({
    url: '/sales/group/member',
    method: 'delete',
    data
  })
}

// 设置组长
export function setGroupLeaderApi(data) {
  return request({
    url: '/sales/group/leader',
    method: 'put',
    data
  })
}

// 获取可用销售人员列表
export function getAvailableSalesApi(params) {
  return request({
    url: '/sales/staff/available',
    method: 'get',
    params
  })
}

// 获取组长选项
export function getLeaderOptionsApi() {
  return request({
    url: '/sales/staff/options',
    method: 'get'
  })
}

// 获取销售组统计信息
export function getSalesGroupStatsApi() {
  return request({
    url: '/sales/group/stats',
    method: 'get'
  })
}

// 获取销售组选项列表（用于下拉选择）
export function getSalesGroupOptionsApi() {
  return request({
    url: '/sales/group/options',
    method: 'get'
  })
}
