export declare interface Word {
    wordId?: string;
    textbookId?: string;
    textbookType?: string;
    textbookName?: string;
    word?: string;
    /** 音节 */
    syllables?: string;

    /** 英式音标 */
    phoneticUk?: string;

    /** 美式音标 */
    phoneticUs?: string;

    /** 英式发音音频文件URL */
    audioUkUrl?: string;

    /** 美式发音音频文件URL */
    audioUsUrl?: string;

    /** 词义(可包含多个词性及对应中文解释,如{pos: [{"pos": "n.", "def": "猫"}, {"pos": "v.", "def": "抓"}], practices:["", ""]) */
    meaningsStr?: string;
    meanings: { [key: string]: Meanings };

    /** 例句 ([{"sentence_en": "...", "sentence_cn": "...", "audio_uk_url": "...", "audio_us_url": "...", "structure_parts_en": [["...", "..."]]}]) */
    sentences: { [key: string]: Sentences[] };
    sentencesStr?: string;

    /** 标签 (数组类型, 例如: "高频"、"名词"、"动词") */
    tags?: string;

    /** 难度等级 (1-5) */
    difficulty?: number;

    changeDescription?:string;

    /** 标准讲解视频URL (通用) */
    videoUrl?: { [key: string]: string };

    /** 单词测验标记 */
    flagPracticeWord?: boolean;

    /** 句子翻译标记 */
    flagPracticeTranslate?: boolean;

    /** 句子排序标记 */
    flagPracticeOrder?: boolean;
}

export declare interface Meanings {
    pos: Pos[];
    practices: string[];
    posStr?: string;
    practicesStr?: string;
}

declare interface Pos {
    pos?: string;
    def?: string;
}

export declare interface Sentences {
    stage:string;
    sentenceEn?: string;
    sentenceCn?: string;
    audioUkUrl?: string;
    audioUsUrl?: string;
    structurePartsEn?: string[];
    structurePartsEnStr?: string;
    practices?: string[];
    practicesStr?: string;
}

export declare interface QueryParam {
    textbookId?: string;
    unitId?: string;
    textBookType?: string;
    textBookName?: QryCondition;
    word?: QryCondition;
    pageNum: number;
    pageSize: number;
     /* 教材Id */
    //  private String textbookId;
    //  /* 单元Id */
    //  private String unitId;
    //  /* 教材类型 */
    //  private Integer textBookType;
    //  /* 教材名称 */
    //  private QryCondition textBookName;
    //  /* 单词 */
    //  private QryCondition word;
}

export  declare interface QryCondition {
    
    value?: string;
    /** 精确查询:EQ,模糊查询:LIKE */
    compare?: string;
}

export declare interface TeacherMaterial {
    id: Number;
    name: string;
    children?: TeacherMaterial[];
}

import request from '@/utils/request';
// 获取教材列表
export function getWordList(params: QueryParam) {
    return request({
        url: '/word/word/page?pageSize='+params.pageSize+'&pageNum='+params.pageNum,
        method: 'post',
        data: params
    });
}

export function editWord(params: any) {
    return request({
        url: '/word/word/edit',
        method: 'post',
        data: params,
    });
}

export function uploadFile(wordId:string,params: any) {
    return request({
        url: '/word/word/upload/'+wordId,
        method: 'post',
        data: params,
        headers: {
            'Content-Type': 'multipart/form-data'
          }
    });
}