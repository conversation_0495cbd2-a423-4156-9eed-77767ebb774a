<template>
  <div class="writeoff-records-container">
    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="queryParams" inline label-width="80px">
        <el-form-item label="订单号">
          <el-input
            v-model="queryParams.orderNo"
            placeholder="请输入第三方订单号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="学生姓名">
          <el-input
            v-model="queryParams.studentName"
            placeholder="请输入学生姓名"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="核销来源">
          <el-select v-model="queryParams.orderSource" placeholder="请选择核销来源" clearable>
            <el-option label="抖店" value="抖店" />
            <el-option label="星橙CRM" value="星橙CRM" />
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="queryParams.approveStatus" placeholder="请选择审核状态" clearable>
            <el-option label="待审核" value="待审核" />
            <el-option label="审核通过" value="审核通过" />
            <el-option label="审核拒绝" value="审核拒绝" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 核销记录列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="writeOffList"
        stripe
        style="width: 100%"
      >
        <el-table-column label="第三方订单号" prop="orderNo" width="180" show-overflow-tooltip />
        <el-table-column label="核销来源" prop="orderSource" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getSourceTagType(row.orderSource)">
              {{ row.orderSource }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="学生姓名" prop="studentName" width="120" />
        <el-table-column label="产品名称" prop="productName" min-width="200" show-overflow-tooltip />
        <el-table-column label="产品价格" prop="productPrice" width="120" align="center">
          <template #default="{ row }">
            <span class="amount-text">¥{{ (row.productPrice / 100).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审核状态" prop="approveStatus" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getApproveStatusTagType(row.approveStatus)">
              {{ row.approveStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="申请人" prop="createBy" width="120" />
        <el-table-column label="申请时间" prop="createTime" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="审核时间" prop="approveTime" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.approveTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              @click="handleDetail(row)"
            >
              查看详情
            </el-button>
            <el-button
              v-if="canApprove(row)"
              type="success"
              link
              @click="handleApprove(row, '审核通过')"
            >
              通过
            </el-button>
            <el-button
              v-if="canApprove(row)"
              type="danger"
              link
              @click="handleApprove(row, '审核拒绝')"
            >
              拒绝
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 核销详情对话框 -->
    <el-dialog
      title="核销记录详情"
      v-model="detailVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="writeOffDetail" class="writeoff-detail">
        <!-- 基本信息 -->
        <el-card class="detail-card" style="margin-bottom: 20px;">
          <template #header>
            <span>基本信息</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="第三方订单号">{{ writeOffDetail.orderNo }}</el-descriptions-item>
            <el-descriptions-item label="核销来源">
              <el-tag :type="getSourceTagType(writeOffDetail.orderSource)">
                {{ writeOffDetail.orderSource }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="学生姓名">{{ writeOffDetail.studentName }}</el-descriptions-item>
            <el-descriptions-item label="申请人">{{ writeOffDetail.createBy }}</el-descriptions-item>
            <el-descriptions-item label="申请时间">{{ formatDateTime(writeOffDetail.createTime) }}</el-descriptions-item>
            <el-descriptions-item label="审核状态">
              <el-tag :type="getApproveStatusTagType(writeOffDetail.approveStatus)">
                {{ writeOffDetail.approveStatus }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 产品信息 -->
        <el-card class="detail-card" style="margin-bottom: 20px;">
          <template #header>
            <span>产品信息</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="产品名称">{{ writeOffDetail.productName }}</el-descriptions-item>
            <el-descriptions-item label="产品价格">
              <span class="amount-text">¥{{ (writeOffDetail.productPrice / 100).toFixed(2) }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 订单截图 -->
        <el-card class="detail-card" v-if="writeOffDetail.orderImg">
          <template #header>
            <span>订单截图</span>
          </template>
          <div class="image-container">
            <el-image
              :src="writeOffDetail.orderImg"
              :preview-src-list="[writeOffDetail.orderImg]"
              fit="contain"
              style="width: 200px; height: 200px;"
            />
          </div>
        </el-card>
      </div>

      <template #footer>
        <el-button @click="detailVisible = false">关 闭</el-button>
        <el-button
          v-if="writeOffDetail && writeOffDetail.approveStatus === '待审核'"
          type="success"
          @click="handleApprove(writeOffDetail, '审核通过')"
        >
          审核通过
        </el-button>
        <el-button
          v-if="writeOffDetail && writeOffDetail.approveStatus === '待审核'"
          type="danger"
          @click="handleApprove(writeOffDetail, '审核拒绝')"
        >
          审核拒绝
        </el-button>
      </template>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      title="核销审核"
      v-model="approvalVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <div v-if="currentWriteOffRecord" class="approval-content">
        <!-- 核销信息概要 -->
        <el-card class="info-card" style="margin-bottom: 20px;">
          <template #header>
            <span>核销信息</span>
          </template>
          <el-descriptions :column="1" size="small">
            <el-descriptions-item label="第三方订单号">{{ currentWriteOffRecord.orderNo }}</el-descriptions-item>
            <el-descriptions-item label="核销来源">{{ currentWriteOffRecord.orderSource }}</el-descriptions-item>
            <el-descriptions-item label="学生姓名">{{ currentWriteOffRecord.studentName }}</el-descriptions-item>
            <el-descriptions-item label="产品名称">{{ currentWriteOffRecord.productName }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 审核表单 -->
        <el-form
          ref="approvalForm"
          :model="approvalForm"
          :rules="approvalRules"
          label-width="100px"
        >
          <el-form-item label="审核结果">
            <el-tag :type="currentApprovalResult === '审核通过' ? 'success' : 'danger'">
              {{ currentApprovalResult }}
            </el-tag>
          </el-form-item>

          <el-form-item label="审核备注" prop="approveRemark">
            <el-input
              v-model="approvalForm.approveRemark"
              type="textarea"
              :rows="4"
              placeholder="请输入审核备注"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="approvalVisible = false">取 消</el-button>
        <el-button
          type="primary"
          :loading="approvalSubmitting"
          @click="submitApproval"
        >
          {{ approvalSubmitting ? '提交中...' : '确认审核' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WriteOffRecords">
import { onMounted, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date.js'
import {
  listWriterOff,
  exportWriterOff,
  getWriteOffDetail,
  approveWriteOff
} from '@/api/management/order-writeroff'

// 响应式数据
const loading = ref(false)
const writeOffList = ref([])
const total = ref(0)
const dateRange = ref([])
const detailVisible = ref(false)
const approvalVisible = ref(false)
const currentWriteOffId = ref('')
const currentWriteOffRecord = ref(null)
const currentApprovalResult = ref('')
const writeOffDetail = ref(null)
const approvalSubmitting = ref(false)

// 审核表单
const approvalForm = reactive({
  approveRemark: ''
})

const approvalRules = {
  approveRemark: [
    { required: true, message: '请输入审核备注', trigger: 'blur' },
    { min: 5, max: 200, message: '审核备注长度在5到200个字符', trigger: 'blur' }
  ]
}

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  orderNo: '',
  studentName: '',
  orderSource: '',
  approveStatus: '',
  createTimeStart: '',
  createTimeEnd: ''
})

// 方法
const getList = async () => {
  try {
    loading.value = true
    
    // 处理时间范围
    if (dateRange.value && dateRange.value.length === 2) {
      queryParams.createTimeStart = dateRange.value[0]
      queryParams.createTimeEnd = dateRange.value[1]
    } else {
      queryParams.createTimeStart = ''
      queryParams.createTimeEnd = ''
    }
    
    const response = await listWriterOff(queryParams)
    
    if (response.code === 200) {
      writeOffList.value = response.rows || []
      total.value = response.total || 0
    } else {
      ElMessage.error(response.msg || '获取核销记录失败')
    }
  } catch (error) {
    console.error('获取核销记录失败:', error)
    ElMessage.error('获取核销记录失败')
  } finally {
    loading.value = false
  }
}

const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

const resetQuery = () => {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    orderNo: '',
    studentName: '',
    orderSource: '',
    writerOffStatus: ''
  })
  dateRange.value = []
  getList()
}

const handleSizeChange = (size) => {
  queryParams.pageSize = size
  queryParams.pageNum = 1
  getList()
}

const handleCurrentChange = (page) => {
  queryParams.pageNum = page
  getList()
}

const handleDetail = async (row) => {
  try {
    currentWriteOffId.value = row.id

    // 获取详情数据
    const response = await getWriteOffDetail(row.id)

    if (response.code === 200) {
      writeOffDetail.value = response.data
      detailVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取详情失败')
    }
  } catch (error) {
    console.error('获取核销记录详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

const handleApprove = (row, result) => {
  currentWriteOffRecord.value = row
  currentApprovalResult.value = result
  approvalForm.approveRemark = ''
  approvalVisible.value = true
}

const submitApproval = async () => {
  try {
    approvalSubmitting.value = true

    const approvalData = {
      approveStatus: currentApprovalResult.value,
      approveRemark: approvalForm.approveRemark
    }

    const response = await approveWriteOff(currentWriteOffRecord.value.id, approvalData)

    if (response.code === 200) {
      ElMessage.success('审核成功')
      approvalVisible.value = false
      getList()
      // 如果详情对话框打开，也需要刷新详情
      if (detailVisible.value && writeOffDetail.value) {
        const detailResponse = await getWriteOffDetail(writeOffDetail.value.id)
        if (detailResponse.code === 200) {
          writeOffDetail.value = detailResponse.data
        }
      }
    } else {
      ElMessage.error('审核失败: ' + (response.msg || '未知错误'))
    }
  } catch (error) {
    console.error('审核失败:', error)
    ElMessage.error('审核失败: ' + (error.message || '未知错误'))
  } finally {
    approvalSubmitting.value = false
  }
}

const handleApprovalSuccess = () => {
  getList()
}

const handleExport = async () => {
  try {
    const exportParams = { ...queryParams }
    if (dateRange.value && dateRange.value.length === 2) {
      exportParams.createTimeStart = dateRange.value[0]
      exportParams.createTimeEnd = dateRange.value[1]
    }
    
    await exportWriterOff(exportParams)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 工具方法
const getSourceTagType = (source) => {
  const sourceMap = {
    '抖店': 'primary',
    '星橙CRM': 'success'
  }
  return sourceMap[source] || 'info'
}

const getApproveStatusTagType = (status) => {
  const statusMap = {
    '待审核': 'warning',
    '审核通过': 'success',
    '审核拒绝': 'danger'
  }
  return statusMap[status] || 'info'
}

const canApprove = (row) => {
  return row.approveStatus === '待审核'
}

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style scoped>
.writeoff-records-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.amount-text {
  font-weight: 500;
  color: #E6A23C;
}
</style>
