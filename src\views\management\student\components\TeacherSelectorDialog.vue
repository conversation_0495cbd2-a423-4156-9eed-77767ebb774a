<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择首选教师"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="teacher-selector-content">
      <!-- 搜索条件 -->
      <el-card shadow="never" class="search-card">
        <el-form :model="searchForm" inline>
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="教师姓名或手机号"
              style="width: 200px"
              clearable
            />
          </el-form-item>
          <el-form-item label="教学组">
            <el-select
              v-model="searchForm.groupId"
              placeholder="请选择教学组"
              style="width: 200px"
              clearable
            >
              <el-option
                v-for="group in teachingGroups"
                :key="group.id"
                :label="group.name"
                :value="group.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="性别">
            <el-select
              v-model="searchForm.gender"
              placeholder="请选择性别"
              style="width: 120px"
              clearable
            >
              <el-option label="男" value="0" />
              <el-option label="女" value="1" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchTeachers" :loading="searching">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 教师列表 -->
      <el-card shadow="never" class="teachers-card">
        <template #header>
          <div class="card-header">
            <span>可选教师列表</span>
            <span class="teacher-count">
              已选择 {{ selectedTeacherIds.length }}/2 位教师
            </span>
          </div>
        </template>

        <div v-loading="searching" class="teachers-list">
          <div
            v-for="teacher in teachers"
            :key="teacher.id"
            class="teacher-card"
            :class="{ 
              'selected': selectedTeacherIds.includes(teacher.id),
              'disabled': !selectedTeacherIds.includes(teacher.id) && selectedTeacherIds.length >= 2
            }"
            @click="toggleTeacher(teacher)"
          >
            <div class="teacher-avatar">
              <el-avatar :size="50">
                {{ teacher.name?.charAt(0) || 'T' }}
              </el-avatar>
            </div>
            
            <div class="teacher-info">
              <div class="teacher-name">{{ teacher.name }}</div>
              <div class="teacher-details">
                <div class="detail-item">
                  <el-icon><Phone /></el-icon>
                  <span>{{ teacher.phone }}</span>
                </div>
                <div class="detail-item" v-if="teacher.groupName">
                  <el-icon><OfficeBuilding /></el-icon>
                  <span>{{ teacher.groupName }}</span>
                </div>
                <div class="detail-item" v-if="teacher.subjects?.length">
                  <el-icon><Document /></el-icon>
                  <span>{{ teacher.subjects.join('、') }}</span>
                </div>
                <div class="detail-item" v-if="teacher.teachingYears">
                  <el-icon><Trophy /></el-icon>
                  <span>{{ teacher.teachingYears }}年教学经验</span>
                </div>
              </div>
            </div>

            <div class="teacher-status">
              <div class="current-students">
                <span class="label">当前学生：</span>
                <span class="value">{{ teacher.currentStudents || 0 }}人</span>
              </div>
              <div class="selection-status">
                <el-tag
                  v-if="selectedTeacherIds.includes(teacher.id)"
                  type="success"
                  size="small"
                >
                  已选择
                </el-tag>
                <el-tag
                  v-else-if="selectedTeacherIds.length >= 2"
                  type="info"
                  size="small"
                >
                  不可选
                </el-tag>
                <el-tag
                  v-else
                  type="primary"
                  size="small"
                >
                  可选择
                </el-tag>
              </div>
            </div>

            <div class="teacher-actions">
              <el-button
                size="small"
                @click.stop="viewTeacherDetail(teacher)"
              >
                <el-icon><View /></el-icon>
                查看详情
              </el-button>
            </div>
          </div>

          <el-empty
            v-if="!searching && teachers.length === 0"
            description="没有找到符合条件的教师"
          />
        </div>
      </el-card>

      <!-- 已选择的教师 -->
      <el-card v-if="selectedTeachers.length > 0" shadow="never" class="selected-card">
        <template #header>
          <div class="card-header">
            <span>已选择的教师</span>
          </div>
        </template>

        <div class="selected-teachers">
          <div
            v-for="(teacher, index) in selectedTeachers"
            :key="teacher.id"
            class="selected-teacher"
          >
            <div class="teacher-info">
              <el-avatar :size="40">
                {{ teacher.name?.charAt(0) || 'T' }}
              </el-avatar>
              <div class="teacher-details">
                <div class="teacher-name">{{ teacher.name }}</div>
                <div class="teacher-phone">{{ teacher.phone }}</div>
              </div>
            </div>
            <div class="priority-tag">
              <el-tag :type="index === 0 ? 'primary' : 'info'" size="small">
                {{ index === 0 ? '第一选择' : '第二选择' }}
              </el-tag>
            </div>
            <div class="teacher-actions">
              <el-button
                type="danger"
                size="small"
                text
                @click="removeTeacher(teacher.id)"
              >
                <el-icon><Delete /></el-icon>
                移除
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :disabled="selectedTeachers.length === 0"
        >
          确定选择（{{ selectedTeachers.length }}/2）
        </el-button>
      </div>
    </template>

    <!-- 教师详情对话框 -->
    <TeacherDetailDialog
      v-model="showDetailDialog"
      :teacher="selectedTeacherForDetail"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Search, Refresh, Phone, OfficeBuilding, Document, 
  Trophy, View, Delete 
} from '@element-plus/icons-vue'
import { getAvailableTeachersApi } from '@/api/management/courseBooking'
import { getTeachingGroupsApi } from '@/api/management/teachingGroup'
import TeacherDetailDialog from '@/views/management/teacher/components/TeacherDetailDialog.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  subject: {
    type: String,
    default: ''
  },
  specification: {
    type: String,
    default: ''
  },
  selectedTeachers: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'confirm'])

// Refs
const searching = ref(false)
const teachers = ref([])
const teachingGroups = ref([])
const selectedTeacherIds = ref([])
const showDetailDialog = ref(false)
const selectedTeacherForDetail = ref(null)

// Computed
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const selectedTeachers = computed(() => {
  return teachers.value.filter(teacher => 
    selectedTeacherIds.value.includes(teacher.id)
  )
})

// Form data
const searchForm = reactive({
  keyword: '',
  groupId: '',
  gender: ''
})

// Methods
function handleClose() {
  emit('update:modelValue', false)
}

function resetSearch() {
  searchForm.keyword = ''
  searchForm.groupId = ''
  searchForm.gender = ''
  searchTeachers()
}

async function searchTeachers() {
  try {
    searching.value = true

    const params = {
      subject: props.subject,
      specification: props.specification,
      keyword: searchForm.keyword,
      groupId: searchForm.groupId,
      gender: searchForm.gender
    }

    const { data } = await getAvailableTeachersApi(params)
    teachers.value = data || []

  } catch (error) {
    console.error('搜索教师失败:', error)
    ElMessage.error('搜索教师失败: ' + (error.message || '未知错误'))
  } finally {
    searching.value = false
  }
}

async function loadTeachingGroups() {
  try {
    const { data } = await getTeachingGroupsApi()
    teachingGroups.value = data || []
  } catch (error) {
    console.error('加载教学组失败:', error)
  }
}

function toggleTeacher(teacher) {
  const index = selectedTeacherIds.value.indexOf(teacher.id)
  
  if (index > -1) {
    // 已选择，移除
    selectedTeacherIds.value.splice(index, 1)
  } else {
    // 未选择，添加
    if (selectedTeacherIds.value.length >= 2) {
      ElMessage.warning('最多只能选择2位教师')
      return
    }
    selectedTeacherIds.value.push(teacher.id)
  }
}

function removeTeacher(teacherId) {
  const index = selectedTeacherIds.value.indexOf(teacherId)
  if (index > -1) {
    selectedTeacherIds.value.splice(index, 1)
  }
}

function viewTeacherDetail(teacher) {
  selectedTeacherForDetail.value = teacher
  showDetailDialog.value = true
}

function handleConfirm() {
  if (selectedTeachers.value.length === 0) {
    ElMessage.warning('请至少选择一位教师')
    return
  }

  // 转换为预约课申请需要的格式
  const teachersForBooking = selectedTeachers.value.map(teacher => ({
    teacherId: teacher.id,
    teacherName: teacher.name,
    teacherPhone: teacher.phone,
    groupName: teacher.groupName
  }))

  emit('confirm', teachersForBooking)
  handleClose()
}

// Watch for dialog visibility changes
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    // 初始化已选择的教师
    selectedTeacherIds.value = props.selectedTeachers.map(t => t.teacherId) || []
    
    // 搜索教师
    if (props.subject && props.specification) {
      searchTeachers()
    }
  }
})

// Lifecycle
onMounted(() => {
  loadTeachingGroups()
})
</script>

<style scoped>
.teacher-selector-content {
  max-height: 70vh;
  overflow-y: auto;
}

.search-card,
.teachers-card,
.selected-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.teacher-count {
  font-size: 14px;
  color: #909399;
}

.teachers-list {
  max-height: 400px;
  overflow-y: auto;
}

.teacher-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fff;
}

.teacher-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.teacher-card.selected {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.teacher-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.teacher-avatar {
  margin-right: 16px;
}

.teacher-info {
  flex: 1;
  margin-right: 16px;
}

.teacher-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.teacher-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.teacher-status {
  margin-right: 16px;
  text-align: center;
}

.current-students {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
}

.current-students .label {
  color: #909399;
}

.current-students .value {
  font-weight: 500;
  color: #303133;
}

.selected-teachers {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.selected-teacher {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.selected-teacher .teacher-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-right: 16px;
}

.selected-teacher .teacher-details {
  margin-left: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.selected-teacher .teacher-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 0;
}

.selected-teacher .teacher-phone {
  font-size: 12px;
  color: #909399;
}

.priority-tag {
  margin-right: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
