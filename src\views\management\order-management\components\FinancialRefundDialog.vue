<template>
  <el-dialog
    v-model="visible"
    title="财务退款"
    width="1000px"
    :close-on-click-modal="false"
    @close="onClose"
  >
    <div v-if="orderInfo" class="refund-content">
      <!-- 订单信息 -->
      <el-card class="order-info-card" shadow="never" style="margin-bottom: 16px;">
        <template #header>
          <span>订单信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ orderInfo.no }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="statusType(orderInfo.orderStatus)">{{ orderInfo.orderStatus }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单总金额">¥{{ (orderInfo.totalAmt / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="已支付金额">¥{{ (orderInfo.amtPaid / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="学生姓名">{{ orderInfo.studentName }}</el-descriptions-item>
          <el-descriptions-item label="销售人员">{{ orderInfo.salerName }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 购买产品信息 -->
      <el-card class="product-info-card" shadow="never" style="margin-bottom: 16px;">
        <template #header>
          <span>购买产品信息</span>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="购买课时">{{ productDetail.quantity }}</el-descriptions-item>
          <el-descriptions-item label="赠送课时">{{ productDetail.bonusHoursQuantity }}</el-descriptions-item>
          <el-descriptions-item label="单价">¥{{ (productDetail.unitPrice / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="教材费">¥{{ (productDetail.materialFee / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="原价">¥{{ (productDetail.originalPrice / 100).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="售价">¥{{ (productDetail.sellingPrice / 100).toFixed(2) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 课时退款计算信息 -->
      <el-card v-if="refundCalculation" class="calculation-card" shadow="never" style="margin-bottom: 16px;">
        <template #header>
          <span>课时退款计算</span>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="总课时">{{ refundCalculation.totalCourseHours }}</el-descriptions-item>
          <el-descriptions-item label="已释放课时">{{ refundCalculation.totalReleasedHours }}</el-descriptions-item>
          <el-descriptions-item label="剩余课时">{{ refundCalculation.remainingHours }}</el-descriptions-item>
          <el-descriptions-item label="已消费课时">{{ refundCalculation.consumedHours }}</el-descriptions-item>
          <el-descriptions-item label="可退课时">{{ refundCalculation.refundableHours }}</el-descriptions-item>
          <el-descriptions-item label="预估可退金额">
              <div>¥{{ (orderDetail.evaluateRefundAmt / 100).toFixed(2) }}</div>
              <div style="font-size: 12px; color: #7d7f83">订单金额 - 教材费 - (单价 * 已消耗课时)</div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 交易退款明细 -->
      <el-card v-if="refundCalculation" class="transaction-card" shadow="never" style="margin-bottom: 16px;">
        <template #header>
          <span>交易退款明细</span>
        </template>
        <el-table :data="refundDetails" border>
          <el-table-column prop="orderTrxId" label="支付流水" min-width="200" />
          <el-table-column label="支付金额" width="120">
            <template #default="{ row }">
              ¥{{ (row.paymentAmount / 100).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="退款金额" width="150">
            <template #default="{ row, $index }">
              <el-input-number
                  v-model="row.actualRefundAmount"
                  :min="0"
                  :max="Number((row.paymentAmount / 100).toFixed(2))"
                  :precision="2"
                  :step="0.01"
                  size="small"
              />
            </template>
          </el-table-column>
          <el-table-column prop="paymentRatio" label="支付比例" width="120">
            <template #default="{ row }">
              {{ row.paymentRatio * 100 }}%
            </template>
          </el-table-column>
          <el-table-column prop="consumedFromThisPayment" label="已消耗课时数" width="120" />
          <el-table-column prop="refundableHours" label="可退款课时数" width="120" />
        </el-table>
      </el-card>

      <!-- 退款表单 -->
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="总退款金额">
          <el-input-number
            v-model="totalRefundAmount"
            :precision="2"
            disabled
            style="width: 200px;"
          />
          <span style="margin-left: 10px; color: #909399;">元</span>
        </el-form-item>
        <el-form-item label="退款原因" prop="refundReason">
          <el-input
            v-model="form.refundReason"
            type="textarea"
            :rows="4"
            maxlength="200"
            show-word-limit
            placeholder="请输入退款原因"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onClose">取 消</el-button>
        <el-button type="primary" :loading="submitting" @click="onSubmit"
                   v-hasPermi="['refund:records:financial-refund']">
          {{ submitting ? '处理中...' : '确认退款' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {computed, ref, watch} from 'vue'
import {ElMessage, FormInstance, FormRules} from 'element-plus'
import {calculateRefundDataApi, financialRefundApi} from '@/api/management/order-manager'
import type { RefundCalculation, RefundDetail, OrderDetailExt, ProductDetailExt, OrderInfo } from "@/api/management/order-manager.types";

const props = defineProps<{ modelValue: boolean; orderInfo: OrderInfo | null }>()
const emit = defineEmits<{ (e: 'update:modelValue', v: boolean): void; (e: 'success'): void }>()

const visible = ref<boolean>(props.modelValue)
watch(
  () => props.modelValue,
  (v) => {
    visible.value = v
    if (v && props.orderInfo) {
      loadRefundCalculation()
    }
  }
)
watch(visible, (v) => emit('update:modelValue', v))

const submitting = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()
const form = ref({ refundReason: '' })
const refundCalculation = ref<RefundCalculation | null>(null)
const refundDetails = ref<RefundDetail[]>([])
const orderDetail = ref<OrderDetailExt[]>([])
const productDetail = ref<ProductDetailExt[]>([])

const rules: FormRules = {
  refundReason: [
    { required: true, message: '请输入退款原因', trigger: 'blur' },
    { min: 5, max: 200, message: '退款原因长度在5到200个字符', trigger: 'blur' }
  ]
}

const totalRefundAmount = computed(() => {
  const total = refundDetails.value.reduce((sum, item) => {
    const amount = Number(item.actualRefundAmount) || 0
    return sum + amount
  }, 0)
  return Number(total.toFixed(2))
})

const statusType = (status: string) => {
  const map: Record<string, string> = {
    '未付款': 'warning',
    '已付款': 'success',
    '已全额支付': 'success',
    '已部分支付': 'primary',
    '已取消': 'danger',
    '已退款': 'info',
    '退款待审核': 'primary'
  }
  return map[status] || 'info'
}

const loadRefundCalculation = async () => {
  if (!props.orderInfo) return
  
  try {
    loading.value = true
    const res: any = await calculateRefundDataApi(props.orderInfo.no)
    if (res.code === 200) {
      refundCalculation.value = res.data.refundCalculationResult
      // 初始化退款明细 - 确保数值类型正确
      refundDetails.value = res.data.refundCalculationResult.paymentReleaseDetails.map(item => ({
        ...item,
        actualRefundAmount: Number(((res.data.orderDetail.evaluateRefundAmt / 100) * item.paymentRatio).toFixed(2)),
      }))
      productDetail.value = res.data.productDetail
      orderDetail.value = res.data.orderDetail
    } else {
      ElMessage.error(res.msg || '获取退款计算数据失败')
    }
  } catch (error) {
    ElMessage.error('获取退款计算数据失败')
  } finally {
    loading.value = false
  }
}

const reset = () => {
  form.value.refundReason = ''
  refundCalculation.value = null
  refundDetails.value = []
  formRef.value?.resetFields()
}

const onSubmit = async () => {
  await formRef.value?.validate(async (valid) => {
    if (!valid || !props.orderInfo) return
    
    if (totalRefundAmount.value <= 0) {
      ElMessage.error('退款金额必须大于0')
      return
    }
    
    try {
      submitting.value = true
      const payload = {
        orderId: props.orderInfo.id,
        orderNo: props.orderInfo.no,
        refundReason: form.value.refundReason,
        totalRefundAmount: Math.round(totalRefundAmount.value * 100), // 转换为分
        refundDetails: refundDetails.value.map(item => ({
          orderTrxId: item.orderTrxId,
          refundAmount: Math.round((item.actualRefundAmount || 0) * 100) // 转换为分
        }))
      }
      const res: any = await financialRefundApi(props.orderInfo.id, payload)
      if (res.code === 200) {
        ElMessage.success('财务退款处理成功')
        emit('success')
        onClose()
      } else {
        ElMessage.error(res.msg || '财务退款处理失败')
      }
    } finally {
      submitting.value = false
    }
  })
}

const onClose = () => {
  visible.value = false
  reset()
}

const handleRefundAmountChange = (value: number, index: number) => {
  // 确保数值更新后触发响应式计算
  if (refundDetails.value[index]) {
    refundDetails.value[index].actualRefundAmount = Number(value) || 0
  }
}
</script>

<style scoped>
.refund-content { padding: 8px 0; }
.order-info-card, .calculation-card, .transaction-card { border-radius: 8px; }
.dialog-footer { text-align: right; }
</style>