<template>
  <el-dialog
    v-model="visible"
    title="学生详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="student-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="学生姓名">
          <span>{{ studentDetail.name }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="手机号码">
          <span>{{ studentDetail.phone }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="性别">
          <span>{{ getGenderText(studentDetail.gender) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="年级">
          <span>{{ getGradeText(studentDetail.grade) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="学校">
          <span>{{ studentDetail.school }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(studentDetail.status)">
            {{ getStatusText(studentDetail.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="销售人员">
          <span>{{ studentDetail.salesName || '未分配' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="销售组">
          <span>{{ studentDetail.salesGroupName || '-' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="总课时">
          <span>{{ studentDetail.totalHours || 0 }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="剩余课时">
          <span>{{ studentDetail.remainingHours || 0 }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          <span>{{ formatDate(studentDetail.createTime) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          <span>{{ formatDate(studentDetail.updateTime) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="学习目标" :span="2">
          <span>{{ studentDetail.learningGoals || '-' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          <span>{{ studentDetail.remarks || '-' }}</span>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 课时记录 -->
      <div class="section-title">课时记录</div>
      <el-table :data="hourRecords" stripe style="width: 100%">
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="subject" label="科目" width="100" />
        <el-table-column prop="hours" label="课时数" width="100" align="center" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.type === 'add' ? 'success' : 'danger'" size="small">
              {{ row.type === 'add' ? '增加' : '消耗' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明" show-overflow-tooltip />
        <el-table-column prop="operator" label="操作人" width="100" />
      </el-table>

      <!-- 学习记录 -->
      <div class="section-title">学习记录</div>
      <el-table :data="learningRecords" stripe style="width: 100%">
        <el-table-column prop="date" label="上课日期" width="120" />
        <el-table-column prop="subject" label="科目" width="100" />
        <el-table-column prop="teacherName" label="教师" width="100" />
        <el-table-column prop="duration" label="时长(分钟)" width="100" align="center" />
        <el-table-column prop="content" label="课程内容" show-overflow-tooltip />
        <el-table-column prop="feedback" label="课后反馈" show-overflow-tooltip />
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑学生</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getSalesStudentDetailApi as getSalesStudentDetail } from '@/api/management/salesStudent'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  studentId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'refresh', 'edit'])

const visible = ref(false)
const loading = ref(false)
const studentDetail = ref({})
const hourRecords = ref([])
const learningRecords = ref([])

// 年级选项
const GRADE_OPTIONS = [
  { label: '一年级', value: 1 },
  { label: '二年级', value: 2 },
  { label: '三年级', value: 3 },
  { label: '四年级', value: 4 },
  { label: '五年级', value: 5 },
  { label: '六年级', value: 6 },
  { label: '初一', value: 7 },
  { label: '初二', value: 8 },
  { label: '初三', value: 9 },
  { label: '高一', value: 10 },
  { label: '高二', value: 11 },
  { label: '高三', value: 12 }
]

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.studentId) {
    getStudentDetail()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 获取学生详情
const getStudentDetail = async () => {
  if (!props.studentId) return
  
  loading.value = true
  try {
    const response = await getSalesStudentDetail(props.studentId)
    studentDetail.value = response.data.student
    hourRecords.value = response.data.hourRecords || []
    learningRecords.value = response.data.learningRecords || []
  } catch (error) {
    ElMessage.error('获取学生详情失败')
  } finally {
    loading.value = false
  }
}

// 工具方法
const getGenderText = (gender) => {
  const genderMap = {
    '0': '男',
    '1': '女',
    '2': '未知'
  }
  return genderMap[gender] || '未知'
}

const getGradeText = (grade) => {
  const option = GRADE_OPTIONS.find(item => item.value === parseInt(grade))
  return option ? option.label : grade
}

const getStatusText = (status) => {
  const statusMap = {
    'active': '在读',
    'inactive': '暂停',
    'graduated': '毕业'
  }
  return statusMap[status] || status
}

const getStatusType = (status) => {
  const typeMap = {
    'active': 'success',
    'inactive': 'warning',
    'graduated': 'info'
  }
  return typeMap[status] || 'info'
}

const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

// 事件处理
const handleClose = () => {
  visible.value = false
}

const handleEdit = () => {
  emit('edit', studentDetail.value)
  handleClose()
}
</script>

<style scoped>
.student-detail {
  max-height: 600px;
  overflow-y: auto;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 20px 0 10px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.dialog-footer {
  text-align: right;
}
</style>
